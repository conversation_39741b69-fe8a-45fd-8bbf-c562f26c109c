#!/usr/bin/env python3
"""
测试意图分析功能的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import SearchIntegratedAudioHandler, ConversationTurn

async def test_intent_analysis():
    """测试意图分析功能"""
    print("🧪 开始测试意图分析功能")
    
    # 创建SearchIntegratedAudioHandler实例
    agent = SearchIntegratedAudioHandler()
    
    # 测试用例 - 特别关注上下文相关的购买意图
    test_cases = [
        # 基础测试用例
        {
            "input": "我想买水果",
            "context": [],
            "expected": "search",
            "description": "直接购买意图"
        },
        {
            "input": "你好",
            "context": [],
            "expected": "interaction", 
            "description": "问候语"
        },
        {
            "input": "唉",
            "context": [],
            "expected": "no_intent",
            "description": "纯语气词"
        },
        
        # 重点测试：上下文相关的购买意图
        {
            "input": "我昨天丢了伞",
            "context": [
                ConversationTurn("conv1", "杭州今天会下雨吗", "interaction", 0.9)
            ],
            "expected": "search",
            "description": "天气上下文中的伞相关需求 - 重点测试用例"
        },
        {
            "input": "我的坏了",
            "context": [
                ConversationTurn("conv2", "手机推荐", "search", 0.9)
            ],
            "expected": "search",
            "description": "手机搜索上下文中的替换需求"
        },
        {
            "input": "没有了",
            "context": [
                ConversationTurn("conv3", "洗发水", "search", 0.9)
            ],
            "expected": "search",
            "description": "商品搜索上下文中的补充需求"
        },
        
        # 边界测试用例
        {
            "input": "伞",
            "context": [],
            "expected": "search",
            "description": "单独的商品名称"
        },
        {
            "input": "丢了",
            "context": [],
            "expected": "search",  # 应该被识别为潜在购买需求
            "description": "状态词（可能暗示购买需求）"
        },
        {
            "input": "我昨天丢了伞",
            "context": [],
            "expected": "search",
            "description": "无上下文的隐含购买需求"
        },
        
        # 应该被正确忽略的内容
        {
            "input": "嗯嗯",
            "context": [],
            "expected": "no_intent",
            "description": "重复语气词"
        },
        {
            "input": "妈妈你过来",
            "context": [],
            "expected": "no_intent",
            "description": "对其他人说话"
        }
    ]
    
    print(f"📝 测试用例数量: {len(test_cases)}")
    print("=" * 80)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试用例 {i}: {test_case['description']}")
        print(f"   输入: '{test_case['input']}'")
        
        # 设置上下文
        if test_case['context']:
            agent.conversation_memory.conversation_history = test_case['context']
            print(f"   上下文: {[turn.user_input for turn in test_case['context']]}")
        else:
            agent.conversation_memory.conversation_history = []
            print(f"   上下文: 无")
        
        try:
            # 调用意图分析函数
            result = await agent.analyze_user_intent(test_case['input'], f"test_conv_{i}")
            
            actual_intent = result.get('intent', 'unknown')
            expected_intent = test_case['expected']
            confidence = result.get('confidence', 0)
            reason = result.get('reason', '')
            
            # 检查结果
            if actual_intent == expected_intent:
                print(f"   ✅ 成功: {actual_intent} (置信度: {confidence:.2f})")
                print(f"   理由: {reason}")
                success_count += 1
            else:
                print(f"   ❌ 失败: 期望 {expected_intent}, 实际 {actual_intent} (置信度: {confidence:.2f})")
                print(f"   理由: {reason}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print(f"🧪 测试完成")
    print(f"📊 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有测试用例通过！")
    else:
        print(f"⚠️  有 {total_count - success_count} 个测试用例失败")

if __name__ == "__main__":
    asyncio.run(test_intent_analysis())
