Metadata-Version: 2.4
Name: realtime_speech_search_agent
Version: 1.18.0.dev0
Summary: 实时语音商品搜索代理 - 基于smolagents的语音交互购物助手
Author-email: Realtime Speech Search Team <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: huggingface-hub>=0.31.2
Requires-Dist: requests>=2.32.3
Requires-Dist: rich>=13.9.4
Requires-Dist: jinja2>=3.1.4
Requires-Dist: pillow>=10.0.1
Requires-Dist: python-dotenv>=1.0.1
Requires-Dist: fastapi>=0.115.11
Requires-Dist: uvicorn>=0.34.0
Requires-Dist: starlette>=0.46.1
Requires-Dist: python-multipart>=0.0.20
Requires-Dist: sounddevice>=0.5.1
Requires-Dist: soundfile>=0.13.1
Requires-Dist: pyaudio
Requires-Dist: pydub>=0.25.1
Requires-Dist: librosa>=0.11.0
Requires-Dist: audioread>=3.0.1
Requires-Dist: websockets>=15.0.1
Requires-Dist: aiortc>=1.10.1
Requires-Dist: fastrtc>=0.0.14
Requires-Dist: aioice>=0.9.0
Requires-Dist: pylibsrtp>=0.11.0
Requires-Dist: aiohttp>=3.11.13
Requires-Dist: aiofiles>=23.2.1
Requires-Dist: aiohappyeyeballs>=2.6.1
Requires-Dist: aiohttp-retry>=2.9.1
Requires-Dist: httpx>=0.28.1
Requires-Dist: httpcore>=1.0.7
Requires-Dist: numpy>=2.1.3
Requires-Dist: pandas>=2.2.3
Requires-Dist: pydantic>=2.10.6
Requires-Dist: orjson>=3.10.15
Requires-Dist: gradio>=5.20.1
Requires-Dist: gradio_client>=1.7.2
Requires-Dist: kokoro-onnx>=0.4.5
Requires-Dist: phonemizer-fork>=3.3.1
Requires-Dist: espeakng-loader>=0.2.4
Requires-Dist: fastrand>=1.0.0
Requires-Dist: onnxruntime>=1.21.0
Requires-Dist: scikit-learn>=1.6.1
Requires-Dist: scipy>=1.15.2
Requires-Dist: numba>=0.61.0
Requires-Dist: llvmlite>=0.44.0
Requires-Dist: joblib>=1.4.2
Requires-Dist: threadpoolctl>=3.5.0
Requires-Dist: coloredlogs>=15.0.1
Requires-Dist: colorlog>=6.9.0
Requires-Dist: tqdm>=4.67.1
Requires-Dist: typer>=0.15.2
Requires-Dist: click>=8.1.8
Requires-Dist: PyYAML>=6.0.2
Requires-Dist: tomlkit>=0.13.2
Requires-Dist: python-dateutil>=2.9.0.post0
Requires-Dist: pytz>=2025.1
Requires-Dist: cryptography>=44.0.2
Requires-Dist: pyOpenSSL>=25.0.0
Requires-Dist: PyJWT>=2.10.1
Requires-Dist: certifi>=2025.1.31
Requires-Dist: twilio>=9.5.0
Requires-Dist: protobuf>=6.30.0
Requires-Dist: flatbuffers>=25.2.10
Requires-Dist: msgpack>=1.1.0
Requires-Dist: rdflib>=7.1.3
Requires-Dist: csvw>=3.5.1
Requires-Dist: language-tags>=1.2.0
Requires-Dist: isodate>=0.7.2
Requires-Dist: babel>=2.17.0
Requires-Dist: segments>=2.3.0
Requires-Dist: semantic-version>=2.10.0
Requires-Dist: shellingham>=1.5.4
Requires-Dist: uritemplate>=4.1.1
Requires-Dist: urllib3>=2.3.0
Requires-Dist: dnspython>=2.7.0
Requires-Dist: ifaddr>=0.2.0
Requires-Dist: google-crc32c>=1.6.0
Requires-Dist: pooch>=1.8.2
Requires-Dist: platformdirs>=4.3.6
Requires-Dist: lazy_loader>=0.4
Requires-Dist: soxr>=0.5.0.post1
Requires-Dist: av>=13.1.0
Requires-Dist: groovy>=0.1.2
Requires-Dist: safehttpx>=0.1.6
Requires-Dist: ffmpy>=0.5.0
Requires-Dist: jiter>=0.9.0
Requires-Dist: jsonschema>=4.23.0
Requires-Dist: jsonschema-specifications>=2024.10.1
Requires-Dist: referencing>=0.36.2
Requires-Dist: rpds-py>=0.23.1
Requires-Dist: regex>=2024.11.6
Requires-Dist: rfc3986>=1.5.0
Requires-Dist: sniffio>=1.3.1
Requires-Dist: anyio>=4.8.0
Requires-Dist: pyee>=12.1.1
Requires-Dist: openai
Requires-Dist: smolagents>=1.18.0
Provides-Extra: dev
Requires-Dist: ruff>=0.9.0; extra == "dev"
Requires-Dist: pytest>=8.1.0; extra == "dev"
Requires-Dist: pytest-asyncio; extra == "dev"
Requires-Dist: ipython>=8.31.0; extra == "dev"
Provides-Extra: search
Requires-Dist: duckduckgo-search>=6.3.7; extra == "search"
Requires-Dist: markdownify>=0.14.1; extra == "search"
Requires-Dist: rank-bm25; extra == "search"
Requires-Dist: Wikipedia-API>=0.8.1; extra == "search"
Provides-Extra: ai
Requires-Dist: torch; extra == "ai"
Requires-Dist: torchvision; extra == "ai"
Requires-Dist: transformers>=4.0.0; extra == "ai"
Requires-Dist: accelerate; extra == "ai"
Provides-Extra: all
Requires-Dist: realtime_speech_search_agent[ai,dev,search]; extra == "all"

# 实时语音商品搜索代理

基于smolagents的实时语音交互购物助手，支持语音识别、商品搜索、语音合成和智能对话。

## 功能特性

- 🎤 **实时语音识别**：支持连续语音输入和语音中断
- 🛍️ **智能商品搜索**：集成淘宝商品搜索API，支持商品详情获取
- 🔊 **语音合成**：支持多种TTS引擎，流式语音输出
- 💬 **智能对话**：基于大语言模型的自然语言交互
- 🌐 **网络搜索**：支持天气、百科等信息查询
- 📱 **Web界面**：现代化的Web UI，支持商品卡片展示
- 🎛️ **音量控制**：智能音频管理，避免回音干扰

## 系统要求

- Python 3.10+
- uv 包管理器
- 支持音频输入/输出的系统
- 网络连接（用于API调用）

## 快速开始

### 方法一：自动安装（推荐）

```bash
# Linux/macOS
bash install.sh

# Windows
install.bat
```

### 方法二：手动安装

#### 1. 安装uv包管理器

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或使用pip安装
pip install uv
```

#### 2. 克隆项目

```bash
git clone <repository-url>
cd realtime_speech_search_agent
```

#### 3. 安装依赖

```bash
# 安装所有依赖
uv sync

# ⚠️ 重要：安装修改过的openai包
uv add --editable ./thirdparty/realtime_chat/openai-python
```

#### 4. 配置环境变量

创建 `.env` 文件：

```bash
# Azure OpenAI配置（必需）
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_MODEL_NAME=gpt-4

# 运行模式配置
MODE=WEB  # 可选：WEB/UI/PHONE

# 可选配置
DEBUG=false
LOG_LEVEL=INFO
```

## 运行应用

### 方法一：快速启动（推荐）

```bash
# Linux/macOS
bash start.sh [mode]

# Windows
start.bat [mode]

# mode可选：web(默认), ui, phone
```

### 方法二：手动启动

```bash
# 直接运行（Web模式）
uv run python app.py

# 指定运行模式
MODE=UI uv run python app.py     # UI模式
MODE=PHONE uv run python app.py  # 电话模式

# 或激活虚拟环境后运行
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows
python app.py
```

### 访问应用

- **Web模式**：https://localhost:7878 （默认，推荐）
- **UI模式**：Gradio界面，适合开发调试
- **电话模式**：电话接入模式，需要Twilio配置

### 运行模式说明

| 模式 | 描述 | 适用场景 |
|------|------|----------|
| WEB | 现代Web界面，支持完整功能 | 生产环境，用户使用 |
| UI | Gradio界面，简单易用 | 开发调试，快速测试 |
| PHONE | 电话接入模式 | 电话客服，语音热线 |

## 使用说明

### 语音交互

1. 打开Web界面
2. 点击麦克风按钮开始语音输入
3. 说出您的需求，例如：
   - "帮我搜索苹果手机"
   - "今天北京的天气怎么样"
   - "介绍一下这个商品"

### 商品搜索

- 支持自然语言商品搜索
- 自动显示商品卡片和详细信息
- 支持批量浏览和语音控制导航
- 语音命令：
  - "下一个" / "上一个"：切换商品
  - "换一批"：切换商品批次
  - "详细介绍"：获取商品详情

### 智能对话

- 支持多轮对话和上下文理解
- 自动意图识别（搜索/交互/网络查询）
- 智能查询重写和澄清提问
- 情感化回复和购物引导

## 项目结构

```
realtime_speech_search_agent/
├── app.py                          # 主应用文件
├── pyproject.toml                  # 项目配置和依赖
├── README.md                       # 项目说明
├── cert.pem / key.pem             # SSL证书（HTTPS需要）
├── image_cache/                    # 图片缓存目录
├── prompts/                        # 提示词配置
├── thirdparty/                     # 第三方模块
│   ├── realtime_chat/             # 实时语音聊天模块
│   │   ├── openai-python/         # 修改过的OpenAI Python SDK
│   │   ├── chat.py                # 聊天处理
│   │   ├── tts8.py                # TTS引擎
│   │   └── index.html             # Web界面
│   └── tb_speech_search/          # 淘宝搜索模块
│       ├── custom_tools.py        # 自定义工具
│       └── search_agent.py        # 搜索代理
└── doc/                           # 文档目录
```

## 开发说明

### 依赖管理

项目使用uv进行依赖管理，主要依赖包括：

- **Web框架**：FastAPI + Uvicorn
- **语音处理**：sounddevice, soundfile, librosa
- **实时通信**：websockets, aiortc, fastrtc
- **AI模型**：修改过的openai包（支持实时API）
- **UI框架**：Gradio
- **数据处理**：numpy, pandas, pydantic

### 修改过的OpenAI包

项目包含一个修改过的OpenAI Python SDK，位于 `thirdparty/realtime_chat/openai-python/`，主要增强：

- 实时语音API支持
- 流式音频处理
- WebSocket连接优化
- 语音中断处理

### 添加新功能

1. 在相应模块中添加功能代码
2. 更新依赖（如需要）：`uv add package_name`
3. 更新文档和测试
4. 提交代码

## 验证安装

安装完成后，运行验证脚本检查环境：

```bash
# 运行完整验证
uv run python verify_installation.py

# 或使用快速验证
uv run python -c "import fastapi, gradio, sounddevice; print('✅ 核心依赖正常')"
```

## 故障排除

如遇到问题，请查看详细的故障排除指南：

📚 **[完整故障排除指南](TROUBLESHOOTING.md)**

### 快速解决方案

1. **依赖问题**: `uv sync --reinstall`
2. **OpenAI包问题**: `uv add --editable ./thirdparty/realtime_chat/openai-python`
3. **音频问题**: 检查设备权限和驱动
4. **SSL问题**: 生成证书或使用HTTP模式
5. **端口占用**: 更改端口或终止占用进程

### 获取帮助

- 运行 `uv run python verify_installation.py` 诊断问题
- 查看应用日志获取详细错误信息
- 参考 [TROUBLESHOOTING.md](TROUBLESHOOTING.md) 获取解决方案

## 许可证

本项目基于Apache 2.0许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
