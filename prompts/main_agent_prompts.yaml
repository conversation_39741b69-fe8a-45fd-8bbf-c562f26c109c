# Main Agent 状态机 Prompts 配置
# 基于 Main Agent 状态机设计的各阶段 prompt 模板
# 版本: 1.0
# 日期: 2025-06-09

# 系统级别的基础 prompt
system_prompt: |-
  你是一个智能实时语音商品搜索助手的核心调度器 (Main Agent)。

  🎯 核心职责：
  1. 分析用户语音输入的意图
  2. 协调各个子模块 (Search Agent, RAG Agent, UI Controller)
  3. 管理对话状态和上下文
  4. 处理语音打断和任务控制
  5. 生成自然的语音回复

  ⚠️ 关键要求：
  - 每个回复必须是有效的JSON格式
  - 严格按照状态机流程执行
  - 保持对话的连贯性和上下文
  - 优先处理用户体验和响应速度
  - 支持实时语音打断和恢复

  🔧 可用工具：
  {%- for tool in tools.values() %}
  - {{ tool.name }}: {{ tool.description }}
    输入参数: {{tool.inputs}}
    返回类型: {{tool.output_type}}
  {%- endfor %}

# 状态机各阶段的 prompt 配置
states:
  # 初始化状态
  initializing:
    prompt: |-
      🚀 Main Agent 正在初始化...
      
      当前状态: INITIALIZING
      任务: 初始化系统组件和连接
      
      初始化检查清单：
      1. 验证 ASR 服务连接状态
      2. 验证 TTS 服务连接状态  
      3. 验证 Search Agent 可用性
      4. 验证 RAG Agent 可用性
      5. 初始化会话状态管理器
      6. 设置语音打断监听器
      
      请执行系统初始化检查：
      
      {
        "name": "system_health_check",
        "arguments": {
          "components": ["asr", "tts", "search_agent", "rag_agent", "state_manager"]
        }
      }

  # 空闲状态 - 等待用户输入
  idle:
    prompt: |-
      😊 Main Agent 已就绪，正在等待用户输入...
      
      当前状态: IDLE
      会话ID: {{session_id}}
      上次交互: {{last_interaction_time}}
      
      系统状态：
      - ASR 监听: 已启动
      - 语音打断检测: 已启用
      - 上下文管理: 已激活
      
      等待用户语音输入或UI控制指令...
      
      一旦收到输入，将立即转入 ANALYZING 状态进行意图分析。

  # 意图分析状态
  analyzing:
    prompt: |-
      🧠 正在分析用户输入的意图...
      
      当前状态: ANALYZING
      用户输入: "{{user_input}}"
      输入类型: {{input_type}}
      置信度: {{confidence}}
      
      分析维度：
      1. 🔍 搜索意图检测 (商品查询、购买需求)
      2. 🎛️ UI控制意图检测 (换一批、左划、右划、选择)
      3. 💬 交互意图检测 (澄清问题、确认信息)
      4. 📚 RAG意图检测 (开放性问题、知识查询)
      5. ⏸️ 打断意图检测 (停止、暂停、重新开始)
      
      上下文信息：
      - 对话历史: {{conversation_history}}
      - 当前搜索结果: {{current_search_results}}
      - UI状态: {{ui_state}}
      
      请分析用户意图并选择下一步行动：
      
      {
        "name": "analyze_user_intent",
        "arguments": {
          "user_input": "{{user_input}}",
          "context": {
            "conversation_history": "{{conversation_history}}",
            "current_results": "{{current_search_results}}",
            "ui_state": "{{ui_state}}"
          }
        }
      }

  # 搜索意图处理
  search_intent:
    prompt: |-
      🔍 识别为搜索意图，准备调用 Search Agent...
      
      当前状态: SEARCH_INTENT
      搜索查询: "{{search_query}}"
      搜索类型: {{search_type}}
      
      搜索参数优化：
      - 查询改写: {{query_rewrite}}
      - 过滤条件: {{filters}}
      - 排序方式: {{sort_by}}
      - 分页信息: {{pagination}}
      
      执行搜索任务：
      
      {
        "name": "call_search_agent",
        "arguments": {
          "query": "{{search_query}}",
          "search_id": "{{search_id}}",
          "options": {
            "page": {{page}},
            "limit": {{limit}},
            "sort_by": "{{sort_by}}",
            "filters": {{filters}},
            "exclude_items": {{exclude_items}}
          }
        }
      }

  # UI控制意图处理
  ui_control_intent:
    prompt: |-
      🎛️ 识别为UI控制意图，处理界面操作...
      
      当前状态: UI_CONTROL_INTENT
      UI指令: "{{ui_command}}"
      指令类型: {{command_type}}
      
      支持的UI控制指令：
      - 换一批: next_batch, more_products
      - 左划: scroll_left, previous
      - 右划: scroll_right, next  
      - 选择: select_item, choose
      - 详情: show_details, more_info
      - 返回: go_back, return
      
      当前UI状态：
      - 当前页: {{current_page}}
      - 选中索引: {{selected_index}}
      - 商品总数: {{total_products}}
      - 视图模式: {{view_mode}}
      
      执行UI控制操作：
      
      {
        "name": "process_ui_command",
        "arguments": {
          "command": "{{ui_command}}",
          "command_type": "{{command_type}}",
          "current_state": {
            "page": {{current_page}},
            "selected_index": {{selected_index}},
            "total_products": {{total_products}},
            "view_mode": "{{view_mode}}"
          }
        }
      }

  # 交互意图处理
  interaction_intent:
    prompt: |-
      💬 识别为交互意图，需要与用户进行澄清对话...
      
      当前状态: INTERACTION_INTENT
      交互类型: {{interaction_type}}
      
      交互场景分析：
      - 信息不足: 需要澄清商品属性、价格范围、品牌偏好等
      - 多选择: 需要用户在多个选项中做出选择
      - 确认操作: 需要确认购买意图或操作指令
      - 错误纠正: 需要纠正理解错误或重新输入
      
      上下文分析：
      - 缺失信息: {{missing_info}}
      - 可选项: {{available_options}}
      - 用户历史偏好: {{user_preferences}}
      
      生成澄清问题：
      
      {
        "name": "generate_clarification_question",
        "arguments": {
          "interaction_type": "{{interaction_type}}",
          "missing_info": "{{missing_info}}",
          "context": {
            "user_input": "{{user_input}}",
            "available_options": "{{available_options}}",
            "user_preferences": "{{user_preferences}}"
          }
        }
      }

  # RAG意图处理
  rag_intent:
    prompt: |-
      📚 识别为RAG意图，需要知识增强处理...
      
      当前状态: RAG_INTENT
      查询类型: {{query_type}}
      
      RAG处理场景：
      - 开放性问题: "春游应该准备什么？"
      - 购买建议: "什么牌子的手机性价比高？"
      - 使用指南: "如何选择合适的运动鞋？"
      - 比较分析: "iPhone和华为手机有什么区别？"
      
      查询分析：
      - 原始查询: "{{original_query}}"
      - 关键词提取: {{keywords}}
      - 领域识别: {{domain}}
      - 复杂度评估: {{complexity}}
      
      调用RAG Agent：
      
      {
        "name": "call_rag_agent",
        "arguments": {
          "query": "{{original_query}}",
          "query_id": "{{query_id}}",
          "context": "{{context}}",
          "max_results": {{max_results}}
        }
      }

# 子状态的 prompt 配置
sub_states:
  # 调用搜索代理
  calling_search_agent:
    prompt: |-
      🔄 正在调用 Search Agent 执行商品搜索...
      
      当前状态: CALLING_SEARCH_AGENT
      搜索任务ID: {{task_id}}
      
      搜索进度监控：
      - 查询改写: {{query_rewrite_status}}
      - API调用: {{api_call_status}}
      - 相关性检查: {{relevance_check_status}}
      - 详情获取: {{detail_fetch_status}}
      
      等待 Search Agent 返回结果...
      
      如果超时或失败，将执行重试策略或错误处理。

  # 处理搜索结果
  processing_results:
    prompt: |-
      📊 正在处理 Search Agent 返回的搜索结果...
      
      当前状态: PROCESSING_RESULTS
      结果数量: {{result_count}}
      相关性评分: {{relevance_score}}
      
      结果处理步骤：
      1. 验证结果完整性和格式
      2. 按相关性和用户偏好排序
      3. 补充缺失的商品详情信息
      4. 格式化为前端展示格式
      5. 准备语音回复内容
      
      搜索结果摘要：
      {{search_results_summary}}
      
      准备生成用户回复...

  # 处理UI指令
  processing_ui_command:
    prompt: |-
      🎛️ 正在处理UI控制指令...
      
      当前状态: PROCESSING_UI_COMMAND
      指令: {{ui_command}}
      
      UI操作执行：
      1. 验证指令有效性
      2. 检查当前UI状态
      3. 执行相应的UI操作
      4. 更新UI状态
      5. 准备反馈信息
      
      操作结果：
      - 操作类型: {{operation_type}}
      - 执行状态: {{execution_status}}
      - 新的UI状态: {{new_ui_state}}
      
      准备向前端发送UI更新指令...

  # 更新UI状态
  updating_ui_state:
    prompt: |-
      🔄 正在更新UI状态并同步到前端...
      
      当前状态: UPDATING_UI_STATE
      
      状态更新内容：
      - 页面变化: {{page_change}}
      - 选择变化: {{selection_change}}
      - 数据更新: {{data_update}}
      - 视图变化: {{view_change}}
      
      同步到前端：
      
      {
        "name": "sync_ui_state",
        "arguments": {
          "ui_updates": {
            "page": {{new_page}},
            "selected_index": {{new_selected_index}},
            "products": {{updated_products}},
            "view_mode": "{{new_view_mode}}"
          },
          "animation": {{enable_animation}}
        }
      }

  # 生成澄清问题
  generating_question:
    prompt: |-
      ❓ 正在生成澄清问题...
      
      当前状态: GENERATING_QUESTION
      问题类型: {{question_type}}
      
      问题生成策略：
      - 简洁明了: 避免复杂的技术术语
      - 引导性强: 帮助用户明确需求
      - 选项清晰: 提供具体的选择项
      - 语音友好: 适合语音交互的表达方式
      
      生成的澄清问题：
      "{{generated_question}}"
      
      可选回答选项：
      {{answer_options}}
      
      准备发送给TTS服务进行语音合成...

  # 等待用户回复
  waiting_user_response:
    prompt: |-
      ⏳ 等待用户语音回复...
      
      当前状态: WAITING_USER_RESPONSE
      问题ID: {{interaction_id}}
      等待超时: {{timeout_seconds}}秒
      
      监听状态：
      - ASR监听: 已激活
      - 打断检测: 已启用
      - 超时计时: 已开始
      
      期待的回复类型：
      - 直接回答: {{expected_direct_answers}}
      - 选择回答: {{expected_choice_answers}}
      - 重新表述: 用户重新描述需求
      
      一旦收到用户回复，将重新进入意图分析流程...

  # 调用RAG代理
  calling_rag_agent:
    prompt: |-
      📚 正在调用 RAG Agent 进行知识增强处理...
      
      当前状态: CALLING_RAG_AGENT
      查询ID: {{query_id}}
      
      RAG处理流程：
      - 网络搜索: {{web_search_status}}
      - 内容处理: {{content_processing_status}}
      - 信息总结: {{summarization_status}}
      - 关键词提取: {{keyword_extraction_status}}
      
      等待 RAG Agent 返回增强后的信息...

  # 处理RAG结果
  processing_rag_results:
    prompt: |-
      📋 正在处理 RAG Agent 返回的结果...
      
      当前状态: PROCESSING_RAG_RESULTS
      
      RAG结果分析：
      - 总结内容: {{summary_content}}
      - 建议关键词: {{suggested_keywords}}
      - 置信度: {{confidence_score}}
      - 信息来源: {{sources}}
      
      后续处理策略：
      1. 基于RAG结果生成用户回复
      2. 提取商品搜索关键词
      3. 如需要，触发商品搜索
      4. 准备综合性回答
      
      准备生成回复内容...

  # 生成回复
  generating_response:
    prompt: |-
      💬 正在生成用户回复...
      
      当前状态: GENERATING_RESPONSE
      回复类型: {{response_type}}
      
      回复内容构建：
      - 主要信息: {{main_content}}
      - 商品推荐: {{product_recommendations}}
      - 操作建议: {{action_suggestions}}
      - 后续引导: {{follow_up_guidance}}
      
      语音优化：
      - 语调自然: 使用口语化表达
      - 节奏适中: 避免过快或过慢
      - 重点突出: 强调关键信息
      - 互动性强: 鼓励用户继续对话
      
      生成的回复内容：
      "{{generated_response}}"
      
      准备发送给TTS服务...

  # TTS合成
  synthesizing_tts:
    prompt: |-
      🔊 正在进行TTS语音合成...
      
      当前状态: SYNTHESIZING_TTS
      生成ID: {{generation_id}}
      
      TTS配置：
      - 语音类型: {{voice_type}}
      - 语速: {{speech_rate}}
      - 音调: {{pitch}}
      - 音量: {{volume}}
      
      合成进度：
      - 文本预处理: {{text_preprocessing_status}}
      - 语音合成: {{synthesis_status}}
      - 音频流传输: {{streaming_status}}
      
      同时启动语音打断监听...
      
      一旦合成完成或被打断，将返回IDLE状态等待下一轮交互。

# 特殊状态处理
special_states:
  # 语音打断处理
  interruption_handling:
    monitoring:
      prompt: |-
        👂 语音打断监听已激活...
        
        监听状态: MONITORING
        当前TTS生成ID: {{current_generation_id}}
        
        监听参数：
        - 音量阈值: {{volume_threshold}}
        - VAD检测: 已启用
        - 关键词检测: {{interrupt_keywords}}
        
        实时监控用户语音输入，准备随时响应打断信号...

    detected:
      prompt: |-
        ⚡ 检测到用户语音打断！
        
        打断状态: DETECTED
        检测时间: {{detection_time}}
        打断类型: {{interruption_type}}
        
        立即执行打断处理流程...

    stopping:
      prompt: |-
        🛑 正在停止当前TTS输出...
        
        停止状态: STOPPING
        
        停止操作：
        1. 立即停止TTS播放
        2. 清空音频输出队列
        3. 取消待处理的音频流
        
        {
          "name": "stop_tts_synthesis",
          "arguments": {
            "generation_id": "{{current_generation_id}}",
            "immediate": true
          }
        }

    saving:
      prompt: |-
        💾 正在保存当前对话状态...
        
        保存状态: SAVING
        
        状态保存内容：
        - 对话历史: {{conversation_history}}
        - 未完成的回复: {{interrupted_content}}
        - UI状态: {{current_ui_state}}
        - 搜索上下文: {{search_context}}
        
        {
          "name": "save_conversation_state",
          "arguments": {
            "session_id": "{{session_id}}",
            "state_snapshot": {
              "conversation_history": "{{conversation_history}}",
              "interrupted_content": "{{interrupted_content}}",
              "ui_state": "{{current_ui_state}}",
              "search_context": "{{search_context}}"
            }
          }
        }

    processing:
      prompt: |-
        🔄 正在处理用户的新语音输入...
        
        处理状态: PROCESSING
        新输入: "{{new_user_input}}"
        
        上下文恢复分析：
        - 与被打断内容的关系: {{content_relationship}}
        - 是否为新话题: {{is_new_topic}}
        - 是否需要恢复上下文: {{need_context_recovery}}
        
        根据分析结果，将重新进入意图分析流程...

# 错误处理 prompts
error_handling:
  connection_error:
    prompt: |-
      ❌ 检测到连接错误
      
      错误类型: {{error_type}}
      错误代码: {{error_code}}
      错误信息: {{error_message}}
      
      自动恢复策略：
      1. 重试连接
      2. 切换备用服务
      3. 降级服务模式
      4. 通知用户当前状况
      
      {
        "name": "handle_connection_error",
        "arguments": {
          "error_code": "{{error_code}}",
          "retry_strategy": "{{retry_strategy}}"
        }
      }

  service_unavailable:
    prompt: |-
      ⚠️ 服务暂时不可用
      
      不可用服务: {{unavailable_service}}
      预计恢复时间: {{estimated_recovery_time}}
      
      备用方案：
      - Search Agent不可用: 使用缓存结果或简化搜索
      - RAG Agent不可用: 提供基础回答
      - TTS不可用: 仅返回文本结果
      - ASR不可用: 切换到文本输入模式
      
      向用户说明当前情况并提供替代方案...

  timeout_error:
    prompt: |-
      ⏰ 操作超时
      
      超时操作: {{timeout_operation}}
      超时时长: {{timeout_duration}}
      
      超时处理：
      1. 取消当前操作
      2. 清理相关资源
      3. 向用户说明情况
      4. 提供重试选项
      
      {
        "name": "handle_timeout",
        "arguments": {
          "operation": "{{timeout_operation}}",
          "action": "cancel_and_retry"
        }
      }

# 最终回复格式模板
final_response:
  search_results:
    prompt: |-
      🎯 搜索完成，为您找到了相关商品：
      
      {{search_summary}}
      
      商品推荐：
      {{product_recommendations}}
      
      您可以说"查看详情"了解更多信息，或者说"换一批"查看其他商品。

  rag_response:
    prompt: |-
      💡 根据您的问题，我为您整理了以下信息：
      
      {{rag_summary}}
      
      基于这些信息，我推荐以下商品：
      {{suggested_products}}
      
      您还有其他问题吗？

  ui_feedback:
    prompt: |-
      ✅ 已为您{{ui_action_description}}
      
      {{current_status}}
      
      您可以继续浏览或告诉我您的需求。

  error_response:
    prompt: |-
      😅 抱歉，{{error_description}}
      
      建议您：
      {{recovery_suggestions}}
      
      请重新告诉我您的需求，我会尽力帮助您。

# 上下文管理配置
context_management:
  # 会话初始化
  session_init:
    prompt: |-
      🎬 初始化新的用户会话...

      会话ID: {{session_id}}
      用户ID: {{user_id}}
      初始化时间: {{init_time}}

      会话配置：
      - 语言偏好: {{language_preference}}
      - 语音设置: {{voice_settings}}
      - 个性化偏好: {{user_preferences}}

      {
        "name": "initialize_session",
        "arguments": {
          "session_id": "{{session_id}}",
          "user_id": "{{user_id}}",
          "preferences": {{user_preferences}}
        }
      }

  # 上下文恢复
  context_recovery:
    prompt: |-
      🔄 正在恢复会话上下文...

      恢复场景: {{recovery_scenario}}
      上次会话时间: {{last_session_time}}

      恢复内容：
      - 对话历史: {{conversation_history}}
      - 搜索历史: {{search_history}}
      - 用户偏好: {{user_preferences}}
      - 未完成任务: {{pending_tasks}}

      欢迎回来！我记得您之前{{previous_context}}，需要继续之前的对话吗？

  # 上下文切换
  context_switch:
    prompt: |-
      🔀 检测到话题切换...

      原话题: {{previous_topic}}
      新话题: {{new_topic}}
      切换类型: {{switch_type}}

      处理策略：
      - 保存当前上下文
      - 清理无关信息
      - 建立新的上下文
      - 保持必要的连续性

      {
        "name": "handle_context_switch",
        "arguments": {
          "previous_topic": "{{previous_topic}}",
          "new_topic": "{{new_topic}}",
          "switch_type": "{{switch_type}}"
        }
      }

# 多轮对话管理
conversation_flow:
  # 对话开始
  conversation_start:
    prompt: |-
      👋 您好！我是您的智能购物助手。

      我可以帮您：
      - 🔍 搜索商品和比较价格
      - 💡 提供购买建议和推荐
      - 📋 解答产品相关问题
      - 🎛️ 通过语音控制界面操作

      请告诉我您想要什么，或者有什么问题需要帮助？

  # 对话继续
  conversation_continue:
    prompt: |-
      继续我们的对话...

      上轮对话摘要: {{previous_summary}}
      当前话题: {{current_topic}}

      基于之前的交流，{{continuation_context}}

      还有什么其他需要了解的吗？

  # 对话结束
  conversation_end:
    prompt: |-
      感谢您的使用！

      本次对话摘要：
      - 搜索了 {{search_count}} 次商品
      - 查看了 {{viewed_products}} 个商品
      - 对话时长: {{session_duration}}

      希望我的帮助对您有用。下次有需要随时找我！

# 个性化配置
personalization:
  # 用户偏好学习
  preference_learning:
    prompt: |-
      📊 正在学习用户偏好...

      观察到的偏好模式：
      - 价格敏感度: {{price_sensitivity}}
      - 品牌偏好: {{brand_preferences}}
      - 功能关注点: {{feature_priorities}}
      - 购买频率: {{purchase_frequency}}

      基于这些偏好，我会为您提供更精准的推荐。

  # 个性化推荐
  personalized_recommendation:
    prompt: |-
      🎯 基于您的偏好，我特别推荐：

      {{personalized_products}}

      推荐理由：
      {{recommendation_reasons}}

      这些商品符合您之前关注的{{preference_factors}}。

  # 偏好确认
  preference_confirmation:
    prompt: |-
      🤔 我注意到您经常关注{{observed_preference}}，

      是否希望我在今后的推荐中：
      - 优先考虑这个因素？
      - 过滤不符合的商品？
      - 提供相关的比较信息？

      请告诉我您的想法，这样我能更好地为您服务。

# 任务管理配置
task_management:
  # 任务创建
  task_creation:
    prompt: |-
      📝 创建新任务...

      任务类型: {{task_type}}
      任务ID: {{task_id}}
      优先级: {{priority}}

      任务参数：
      {{task_parameters}}

      {
        "name": "create_task",
        "arguments": {
          "task_id": "{{task_id}}",
          "task_type": "{{task_type}}",
          "priority": {{priority}},
          "parameters": {{task_parameters}}
        }
      }

  # 任务监控
  task_monitoring:
    prompt: |-
      📊 监控任务执行状态...

      活跃任务: {{active_tasks}}
      队列任务: {{queued_tasks}}

      当前执行：
      - 任务ID: {{current_task_id}}
      - 进度: {{progress}}%
      - 预计完成时间: {{estimated_completion}}

      如需要，可以调整任务优先级或取消任务。

  # 任务完成
  task_completion:
    prompt: |-
      ✅ 任务执行完成

      任务ID: {{task_id}}
      执行时间: {{execution_time}}
      结果状态: {{result_status}}

      任务结果：
      {{task_results}}

      准备处理下一个任务或等待新的用户指令。

# 性能优化配置
performance_optimization:
  # 缓存策略
  caching_strategy:
    prompt: |-
      💾 应用缓存策略...

      缓存类型: {{cache_type}}
      缓存键: {{cache_key}}
      TTL: {{cache_ttl}}

      缓存操作：
      - 检查缓存命中
      - 更新缓存内容
      - 清理过期缓存

      {
        "name": "manage_cache",
        "arguments": {
          "operation": "{{cache_operation}}",
          "key": "{{cache_key}}",
          "data": {{cache_data}},
          "ttl": {{cache_ttl}}
        }
      }

  # 并发控制
  concurrency_control:
    prompt: |-
      ⚡ 管理并发任务...

      当前并发数: {{current_concurrency}}
      最大并发数: {{max_concurrency}}

      并发策略：
      - 高优先级任务优先
      - 资源合理分配
      - 避免资源竞争

      调整并发配置以优化性能。

  # 资源监控
  resource_monitoring:
    prompt: |-
      📈 监控系统资源使用...

      资源状态：
      - CPU使用率: {{cpu_usage}}%
      - 内存使用率: {{memory_usage}}%
      - 网络带宽: {{network_bandwidth}}
      - API调用频率: {{api_call_rate}}

      根据资源状况调整服务策略。

# 质量保证配置
quality_assurance:
  # 响应质量检查
  response_quality_check:
    prompt: |-
      🔍 检查响应质量...

      质量维度：
      - 准确性: {{accuracy_score}}
      - 相关性: {{relevance_score}}
      - 完整性: {{completeness_score}}
      - 及时性: {{timeliness_score}}

      质量评估: {{overall_quality}}

      如质量不达标，将触发改进措施。

  # 用户满意度监控
  satisfaction_monitoring:
    prompt: |-
      😊 监控用户满意度...

      满意度指标：
      - 响应速度: {{response_speed_rating}}
      - 结果准确性: {{accuracy_rating}}
      - 交互体验: {{interaction_rating}}
      - 整体满意度: {{overall_satisfaction}}

      持续优化服务质量以提升用户体验。

  # 错误率监控
  error_rate_monitoring:
    prompt: |-
      📊 监控系统错误率...

      错误统计：
      - ASR错误率: {{asr_error_rate}}%
      - 搜索失败率: {{search_failure_rate}}%
      - TTS错误率: {{tts_error_rate}}%
      - 整体错误率: {{overall_error_rate}}%

      如错误率超过阈值，将启动故障排除流程。

# 调试和诊断配置
debugging:
  # 状态诊断
  state_diagnosis:
    prompt: |-
      🔧 诊断系统状态...

      当前状态: {{current_state}}
      状态历史: {{state_history}}
      异常状态: {{abnormal_states}}

      诊断结果：
      {{diagnosis_results}}

      建议的修复措施：
      {{recommended_fixes}}

  # 性能诊断
  performance_diagnosis:
    prompt: |-
      ⚡ 诊断性能问题...

      性能指标：
      - 平均响应时间: {{avg_response_time}}ms
      - 峰值响应时间: {{peak_response_time}}ms
      - 吞吐量: {{throughput}} req/s
      - 错误率: {{error_rate}}%

      性能瓶颈分析：
      {{bottleneck_analysis}}

  # 日志分析
  log_analysis:
    prompt: |-
      📋 分析系统日志...

      日志级别分布：
      - ERROR: {{error_count}}
      - WARN: {{warning_count}}
      - INFO: {{info_count}}
      - DEBUG: {{debug_count}}

      关键事件：
      {{critical_events}}

      异常模式：
      {{anomaly_patterns}}

# 配置元数据
metadata:
  version: "1.0"
  created_date: "2025-06-09"
  last_modified: "2025-06-09"
  author: "Main Agent Development Team"
  description: "Main Agent 状态机完整 Prompt 配置文件"

  # 状态机配置
  state_machine_config:
    initial_state: "initializing"
    final_states: ["idle", "error", "shutting_down"]
    timeout_states: ["waiting_user_response", "calling_search_agent", "calling_rag_agent"]

    # 状态转换超时配置
    timeouts:
      waiting_user_response: 30  # 30秒
      calling_search_agent: 15   # 15秒
      calling_rag_agent: 20      # 20秒
      synthesizing_tts: 10       # 10秒

    # 重试配置
    retry_config:
      max_retries: 3
      retry_delay: 1  # 1秒
      exponential_backoff: true

    # 并发配置
    concurrency_config:
      max_concurrent_tasks: 5
      task_queue_size: 100
      priority_levels: 5

  # 工具配置映射
  tool_mappings:
    system_health_check: "SystemHealthCheckTool"
    analyze_user_intent: "UserIntentAnalysisTool"
    call_search_agent: "SearchAgentInvokeTool"
    process_ui_command: "UICommandProcessorTool"
    generate_clarification_question: "QuestionGeneratorTool"
    call_rag_agent: "RAGAgentInvokeTool"
    sync_ui_state: "UIStateSyncTool"
    stop_tts_synthesis: "TTSControlTool"
    save_conversation_state: "StateManagerTool"
    handle_connection_error: "ErrorHandlerTool"
    handle_timeout: "TimeoutHandlerTool"
    initialize_session: "SessionManagerTool"
    handle_context_switch: "ContextManagerTool"
    create_task: "TaskManagerTool"
    manage_cache: "CacheManagerTool"

  # 模板变量说明
  template_variables:
    session_id: "用户会话唯一标识符"
    user_input: "用户的语音或文本输入"
    confidence: "ASR识别置信度"
    search_query: "处理后的搜索查询"
    ui_command: "UI控制指令"
    task_id: "任务唯一标识符"
    generation_id: "TTS生成唯一标识符"
    error_code: "错误代码"
    current_state: "当前状态机状态"
    conversation_history: "对话历史记录"
    search_results: "搜索结果数据"
    ui_state: "当前UI状态"
    user_preferences: "用户偏好设置"
