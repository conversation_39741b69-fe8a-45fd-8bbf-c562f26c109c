#!/usr/bin/env python3
"""
状态消息优化演示脚本

展示优化前后状态消息输出时机的对比，说明如何充分利用等待时间提升用户体验。
"""

import asyncio
import time
import random

class StatusMessageDemo:
    """状态消息优化演示"""
    
    def __init__(self):
        self.demo_results = []
    
    async def simulate_llm_call(self, duration=2.0):
        """模拟LLM调用耗时"""
        await asyncio.sleep(duration)
        return f"这是模拟的LLM回复内容"
    
    async def simulate_search_call(self, duration=1.5):
        """模拟搜索API调用耗时"""
        await asyncio.sleep(duration)
        return [
            {"title": "商品1", "price": "199元"},
            {"title": "商品2", "price": "299元"},
            {"title": "商品3", "price": "399元"}
        ]
    
    async def simulate_tts_output(self, text, delay=0.1):
        """模拟TTS输出"""
        await asyncio.sleep(delay)
        print(f"🔊 TTS播放: {text}")
    
    async def demo_old_approach(self, query):
        """演示优化前的方法 - 等待所有处理完成后才输出状态消息"""
        print(f"\n❌ 优化前的方法 - 查询: '{query}'")
        start_time = time.time()
        
        print("  🤔 用户说话完毕，开始处理...")
        
        # 1. 先进行所有处理
        print("  ⏳ 执行query改写...")
        rewritten_query = await self.simulate_llm_call(1.0)
        
        print("  ⏳ 执行商品搜索...")
        search_results = await self.simulate_search_call(1.5)
        
        print("  ⏳ 生成回复...")
        response = await self.simulate_llm_call(1.0)
        
        # 2. 所有处理完成后才输出状态消息和结果
        processing_time = time.time() - start_time
        print(f"  ⚡ 现在才输出状态消息: '正在为您搜索「{query}」相关商品...' (已等待 {processing_time:.1f}s)")
        await self.simulate_tts_output(f"正在为您搜索「{query}」相关商品...")
        
        await self.simulate_tts_output(f"找到{len(search_results)}个相关商品")
        
        total_time = time.time() - start_time
        print(f"  🕒 总耗时: {total_time:.1f}s")
        print(f"  😞 用户体验: 用户需要等待 {processing_time:.1f}s 才听到任何反馈")
        
        return {
            'approach': 'old',
            'query': query,
            'total_time': total_time,
            'first_feedback_delay': processing_time,
            'user_experience': 'poor'
        }
    
    async def demo_new_approach(self, query):
        """演示优化后的方法 - 立即输出状态消息，并行处理"""
        print(f"\n✅ 优化后的方法 - 查询: '{query}'")
        start_time = time.time()
        
        print("  🤔 用户说话完毕，开始处理...")
        
        # 1. 立即输出状态消息
        first_feedback_time = time.time()
        first_feedback_delay = first_feedback_time - start_time
        print(f"  ⚡ 立即输出状态消息: '正在为您搜索「{query}」相关商品...' (延迟仅 {first_feedback_delay:.3f}s)")
        await self.simulate_tts_output(f"正在为您搜索「{query}」相关商品...")
        
        # 2. 在状态消息播放的同时，并行执行处理
        print("  🔄 在TTS播放的同时并行处理...")
        
        # 并行执行query改写
        print("  ⏳ 并行执行query改写...")
        rewrite_task = asyncio.create_task(self.simulate_llm_call(1.0))
        
        # 等待改写完成
        rewritten_query = await rewrite_task
        
        # 如果query被改写，立即播报
        if random.choice([True, False]):  # 模拟50%概率改写
            rewrite_time = time.time() - start_time
            print(f"  ⚡ 立即播报改写结果 (耗时 {rewrite_time:.1f}s)")
            await self.simulate_tts_output(f"我理解您要搜索「{query} 手机」，正在为您查找相关商品...")
        
        # 并行执行搜索
        print("  ⏳ 并行执行商品搜索...")
        search_task = asyncio.create_task(self.simulate_search_call(1.5))
        
        # 并行生成回复
        print("  ⏳ 并行生成回复...")
        response_task = asyncio.create_task(self.simulate_llm_call(1.0))
        
        # 等待搜索完成
        search_results = await search_task
        
        # 等待回复生成完成
        response = await response_task
        
        # 输出最终结果
        await self.simulate_tts_output(f"找到{len(search_results)}个相关商品")
        
        total_time = time.time() - start_time
        print(f"  🕒 总耗时: {total_time:.1f}s")
        print(f"  😊 用户体验: 用户仅等待 {first_feedback_delay:.3f}s 就听到反馈，感知等待时间大幅减少")
        
        return {
            'approach': 'new',
            'query': query,
            'total_time': total_time,
            'first_feedback_delay': first_feedback_delay,
            'user_experience': 'excellent'
        }
    
    async def run_comparison_demo(self):
        """运行对比演示"""
        print("🎭 状态消息优化对比演示")
        print("="*60)
        
        test_queries = [
            "手机",
            "苹果手机", 
            "便宜的运动鞋"
        ]
        
        for query in test_queries:
            print(f"\n🔍 测试查询: '{query}'")
            print("-" * 40)
            
            # 演示优化前的方法
            old_result = await self.demo_old_approach(query)
            self.demo_results.append(old_result)
            
            # 等待一下，让演示更清晰
            await asyncio.sleep(0.5)
            
            # 演示优化后的方法
            new_result = await self.demo_new_approach(query)
            self.demo_results.append(new_result)
            
            # 对比结果
            improvement = old_result['first_feedback_delay'] - new_result['first_feedback_delay']
            improvement_percent = (improvement / old_result['first_feedback_delay']) * 100
            
            print(f"\n📊 对比结果:")
            print(f"  首次反馈延迟改善: {improvement:.1f}s ({improvement_percent:.1f}%)")
            print(f"  用户感知等待时间大幅减少")
            
            print("\n" + "="*60)
    
    def print_summary(self):
        """打印演示总结"""
        print("\n🎯 优化效果总结")
        print("="*60)
        
        old_results = [r for r in self.demo_results if r['approach'] == 'old']
        new_results = [r for r in self.demo_results if r['approach'] == 'new']
        
        if old_results and new_results:
            avg_old_delay = sum(r['first_feedback_delay'] for r in old_results) / len(old_results)
            avg_new_delay = sum(r['first_feedback_delay'] for r in new_results) / len(new_results)
            
            improvement = avg_old_delay - avg_new_delay
            improvement_percent = (improvement / avg_old_delay) * 100
            
            print(f"📈 平均首次反馈延迟:")
            print(f"  优化前: {avg_old_delay:.1f}s")
            print(f"  优化后: {avg_new_delay:.3f}s")
            print(f"  改善幅度: {improvement:.1f}s ({improvement_percent:.1f}%)")
            
            print(f"\n🎉 优化效果:")
            print(f"  ✅ 状态消息立即输出，用户感知等待时间大幅减少")
            print(f"  ✅ 充分利用TTS播放时间，并行执行后台处理")
            print(f"  ✅ 改写结果立即播报，提升用户体验")
            print(f"  ✅ 整体响应速度感知提升 {improvement_percent:.0f}%")
            
            print(f"\n💡 核心优化原理:")
            print(f"  1. 状态消息立即输出到TTS，不等待后台处理")
            print(f"  2. 在TTS播放期间并行执行query改写和搜索")
            print(f"  3. 有改写结果时立即播报，无需等待搜索完成")
            print(f"  4. 充分利用等待时间，提升用户体验")

async def main():
    """主演示函数"""
    demo = StatusMessageDemo()
    
    print("🚀 开始状态消息优化演示")
    print("这个演示将展示优化前后状态消息输出时机的差异")
    print("重点关注首次反馈的延迟时间和用户体验改善")
    
    await demo.run_comparison_demo()
    demo.print_summary()
    
    print(f"\n✨ 演示完成！")
    print(f"通过立即输出状态消息并并行处理，用户体验得到显著提升。")

if __name__ == "__main__":
    asyncio.run(main())
