#!/usr/bin/env python3
"""
调试Agent创建问题
"""

from smolagents import Tool
from run import create_intelligent_agent


def debug_agent_creation():
    """调试Agent创建过程"""
    print("开始调试Agent创建...")

    try:
        # 直接检查create_intelligent_agent中的工具创建
        print("1. 直接检查工具创建过程...")
        from custom_tools import QueryAnalysisTool, UserQuestionTool, QueryEnhancementTool, WebSearchTool, SearchSummaryTool, TaobaoMainSearchTool
        from scripts.text_web_browser import SimpleTextBrowser, VisitTool
        from scripts.text_inspector_tool import TextInspectorTool
        from custom_model import CustomModel
        import os

        # 创建模型和浏览器
        model_params = {
            "model_id": "gpt-4-0409",
            "custom_role_conversions": {"tool-call": "assistant", "tool-response": "user"},
            "max_completion_tokens": 8192,
            "api_base": os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api"),
            "api_key": os.getenv("AZURE_OPENAI_API_KEY", "27db1fc058c1861870be4c21a7f93cdc"),
            "reasoning_effort": "low"
        }
        model = CustomModel(**model_params)

        browser_config = {
            "viewport_size": 1024 * 5,
            "downloads_folder": "downloads_folder",
            "request_kwargs": {"headers": {"User-Agent": "Mozilla/5.0"}, "timeout": 300},
        }
        browser = SimpleTextBrowser(**browser_config)
        text_limit = 100000

        # 创建工具列表
        print("2. 创建工具列表...")
        INTELLIGENT_TOOLS = [
            QueryAnalysisTool(model),
            UserQuestionTool(model),
            QueryEnhancementTool(model),
            WebSearchTool(),
            SearchSummaryTool(),
            TaobaoMainSearchTool(),
            VisitTool(browser),
            TextInspectorTool(model, text_limit),
        ]

        print(f"   创建了 {len(INTELLIGENT_TOOLS)} 个工具")

        # 检查每个工具
        for i, tool in enumerate(INTELLIGENT_TOOLS):
            tool_type = type(tool)
            is_tool_instance = isinstance(tool, Tool)
            tool_name = getattr(tool, 'name', 'NO_NAME')

            print(f"   {i+1}. {tool_name} - 类型: {tool_type} - 是Tool实例: {is_tool_instance}")

            if not is_tool_instance:
                print(f"      ❌ 这个工具不是Tool的实例！")
                print(f"      详细信息: {tool}")
                return False

        print("✅ 所有工具都是Tool的实例")

        # 现在创建基础agent
        print("3. 创建基础agent...")
        base_agent = create_intelligent_agent()
        print(f"✅ 基础agent创建成功: {type(base_agent)}")

        # 检查base_agent.tools的类型
        print(f"4. 检查base_agent.tools的类型: {type(base_agent.tools)}")

        if isinstance(base_agent.tools, dict):
            print(f"   tools是字典，包含 {len(base_agent.tools)} 个工具:")
            for name, tool in base_agent.tools.items():
                tool_type = type(tool)
                is_tool_instance = isinstance(tool, Tool)
                print(f"   '{name}' - 类型: {tool_type} - 是Tool实例: {is_tool_instance}")
        elif isinstance(base_agent.tools, list):
            print(f"   tools是列表，包含 {len(base_agent.tools)} 个工具:")
            for i, tool in enumerate(base_agent.tools):
                tool_type = type(tool)
                is_tool_instance = isinstance(tool, Tool)
                tool_name = getattr(tool, 'name', 'NO_NAME')
                print(f"   {i+1}. {tool_name} - 类型: {tool_type} - 是Tool实例: {is_tool_instance}")

                if not is_tool_instance:
                    print(f"      ❌ 这个工具不是Tool的实例！")
                    print(f"      详细信息: {tool}")
                    return False

        return True

    except Exception as e:
        print(f"❌ 创建过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = debug_agent_creation()
    if success:
        print("\n🎉 Agent创建调试成功！")
    else:
        print("\n💥 Agent创建调试失败！")
