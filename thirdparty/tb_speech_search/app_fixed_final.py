#!/usr/bin/env python3
"""
最终修复版本 - 解决所有网络和兼容性问题
"""

import os
import gradio as gr

# 设置环境变量避免网络问题
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'

# SSL证书路径
CERT_FILE = "/Users/<USER>/Documents/data/cert.pem"
KEY_FILE = "/Users/<USER>/Documents/data/key.pem"

def search_products(query: str):
    """搜索商品"""
    if not query.strip():
        return "请输入搜索关键词", ""
    
    try:
        from run import create_enhanced_agent
        from app_with_images import parse_and_display_products
        
        # 创建agent并搜索
        agent = create_enhanced_agent()
        response = agent.run(query)
        
        # 生成带图片的HTML展示
        html_display = parse_and_display_products(response)
        
        return response, html_display
        
    except Exception as e:
        error_msg = f"搜索过程中出现错误: {str(e)}"
        error_html = f"""
        <div style="padding: 20px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 8px; color: #a8071a;">
            <h3 style="color: #cf1322; margin-bottom: 10px;">❌ 搜索失败</h3>
            <p>{error_msg}</p>
        </div>
        """
        return error_msg, error_html

# 创建界面
with gr.Blocks(title="淘宝商品搜索助手 (最终修复版)", theme=gr.themes.Soft()) as demo:
    gr.Markdown("""
    # 🛍️ 淘宝商品搜索助手 (最终修复版)
    
    🔧 **已修复所有网络连接和兼容性问题**
    
    输入您想要搜索的商品，AI助手将为您推荐相关商品，**包括商品图片展示**！
    """)
    
    with gr.Row():
        with gr.Column(scale=4):
            query_input = gr.Textbox(
                label="🔍 搜索关键词",
                placeholder="请输入您想要搜索的商品，如：2000左右的手机、运动鞋推荐等...",
                lines=2
            )
        with gr.Column(scale=1):
            search_btn = gr.Button("🚀 开始搜索", variant="primary", size="lg")
    
    with gr.Tabs():
        with gr.TabItem("🎨 商品展示 (带图片)"):
            product_display = gr.HTML(label="商品信息展示")
        
        with gr.TabItem("📝 原始回复"):
            raw_output = gr.Textbox(
                label="AI助手原始回复",
                lines=15,
                max_lines=25
            )
    
    # 绑定搜索事件
    search_btn.click(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    query_input.submit(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    # 示例查询
    gr.Examples(
        examples=[
            ["2000左右的手机"],
            ["运动鞋推荐"],
            ["秋天水果推荐"],
            ["无线蓝牙耳机"],
            ["学生用品推荐"]
        ],
        inputs=[query_input],
        label="💡 试试这些搜索示例"
    )

def check_ssl_available():
    """检查SSL是否可用"""
    return os.path.exists(CERT_FILE) and os.path.exists(KEY_FILE)

def launch_with_ssl():
    """尝试SSL启动"""
    print("🔒 尝试SSL启动...")
    
    ssl_configs = [
        {"port": 7860, "name": "标准端口7860"},
        {"port": 8443, "name": "高端口8443"},
        {"port": 9443, "name": "备用端口9443"}
    ]
    
    for config in ssl_configs:
        try:
            print(f"🔄 尝试{config['name']}...")
            
            demo.launch(
                server_name="0.0.0.0",
                server_port=config["port"],
                ssl_keyfile=KEY_FILE,
                ssl_certfile=CERT_FILE,
                ssl_verify=False,
                share=False,
                show_error=True,
                quiet=True,
                inbrowser=False,
                prevent_thread_lock=False
            )
            
            print(f"✅ SSL启动成功！")
            print(f"🌐 HTTPS访问地址: https://localhost:{config['port']}")
            return True
            
        except Exception as e:
            print(f"❌ {config['name']}失败: {e}")
            continue
    
    return False

def launch_with_http():
    """HTTP启动"""
    print("🔄 使用HTTP启动...")
    
    http_configs = [
        {"port": 7860, "host": "127.0.0.1"},
        {"port": 7861, "host": "127.0.0.1"},
        {"port": 8080, "host": "127.0.0.1"},
        {"port": 8000, "host": "localhost"}
    ]
    
    for config in http_configs:
        try:
            print(f"🔄 尝试 {config['host']}:{config['port']}...")
            
            demo.launch(
                server_name=config["host"],
                server_port=config["port"],
                share=False,
                show_error=True,
                quiet=True,
                inbrowser=False,
                prevent_thread_lock=False
            )
            
            print(f"✅ HTTP启动成功！")
            print(f"🌐 HTTP访问地址: http://{config['host']}:{config['port']}")
            return True
            
        except Exception as e:
            print(f"❌ {config['host']}:{config['port']} 失败: {e}")
            continue
    
    return False

def main():
    """主函数"""
    print("🚀 启动最终修复版淘宝商品搜索助手...")
    print("=" * 60)
    
    # 检查SSL证书
    ssl_available = check_ssl_available()
    
    if ssl_available:
        print("🔒 检测到SSL证书，尝试HTTPS启动...")
        if launch_with_ssl():
            return
        else:
            print("❌ SSL启动失败，回退到HTTP...")
    else:
        print("⚠️ 未检测到SSL证书，使用HTTP启动...")
    
    # HTTP启动
    if not launch_with_http():
        print("❌ 所有启动方式都失败")
        print("\n🛠️ 请检查:")
        print("1. 端口是否被占用: lsof -i :7860")
        print("2. 网络配置是否正确")
        print("3. 防火墙设置")
        print("4. Python环境和依赖包")
        
        # 最后尝试：最简启动
        print("\n🔄 最后尝试：最简启动...")
        try:
            demo.launch()
            print("✅ 最简启动成功！")
        except Exception as e:
            print(f"❌ 最简启动也失败: {e}")

if __name__ == "__main__":
    main()
