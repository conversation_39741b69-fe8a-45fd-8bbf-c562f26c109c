#!/usr/bin/env python3
"""
Web调试工具 - 自动测试交互式搜索系统
"""

import asyncio
import json
import websockets
import time
from datetime import datetime

class WebDebugger:
    """Web调试器"""
    
    def __init__(self, host="127.0.0.1", port=8000):
        self.host = host
        self.port = port
        self.ws_url = f"ws://{host}:{port}/ws/debug_session_{int(time.time())}"
        self.websocket = None
        self.messages = []
        
    async def connect(self):
        """连接到WebSocket"""
        try:
            self.websocket = await websockets.connect(self.ws_url)
            print(f"✓ 已连接到 {self.ws_url}")
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    async def send_message(self, message_data):
        """发送消息"""
        if not self.websocket:
            print("✗ WebSocket未连接")
            return False
        
        try:
            await self.websocket.send(json.dumps(message_data))
            print(f"📤 发送: {message_data}")
            return True
        except Exception as e:
            print(f"✗ 发送失败: {e}")
            return False
    
    async def receive_message(self, timeout=30):
        """接收消息"""
        if not self.websocket:
            print("✗ WebSocket未连接")
            return None
        
        try:
            message = await asyncio.wait_for(
                self.websocket.recv(), 
                timeout=timeout
            )
            data = json.loads(message)
            self.messages.append({
                'timestamp': datetime.now().isoformat(),
                'data': data
            })
            print(f"📥 接收: {data}")
            return data
        except asyncio.TimeoutError:
            print(f"⏰ 接收超时 ({timeout}秒)")
            return None
        except Exception as e:
            print(f"✗ 接收失败: {e}")
            return None
    
    async def test_search_query(self, query="春游要买什么"):
        """测试搜索查询"""
        print(f"\n🔍 测试查询: '{query}'")
        print("=" * 50)
        
        # 等待连接建立消息
        connection_msg = await self.receive_message(timeout=10)
        if not connection_msg or connection_msg.get('type') != 'connection_established':
            print("✗ 未收到连接确认消息")
            return False
        
        # 发送搜索请求
        search_request = {
            "type": "start_search",
            "query": query
        }
        
        if not await self.send_message(search_request):
            return False
        
        # 监听响应
        interaction_count = 0
        max_interactions = 5
        search_completed = False
        
        while interaction_count < max_interactions and not search_completed:
            message = await self.receive_message(timeout=60)
            
            if not message:
                print("⏰ 等待响应超时")
                break
            
            message_type = message.get('type')
            
            if message_type == 'search_started':
                print(f"🚀 搜索已开始: {message.get('query')}")
                
            elif message_type == 'agent_interaction':
                interaction_count += 1
                print(f"🤖 Agent交互 #{interaction_count}")
                print(f"   消息: {message.get('message')}")
                print(f"   选项: {message.get('options')}")
                
                # 自动响应交互
                interaction_id = message.get('interaction_id')
                options = message.get('options')
                
                if options and len(options) > 0:
                    # 选择第一个选项
                    selected_option = options[0]
                    response = {
                        "type": "user_response",
                        "interaction_id": interaction_id,
                        "response_text": selected_option,
                        "selected_option": selected_option
                    }
                    print(f"🎯 自动选择: {selected_option}")
                    await self.send_message(response)
                else:
                    # 发送文本响应
                    response = {
                        "type": "user_response", 
                        "interaction_id": interaction_id,
                        "response_text": "运动户外用品"
                    }
                    print(f"💬 自动回复: 运动户外用品")
                    await self.send_message(response)
                
            elif message_type == 'status_update':
                status = message.get('status', {})
                print(f"📊 状态更新: {status.get('step', 'unknown')} - {status.get('query', '')}")
                
            elif message_type == 'search_completed':
                search_completed = True
                result = message.get('result')
                print(f"✅ 搜索完成!")
                print(f"📋 结果长度: {len(result) if result else 0} 字符")
                
                # 尝试解析结果
                try:
                    if isinstance(result, str):
                        parsed_result = json.loads(result)
                        if isinstance(parsed_result, list):
                            print(f"🛍️  找到 {len(parsed_result)} 个商品类别")
                            for category in parsed_result[:2]:  # 显示前2个类别
                                if 'items' in category:
                                    print(f"   📦 {category.get('category', 'Unknown')}: {len(category['items'])} 个商品")
                        else:
                            print(f"📄 结果格式: {type(parsed_result)}")
                    else:
                        print(f"📄 结果类型: {type(result)}")
                except:
                    print(f"📄 结果预览: {str(result)[:200]}...")
                
            elif message_type == 'search_error':
                print(f"❌ 搜索错误: {message.get('message')}")
                return False
                
            elif message_type == 'error':
                print(f"❌ 系统错误: {message.get('message')}")
                return False
                
            else:
                print(f"📨 其他消息: {message_type}")
        
        return search_completed
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 连接已关闭")
    
    def print_summary(self):
        """打印调试总结"""
        print("\n" + "=" * 60)
        print("📊 调试总结")
        print("=" * 60)
        print(f"总消息数: {len(self.messages)}")
        
        message_types = {}
        for msg in self.messages:
            msg_type = msg['data'].get('type', 'unknown')
            message_types[msg_type] = message_types.get(msg_type, 0) + 1
        
        print("消息类型统计:")
        for msg_type, count in message_types.items():
            print(f"  {msg_type}: {count}")

async def main():
    """主函数"""
    print("🐛 Web调试工具 - 交互式搜索系统")
    print("=" * 60)
    
    debugger = WebDebugger()
    
    try:
        # 连接
        if not await debugger.connect():
            return
        
        # 测试查询
        success = await debugger.test_search_query("春游要买什么")
        
        if success:
            print("\n🎉 测试成功完成!")
        else:
            print("\n❌ 测试失败")
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await debugger.close()
        debugger.print_summary()

if __name__ == "__main__":
    asyncio.run(main())
