#!/usr/bin/env python3
"""
SSL证书诊断工具
"""

import os
import subprocess
import sys
from datetime import datetime

def check_certificate(cert_path, key_path):
    """检查SSL证书"""
    print("🔍 SSL证书诊断")
    print("=" * 50)
    
    # 检查文件存在性
    print("1. 检查文件存在性:")
    cert_exists = os.path.exists(cert_path)
    key_exists = os.path.exists(key_path)
    
    print(f"   证书文件: {cert_path} {'✓' if cert_exists else '✗'}")
    print(f"   密钥文件: {key_path} {'✓' if key_exists else '✗'}")
    
    if not cert_exists or not key_exists:
        print("\n❌ 证书文件缺失")
        return False
    
    # 检查文件权限
    print("\n2. 检查文件权限:")
    cert_readable = os.access(cert_path, os.R_OK)
    key_readable = os.access(key_path, os.R_OK)
    
    print(f"   证书可读: {'✓' if cert_readable else '✗'}")
    print(f"   密钥可读: {'✓' if key_readable else '✗'}")
    
    if not cert_readable or not key_readable:
        print("\n❌ 证书文件权限不足")
        return False
    
    # 检查证书有效性
    print("\n3. 检查证书有效性:")
    try:
        # 获取证书信息
        result = subprocess.run([
            'openssl', 'x509', '-in', cert_path, '-text', '-noout'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"   ❌ 证书格式无效: {result.stderr}")
            return False
        
        cert_info = result.stdout
        
        # 检查有效期
        not_after_line = [line for line in cert_info.split('\n') if 'Not After' in line]
        if not_after_line:
            print(f"   ✓ 证书有效期: {not_after_line[0].strip()}")
        
        # 检查Subject
        subject_line = [line for line in cert_info.split('\n') if 'Subject:' in line]
        if subject_line:
            print(f"   ✓ 证书主体: {subject_line[0].strip()}")
        
        # 检查SAN (Subject Alternative Names)
        san_section = False
        san_found = False
        localhost_in_san = False
        
        for line in cert_info.split('\n'):
            if 'Subject Alternative Name' in line:
                san_section = True
                san_found = True
                continue
            elif san_section and line.strip().startswith('DNS:'):
                san_entries = line.strip()
                print(f"   ✓ SAN条目: {san_entries}")
                if 'localhost' in san_entries or '127.0.0.1' in san_entries:
                    localhost_in_san = True
                san_section = False
        
        if not san_found:
            print("   ⚠️  未找到SAN扩展")
        elif not localhost_in_san:
            print("   ⚠️  SAN中未包含localhost或127.0.0.1")
        else:
            print("   ✓ SAN包含localhost/127.0.0.1")
        
    except Exception as e:
        print(f"   ❌ 证书检查失败: {e}")
        return False
    
    # 检查密钥匹配
    print("\n4. 检查证书与密钥匹配:")
    try:
        # 获取证书的公钥指纹
        cert_result = subprocess.run([
            'openssl', 'x509', '-in', cert_path, '-pubkey', '-noout'
        ], capture_output=True, text=True)
        
        # 获取私钥的公钥指纹
        key_result = subprocess.run([
            'openssl', 'rsa', '-in', key_path, '-pubout'
        ], capture_output=True, text=True)
        
        if cert_result.returncode == 0 and key_result.returncode == 0:
            if cert_result.stdout == key_result.stdout:
                print("   ✓ 证书与密钥匹配")
            else:
                print("   ❌ 证书与密钥不匹配")
                return False
        else:
            print("   ⚠️  无法验证证书与密钥匹配性")
    
    except Exception as e:
        print(f"   ⚠️  密钥匹配检查失败: {e}")
    
    return True

def generate_self_signed_cert(cert_path, key_path):
    """生成自签名证书"""
    print("\n🔧 生成新的自签名证书")
    print("=" * 50)
    
    try:
        # 创建目录
        os.makedirs(os.path.dirname(cert_path), exist_ok=True)
        
        # 生成私钥和证书
        cmd = [
            'openssl', 'req', '-x509', '-newkey', 'rsa:4096',
            '-keyout', key_path, '-out', cert_path,
            '-days', '365', '-nodes',
            '-subj', '/C=CN/ST=Beijing/L=Beijing/O=Dev/CN=localhost',
            '-addext', 'subjectAltName=DNS:localhost,DNS:127.0.0.1,IP:127.0.0.1'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 自签名证书生成成功")
            print(f"   证书文件: {cert_path}")
            print(f"   密钥文件: {key_path}")
            
            # 设置适当的权限
            os.chmod(key_path, 0o600)
            os.chmod(cert_path, 0o644)
            
            return True
        else:
            print(f"❌ 证书生成失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 证书生成异常: {e}")
        return False

def main():
    """主函数"""
    cert_path = "/Users/<USER>/Documents/data/cert.pem"
    key_path = "/Users/<USER>/Documents/data/key.pem"
    
    print("🔐 SSL证书诊断工具")
    print("=" * 60)
    
    # 检查现有证书
    cert_valid = check_certificate(cert_path, key_path)
    
    if not cert_valid:
        print("\n💡 建议解决方案:")
        print("1. 生成新的自签名证书（包含localhost SAN）")
        print("2. 使用HTTP模式（不推荐用于生产环境）")
        
        choice = input("\n是否生成新的自签名证书？(y/N): ").lower().strip()
        
        if choice == 'y':
            if generate_self_signed_cert(cert_path, key_path):
                print("\n✅ 新证书生成完成，请重新启动服务")
            else:
                print("\n❌ 证书生成失败")
        else:
            print("\n💡 您可以使用以下命令启动HTTP模式:")
            print("   python run_interactive.py")
    else:
        print("\n✅ SSL证书检查通过")
        print("\n🚀 您可以使用以下命令启动HTTPS服务:")
        print("   python run_interactive.py --use-default-ssl")
    
    print("\n📋 启动选项说明:")
    print("   HTTP模式:  python run_interactive.py")
    print("   HTTPS模式: python run_interactive.py --use-default-ssl")
    print("   自定义SSL: python run_interactive.py --ssl-cert /path/to/cert.pem --ssl-key /path/to/key.pem")

if __name__ == "__main__":
    main()
