#!/usr/bin/env python3
"""
最终完整版本 - 支持反问能力和图片展示
"""

import os
import sys
import traceback
import json
import re

# 设置环境变量避免网络问题
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['PYTHONHTTPSVERIFY'] = '0'

def create_agent_safely():
    """安全地创建agent，保持反问能力"""
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from custom_model import CustomModel
        from custom_tools import TaobaoMainSearchTool, TaobaoItemDetailsTool, SearchSummaryTool, QueryAnalysisTool, UserQuestionTool
        from custom_agent import SpeechSearchAgent
        
        # 设置API参数
        API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "27db1fc058c1861870be4c21a7f93cdc")
        ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api")
        
        # 创建模型
        custom_role_conversions = {"tool-call": "assistant", "tool-response": "user"}
        model = CustomModel(
            model_id="gpt-4-0409",
            custom_role_conversions=custom_role_conversions,
            api_base=ENDPOINT,
            api_key=API_KEY,
            max_completion_tokens=8192
        )
        
        # 创建工具 - 包含反问工具
        tools = [
            QueryAnalysisTool(model),
            UserQuestionTool(model),
            SearchSummaryTool(),
            TaobaoMainSearchTool(),
            TaobaoItemDetailsTool(),
        ]
        
        # 尝试添加GoogleSearchTool
        try:
            from smolagents import GoogleSearchTool
            serper_api_key = os.getenv("SERPER_API_KEY", "4b1915a54600d6c6a2d84742f0332346dbb0a6d6")
            os.environ["SERPER_API_KEY"] = serper_api_key
            google_search_tool = GoogleSearchTool(provider="serper")
            tools.insert(0, google_search_tool)
            print("✓ GoogleSearchTool 已添加")
        except Exception as e:
            print(f"⚠️ 跳过GoogleSearchTool: {e}")
        
        # 创建agent - 保持原始的SpeechSearchAgent功能
        agent = SpeechSearchAgent(
            model=model,
            tools=tools,
            max_steps=8,
            verbosity_level=1
        )
        
        return agent
        
    except Exception as e:
        print(f"❌ 创建agent失败: {e}")
        traceback.print_exc()
        return None

def parse_agent_response(agent_response: str):
    """智能解析agent响应，支持多种格式"""
    
    # 1. 尝试解析JSON格式
    try:
        if agent_response.strip().startswith('[') and agent_response.strip().endswith(']'):
            products_data = json.loads(agent_response)
            if isinstance(products_data, list) and len(products_data) > 0:
                category_data = products_data[0]
                if 'items' in category_data:
                    return generate_product_cards(category_data['items'])
    except:
        pass
    
    # 2. 尝试解析Markdown格式
    products = parse_markdown_format(agent_response)
    if products:
        return generate_product_cards(products)
    
    # 3. 如果都不是，显示原始回复
    return generate_text_response(agent_response)

def parse_markdown_format(text):
    """解析Markdown格式的商品信息"""
    products = []
    
    # 提取图片URL
    image_pattern = r'!\[图片\]\((http[^)]+)\)'
    images = re.findall(image_pattern, text)
    
    # 提取购买链接
    link_pattern = r'\[([^\]]+)\]\((http[^)]+)\)'
    links = re.findall(link_pattern, text)
    
    # 提取价格
    price_pattern = r'价格：¥([0-9,]+\.?[0-9]*)'
    prices = re.findall(price_pattern, text)
    
    # 提取店铺
    shop_pattern = r'店铺：([^\n]+)'
    shops = re.findall(shop_pattern, text)
    
    # 提取商品标题和特点
    lines = text.split('\n')
    current_product = {}
    
    for line in lines:
        line = line.strip()
        if re.match(r'^\d+\.\s+', line):
            if current_product:
                products.append(current_product)
            title = re.sub(r'^\d+\.\s+', '', line)
            current_product = {'itemTitle': title}
        elif line.startswith('价格：'):
            price_match = re.search(r'¥([0-9,]+\.?[0-9]*)', line)
            if price_match:
                current_product['itemPrice'] = price_match.group(1)
        elif line.startswith('特点：'):
            current_product['features'] = line.replace('特点：', '').strip()
        elif line.startswith('店铺：'):
            current_product['shop'] = line.replace('店铺：', '').strip()
        elif '![图片]' in line:
            img_match = re.search(r'!\[图片\]\((http[^)]+)\)', line)
            if img_match:
                current_product['imageUrl'] = img_match.group(1)
        elif '[' in line and '](' in line:
            link_match = re.search(r'\[([^\]]+)\]\((http[^)]+)\)', line)
            if link_match:
                current_product['buyLink'] = link_match.group(2)
    
    if current_product:
        products.append(current_product)
    
    # 如果没有解析到完整信息，尝试按索引匹配
    if not products and (images or prices):
        max_items = max(len(images), len(prices), len(shops))
        for i in range(max_items):
            product = {
                'itemTitle': f"推荐商品 {i+1}",
                'itemPrice': prices[i] if i < len(prices) else "价格未知",
                'imageUrl': images[i] if i < len(images) else "",
                'shop': shops[i] if i < len(shops) else "店铺未知",
                'buyLink': links[i][1] if i < len(links) else ""
            }
            products.append(product)
    
    return products

def generate_product_cards(products):
    """生成商品卡片HTML"""
    if not products:
        return "<div style='padding: 20px; text-align: center; color: #666;'>没有找到商品信息</div>"
    
    html = """
    <div style="padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <h2 style="color: #1890ff; margin-bottom: 30px; text-align: center; font-size: 24px;">🛍️ 商品推荐结果</h2>
    """
    
    for i, product in enumerate(products, 1):
        title = product.get('itemTitle', f'商品 {i}')
        price = product.get('itemPrice', '价格未知')
        image_url = product.get('imageUrl', '')
        shop = product.get('shop', product.get('shopInfo', '店铺未知'))
        buy_link = product.get('buyLink', product.get('auctionURL', ''))
        features = product.get('features', '')
        item_id = product.get('itemId', '')
        
        # 如果没有购买链接但有item_id，生成链接
        if not buy_link and item_id:
            buy_link = f"http://a.m.taobao.com/i{item_id}.htm"
        
        html += f"""
        <div style="
            border: 1px solid #e8e8e8; 
            border-radius: 12px; 
            padding: 25px; 
            margin-bottom: 25px; 
            background: white; 
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 24px rgba(0,0,0,0.12)'" 
           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 16px rgba(0,0,0,0.08)'">
            
            <div style="display: flex; gap: 25px; align-items: flex-start;">
                <!-- 商品图片 -->
                <div style="flex-shrink: 0;">
                    {f'''
                    <img src="{image_url}" 
                         style="
                             width: 240px; 
                             height: 240px; 
                             object-fit: cover; 
                             border-radius: 12px; 
                             border: 2px solid #f0f0f0;
                             box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                         " 
                         alt="商品图片"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                    <div style="
                        width: 240px; 
                        height: 240px; 
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); 
                        border-radius: 12px; 
                        display: none; 
                        align-items: center; 
                        justify-content: center; 
                        color: #999;
                        font-size: 16px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 32px; margin-bottom: 10px;">📷</div>
                            <div>暂无图片</div>
                        </div>
                    </div>
                    ''' if image_url else '''
                    <div style="
                        width: 240px; 
                        height: 240px; 
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); 
                        border-radius: 12px; 
                        display: flex; 
                        align-items: center; 
                        justify-content: center; 
                        color: #999;
                        font-size: 16px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 32px; margin-bottom: 10px;">📷</div>
                            <div>暂无图片</div>
                        </div>
                    </div>
                    '''}
                </div>
                
                <!-- 商品信息 -->
                <div style="flex: 1; min-width: 0;">
                    <h3 style="
                        color: #2c3e50; 
                        margin: 0 0 20px 0; 
                        font-size: 20px; 
                        font-weight: 600;
                        line-height: 1.4;
                        word-wrap: break-word;
                    ">{title}</h3>
                    
                    <!-- 价格 -->
                    <div style="margin-bottom: 20px;">
                        <span style="
                            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%); 
                            color: white; 
                            padding: 10px 20px; 
                            border-radius: 25px; 
                            font-weight: bold; 
                            font-size: 20px;
                            box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
                            display: inline-block;
                        ">💰 ¥{price}</span>
                    </div>
                    
                    <!-- 特点 -->
                    {f'''
                    <div style="
                        color: #555; 
                        margin: 15px 0; 
                        line-height: 1.6;
                        background: #f8f9fa;
                        padding: 12px 16px;
                        border-radius: 8px;
                        border-left: 4px solid #1890ff;
                    ">
                        <strong>🔧 产品特点：</strong>{features}
                    </div>
                    ''' if features else ''}
                    
                    <!-- 店铺信息 -->
                    <div style="color: #666; margin: 15px 0;">
                        <span style="
                            background: #e6f7ff; 
                            color: #1890ff;
                            padding: 6px 12px; 
                            border-radius: 6px; 
                            font-size: 14px;
                            border: 1px solid #91d5ff;
                        ">
                            🏪 {shop}
                        </span>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div style="margin-top: 20px;">
                        {f'''
                        <a href="{buy_link}" 
                           target="_blank" 
                           style="
                               display: inline-block; 
                               background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); 
                               color: white; 
                               padding: 12px 24px; 
                               text-decoration: none; 
                               border-radius: 25px; 
                               margin-right: 15px;
                               font-weight: 600;
                               font-size: 16px;
                               box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
                               transition: all 0.3s ease;
                           "
                           onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 6px 16px rgba(24, 144, 255, 0.4)'"
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(24, 144, 255, 0.3)'">
                            🛒 立即购买
                        </a>
                        ''' if buy_link else ''}
                    </div>
                </div>
            </div>
        </div>
        """
    
    html += """
    </div>
    <style>
        @media (max-width: 768px) {
            div[style*="display: flex"] {
                flex-direction: column !important;
            }
            img, div[style*="width: 240px"] {
                width: 100% !important;
                max-width: 320px !important;
                margin: 0 auto !important;
            }
        }
    </style>
    """
    
    return html

def generate_text_response(text):
    """生成文本响应的HTML"""
    return f"""
    <div style="
        padding: 25px; 
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); 
        border-radius: 12px; 
        border: 1px solid #dee2e6;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
        <h3 style="
            color: #495057; 
            margin-bottom: 20px; 
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        ">
            🤖 AI助手回复
        </h3>
        <div style="
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            white-space: pre-wrap; 
            line-height: 1.8; 
            font-size: 15px;
            color: #333;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        ">
            {text}
        </div>
    </div>
    """

def search_products_complete(query: str):
    """完整的商品搜索功能"""
    if not query.strip():
        return "请输入搜索关键词", ""
    
    try:
        print(f"🔍 开始搜索: {query}")
        
        # 创建agent
        agent = create_agent_safely()
        if not agent:
            return "❌ Agent创建失败", "<div style='color: red;'>Agent创建失败</div>"
        
        print("✅ Agent创建成功，开始搜索...")
        
        # 执行搜索
        response = agent.run(query)
        print("✅ 搜索完成")
        
        # 智能解析响应
        html_display = parse_agent_response(response)
        
        return response, html_display
        
    except Exception as e:
        error_msg = f"搜索过程中出现错误: {str(e)}"
        print(f"❌ {error_msg}")
        traceback.print_exc()
        
        error_html = f"""
        <div style="
            padding: 20px; 
            background: #fff2f0; 
            border: 1px solid #ffccc7; 
            border-radius: 8px; 
            color: #a8071a;
        ">
            <h3 style="color: #cf1322; margin-bottom: 10px;">❌ 搜索失败</h3>
            <p>{error_msg}</p>
            <details style="margin-top: 15px;">
                <summary style="cursor: pointer; color: #1890ff;">查看详细错误信息</summary>
                <pre style="
                    background: #f5f5f5; 
                    padding: 10px; 
                    border-radius: 4px; 
                    margin-top: 10px;
                    font-size: 12px;
                    overflow-x: auto;
                ">{traceback.format_exc()}</pre>
            </details>
        </div>
        """
        return error_msg, error_html

# 创建Gradio界面
try:
    import gradio as gr
    
    with gr.Blocks(title="淘宝商品搜索助手 (完整版)", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🛍️ 淘宝商品搜索助手 (完整版)
        
        ✨ **功能特色：**
        - 🤖 **智能反问** - AI会主动询问澄清您的需求
        - 🖼️ **图片展示** - 高清商品图片自动显示
        - 💰 **价格信息** - 清晰的价格标签和对比
        - 🛒 **一键购买** - 直达商品页面
        - 📱 **响应式设计** - 支持桌面和移动端
        
        输入您想要搜索的商品，AI助手将智能分析您的需求并推荐相关商品！
        """)
        
        with gr.Row():
            with gr.Column(scale=4):
                query_input = gr.Textbox(
                    label="🔍 搜索关键词",
                    placeholder="请输入您想要搜索的商品，如：2000左右的手机、运动鞋推荐等...",
                    lines=2
                )
            with gr.Column(scale=1):
                search_btn = gr.Button("🚀 开始搜索", variant="primary", size="lg")
        
        with gr.Tabs():
            with gr.TabItem("🎨 商品展示 (带图片)"):
                product_display = gr.HTML(label="商品信息展示")
            
            with gr.TabItem("📝 完整对话"):
                raw_output = gr.Textbox(
                    label="AI助手完整回复",
                    lines=15,
                    max_lines=25
                )
        
        # 绑定搜索事件
        search_btn.click(
            fn=search_products_complete,
            inputs=[query_input],
            outputs=[raw_output, product_display]
        )
        
        query_input.submit(
            fn=search_products_complete,
            inputs=[query_input],
            outputs=[raw_output, product_display]
        )
        
        # 示例查询
        gr.Examples(
            examples=[
                ["2000左右的手机"],
                ["运动鞋推荐"],
                ["秋天水果推荐"],
                ["无线蓝牙耳机"],
                ["学生用品推荐"],
                ["适合冬天的保暖用品"]
            ],
            inputs=[query_input],
            label="💡 试试这些搜索示例"
        )

    def main():
        """主函数"""
        print("🚀 启动完整版淘宝商品搜索助手...")
        print("=" * 60)
        print("✨ 支持智能反问和图片展示")
        
        # 测试agent创建
        print("\n🧪 测试agent创建...")
        test_agent = create_agent_safely()
        if test_agent:
            print("✅ Agent测试成功，支持反问功能")
        else:
            print("❌ Agent测试失败")
            return
        
        # 启动Gradio
        try:
            demo.launch(
                server_name="127.0.0.1",
                server_port=7860,
                share=False,
                show_error=True,
                quiet=False,
                inbrowser=True
            )
        except Exception as e:
            print(f"❌ Gradio启动失败: {e}")
            print("🔄 尝试其他端口...")
            try:
                demo.launch(
                    server_name="127.0.0.1",
                    server_port=7861,
                    share=False,
                    show_error=True,
                    quiet=True,
                    inbrowser=True
                )
            except Exception as e2:
                print(f"❌ 所有端口都失败: {e2}")

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ Gradio导入失败: {e}")
    print("请安装Gradio: pip install gradio")
except Exception as e:
    print(f"❌ 创建界面失败: {e}")
    traceback.print_exc()
