#!/usr/bin/env python3
"""
测试工具执行
"""

class MockModel:
    def generate(self, messages):
        class MockResponse:
            def __init__(self, content):
                self.content = content
        return MockResponse('{"can_direct_search": true, "intent_clarity": "clear", "analysis_reason": "测试", "suggested_action": "direct_search", "missing_info": []}')

def test_tool_execution():
    """测试工具执行"""
    print("测试工具执行...")
    
    try:
        from custom_tools import QueryAnalysisTool
        from intelligent_search_agent import IntelligentSearchAgent
        
        model = MockModel()
        
        # 创建工具
        query_tool = QueryAnalysisTool(model)
        print(f"✓ 工具创建成功: {query_tool.name}")
        
        # 测试工具直接调用
        result = query_tool.forward("春游带什么东西")
        print(f"✓ 工具直接调用成功: {result[:50]}...")
        
        # 创建agent（简化版本，不实际运行）
        tools = [query_tool]
        
        # 测试agent的execute_tool_call方法
        agent = IntelligentSearchAgent(
            tools=tools,
            model=model,
            max_steps=3
        )
        
        print(f"✓ Agent创建成功")
        
        # 测试execute_tool_call方法
        if hasattr(agent, 'execute_tool_call'):
            print("✓ execute_tool_call方法存在")
            
            # 测试工具调用
            result = agent.execute_tool_call("query_analysis", {"query": "测试查询"})
            print(f"✓ execute_tool_call调用成功: {result[:50]}...")
            
        else:
            print("✗ execute_tool_call方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行测试"""
    print("测试工具执行功能")
    print("=" * 30)
    
    if test_tool_execution():
        print("\n✓ 工具执行测试通过")
        print("execute_tool_call方法已正确实现")
    else:
        print("\n✗ 工具执行测试失败")

if __name__ == "__main__":
    main()
