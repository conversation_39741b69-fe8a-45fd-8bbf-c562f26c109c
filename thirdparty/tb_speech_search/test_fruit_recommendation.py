#!/usr/bin/env python3
"""
测试"秋天的水果推荐"类型的查询，确保最终输出商品信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fruit_recommendation():
    """测试水果推荐查询"""
    print("🍎 测试秋天水果推荐查询...")
    print("=" * 50)
    
    try:
        from run import create_enhanced_agent
        
        # 创建增强型agent
        print("📋 创建增强型agent...")
        agent = create_enhanced_agent(model_id="gpt-4-0409")
        print("✅ Agent创建成功")
        
        # 测试查询
        query = "秋天的水果推荐"
        print(f"\n🔍 测试查询: {query}")
        print("-" * 30)
        
        # 运行agent
        result = agent.run(query)
        
        print(f"\n✅ 查询完成")
        print(f"📄 最终结果:")
        print(result)
        
        # 分析结果
        analyze_fruit_result(result)
        
        # 检查agent状态
        print(f"\n📊 Agent状态检查:")
        print(f"  已搜索商品: {getattr(agent, '_has_searched_products', False)}")
        print(f"  已获取详情: {getattr(agent, '_has_detailed_info', False)}")
        print(f"  待处理ID数量: {len(getattr(agent, '_pending_item_ids', []))}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def analyze_fruit_result(result):
    """分析水果推荐结果"""
    if not isinstance(result, str):
        print("⚠️ 结果不是字符串格式")
        return
    
    result_lower = result.lower()
    
    # 检查是否包含商品信息
    product_indicators = {
        '价格信息': ['¥', '元', '价格', 'price'],
        '商品名称': ['苹果', '梨', '橘子', '柿子', '石榴', '水果'],
        '店铺信息': ['店铺', 'shop', '旗舰店', '专营店'],
        '销量信息': ['销量', '付款', '人付款', '销售'],
        '商品ID': ['itemid', 'item_id', 'id']
    }
    
    found_indicators = []
    for category, keywords in product_indicators.items():
        if any(keyword in result_lower for keyword in keywords):
            found_indicators.append(category)
    
    print(f"\n📊 结果分析:")
    if found_indicators:
        print(f"✅ 包含商品信息: {', '.join(found_indicators)}")
    else:
        print("❌ 缺少商品信息")
    
    # 检查结果长度和详细程度
    if len(result) > 200:
        print(f"✅ 结果详细 (长度: {len(result)} 字符)")
    else:
        print(f"⚠️ 结果较简单 (长度: {len(result)} 字符)")
    
    # 检查是否只是建议而没有具体商品
    suggestion_only_keywords = ['建议', '推荐', '可以', '应该', '不妨']
    has_suggestions = any(keyword in result for keyword in suggestion_only_keywords)
    has_specific_products = any(keyword in result_lower for keyword in ['¥', '店铺', '付款'])
    
    if has_suggestions and not has_specific_products:
        print("⚠️ 可能只包含建议，缺少具体商品信息")
    elif has_specific_products:
        print("✅ 包含具体的商品信息")

def test_multiple_queries():
    """测试多种类似查询"""
    print("\n🧪 测试多种推荐类查询...")
    print("=" * 50)
    
    queries = [
        "秋天的水果推荐",
        "适合冬天的保暖用品",
        "学生用品推荐",
        "健身器材推荐"
    ]
    
    try:
        from run import create_enhanced_agent
        
        for i, query in enumerate(queries, 1):
            print(f"\n🔍 测试 {i}: {query}")
            print("-" * 20)
            
            try:
                agent = create_enhanced_agent(model_id="gpt-4-0409")
                result = agent.run(query)
                
                # 简单分析
                has_product_info = any(keyword in result.lower() for keyword in ['¥', '店铺', '付款', 'price'])
                status = "✅ 包含商品信息" if has_product_info else "❌ 缺少商品信息"
                print(f"结果: {status} (长度: {len(result)} 字符)")
                
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    # 测试水果推荐
    test_fruit_recommendation()
    
    # 测试多种查询
    # test_multiple_queries()  # 取消注释以测试更多查询
