#!/usr/bin/env python3
"""
启动交互式商品搜索系统
"""

import argparse
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def install_dependencies():
    """安装必要的依赖"""
    try:
        import fastapi
        import uvicorn
        import jinja2
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装以下依赖:")
        print("pip install fastapi uvicorn jinja2 python-multipart")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ['templates', 'static']
    
    for dir_name in directories:
        dir_path = current_dir / dir_name
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"✓ 创建目录: {dir_path}")

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        'interactive_agent.py',
        'web_ui.py', 
        'templates/index.html',
        'custom_agent.py',
        'run.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (current_dir / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✓ 所有必要文件存在")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动交互式商品搜索系统")
    parser.add_argument("--host", default="127.0.0.1", help="服务器地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--check-only", action="store_true", help="仅检查环境，不启动服务")
    parser.add_argument("--ssl-cert", help="SSL证书文件路径")
    parser.add_argument("--ssl-key", help="SSL密钥文件路径")
    parser.add_argument("--use-default-ssl", action="store_true",
                       help="使用默认SSL证书路径 (/Users/<USER>/Documents/data/)")

    args = parser.parse_args()
    
    print("🚀 交互式商品搜索系统启动检查")
    print("=" * 50)
    
    # 检查依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 检查文件
    if not check_files():
        sys.exit(1)
    
    if args.check_only:
        print("\n✅ 环境检查完成，所有组件就绪")
        return
    
    # 处理SSL配置
    ssl_certfile = None
    ssl_keyfile = None

    if args.use_default_ssl:
        ssl_certfile = "/Users/<USER>/Documents/data/cert.pem"
        ssl_keyfile = "/Users/<USER>/Documents/data/key.pem"
    elif args.ssl_cert and args.ssl_key:
        ssl_certfile = args.ssl_cert
        ssl_keyfile = args.ssl_key

    print("\n🌟 启动交互式商品搜索系统...")

    if ssl_certfile and ssl_keyfile:
        protocol = "https"
        print(f"🔒 启用SSL加密模式")
    else:
        protocol = "http"
        print(f"⚠️  使用HTTP模式（建议在生产环境中使用SSL）")

    print(f"📍 访问地址: {protocol}://{args.host}:{args.port}")
    print("💡 使用说明:")
    print("   1. 在浏览器中打开上述地址")
    print("   2. 输入您想要搜索的商品")
    print("   3. 根据AI助手的提问进行交互")
    print("   4. 获得个性化的搜索结果")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)

    try:
        # 导入并启动Web UI
        from web_ui import WebUIManager

        ui_manager = WebUIManager()
        ui_manager.run(
            host=args.host,
            port=args.port,
            ssl_certfile=ssl_certfile,
            ssl_keyfile=ssl_keyfile
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔧 故障排除:")
        print("   1. 检查端口是否被占用")
        print("   2. 确认所有依赖已正确安装")
        print("   3. 检查API密钥配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
