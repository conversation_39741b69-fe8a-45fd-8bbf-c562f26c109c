#!/usr/bin/env python3
"""
测试工具调用序列，验证TaobaoMainSearchTool -> TaobaoItemDetailsTool的逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from custom_tools import TaobaoMainSearchTool, TaobaoItemDetailsTool
import json
import re

def test_tool_sequence():
    """测试工具调用序列"""
    print("🧪 测试工具调用序列...")
    
    # 1. 创建工具实例
    search_tool = TaobaoMainSearchTool()
    details_tool = TaobaoItemDetailsTool()
    
    # 2. 执行主搜
    print("\n📋 步骤1: 执行淘宝主搜...")
    query = "运动鞋"
    search_result = search_tool.forward(query)
    print(f"搜索查询: {query}")
    print(f"搜索结果长度: {len(search_result) if isinstance(search_result, str) else 'N/A'}")
    
    # 3. 从搜索结果中提取商品ID
    print("\n📋 步骤2: 提取商品ID...")
    item_ids = extract_item_ids(search_result)
    print(f"提取到的商品ID: {item_ids}")
    
    if not item_ids:
        print("❌ 未能提取到商品ID，测试终止")
        return
    
    # 4. 调用商品详情工具
    print("\n📋 步骤3: 获取商品详情...")
    # 取前3个ID进行测试
    test_ids = item_ids[:3]
    ids_json = json.dumps(test_ids)
    print(f"测试商品ID: {test_ids}")
    
    details_result = details_tool.forward(ids_json)
    print(f"详情结果长度: {len(details_result) if isinstance(details_result, str) else 'N/A'}")
    
    # 5. 解析详情结果
    print("\n📋 步骤4: 解析详情结果...")
    try:
        details_data = json.loads(details_result)
        if isinstance(details_data, list) and len(details_data) > 0:
            print(f"✅ 成功获取 {len(details_data)} 个商品的详细信息")
            
            # 显示第一个商品的关键信息
            first_item = details_data[0]
            print(f"\n📦 第一个商品详情预览:")
            key_fields = ['title', 'price', 'item_id', 'shopInfo', 'realSales']
            for field in key_fields:
                if field in first_item:
                    value = first_item[field]
                    if isinstance(value, str) and len(value) > 50:
                        print(f"  {field}: {value[:50]}...")
                    else:
                        print(f"  {field}: {value}")
        else:
            print("⚠️ 详情结果为空或格式不正确")
    except json.JSONDecodeError:
        print("⚠️ 无法解析详情结果JSON")
    except Exception as e:
        print(f"⚠️ 处理详情结果时出错: {e}")

def extract_item_ids(search_result):
    """从搜索结果中提取商品ID"""
    item_ids = []
    
    try:
        if isinstance(search_result, str):
            # 尝试解析JSON
            try:
                result_data = json.loads(search_result)
            except:
                # 如果不是JSON，使用正则表达式提取
                id_pattern = r'"(?:itemId|item_id|nid|nidlong)"\s*:\s*"?(\d+)"?'
                ids = re.findall(id_pattern, search_result)
                return ids[:5]  # 最多返回5个ID
        else:
            result_data = search_result

        # 处理结构化数据
        if isinstance(result_data, list):
            for item in result_data:
                if isinstance(item, dict):
                    for id_field in ['itemId', 'item_id', 'nid', 'nidlong', 'id']:
                        if id_field in item:
                            item_ids.append(str(item[id_field]))
                            break
        elif isinstance(result_data, dict):
            if 'items' in result_data:
                return extract_item_ids(result_data['items'])
            elif 'result' in result_data:
                return extract_item_ids(result_data['result'])
                
    except Exception as e:
        print(f"⚠️ 提取商品ID时出错: {e}")
    
    return item_ids[:5]  # 最多返回5个ID

if __name__ == "__main__":
    test_tool_sequence()
