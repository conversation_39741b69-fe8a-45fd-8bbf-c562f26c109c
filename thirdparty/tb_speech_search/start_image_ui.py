#!/usr/bin/env python3
"""
启动带图片展示的淘宝商品搜索Web UI
"""

import os
import sys

def main():
    print("🚀 启动带图片展示的淘宝商品搜索助手...")
    print("=" * 60)
    
    # 检查环境
    try:
        import gradio as gr
        print("✅ Gradio 已安装")
    except ImportError:
        print("❌ Gradio 未安装，请运行: pip install gradio")
        return
    
    try:
        from run import create_enhanced_agent
        print("✅ Agent 模块正常")
    except ImportError as e:
        print(f"❌ Agent 模块导入失败: {e}")
        return
    
    # 启动UI
    try:
        from app_with_images import demo
        
        print("\n🎉 启动成功！")
        print("📱 Web界面地址: http://localhost:7860")
        print("🖼️ 支持商品图片展示")
        print("🛒 支持一键购买链接")
        print("\n💡 功能特色:")
        print("  - 智能商品搜索")
        print("  - 商品图片展示")
        print("  - 详细商品信息")
        print("  - 价格和店铺信息")
        print("  - 直达购买链接")
        print("\n🔍 试试这些搜索:")
        print("  - 2000左右的手机")
        print("  - 运动鞋推荐")
        print("  - 秋天水果推荐")
        print("  - 无线蓝牙耳机")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动服务
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            show_error=True,
            quiet=False
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止，感谢使用！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
