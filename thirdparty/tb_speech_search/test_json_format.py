#!/usr/bin/env python3
"""
测试JSON格式修复
"""

import subprocess
import sys
import time

def test_json_format_fix():
    """测试JSON格式修复"""
    print("测试JSON格式修复...")
    
    cmd = [
        sys.executable, 'run.py',
        '--question=苹果手机',
        '--model-id=gpt-4o-0806', 
        '--agent-type=intelligent'
    ]
    
    try:
        # 启动进程并等待
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待15秒钟
        time.sleep(15)
        
        # 终止进程并获取输出
        process.terminate()
        try:
            stdout, stderr = process.communicate(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
        
        print("=== STDOUT ===")
        print(stdout[:2000] if stdout else "No stdout")
        
        print("\n=== STDERR ===")  
        print(stderr[:1000] if stderr else "No stderr")
        
        # 检查是否有JSON解析错误
        if "JSON blob you used is invalid" in stderr:
            print("\n✗ 仍然有JSON解析错误")
            return False
        elif "Error while parsing tool calls" in stderr:
            print("\n✗ 仍然有工具调用解析错误")
            return False
        elif "使用智能搜索Agent" in stdout:
            print("\n✓ Agent启动成功")
            if "JSON blob" not in stderr:
                print("✓ 没有JSON解析错误")
                return True
            else:
                print("✗ 仍有JSON相关错误")
                return False
        else:
            print("\n? 输出不明确，但没有明显错误")
            return True
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_react_mode():
    """测试react模式"""
    print("\n测试React模式...")
    
    cmd = [
        sys.executable, 'run.py',
        '--question=测试',
        '--agent-type=react'
    ]
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if "JSON blob you used is invalid" in result.stderr:
            print("✗ React模式有JSON解析错误")
            return False
        else:
            print("✓ React模式没有JSON解析错误")
            return True
            
    except subprocess.TimeoutExpired:
        print("✓ React模式运行正常（超时但没有错误）")
        return True
    except Exception as e:
        print(f"✗ React模式测试失败: {e}")
        return False

def main():
    """运行测试"""
    print("JSON格式修复测试")
    print("=" * 50)
    
    results = []
    results.append(test_react_mode())
    results.append(test_json_format_fix())
    
    print("\n" + "=" * 50)
    print("测试结果:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过 ({passed}/{total})")
        print("\n✅ JSON格式问题已修复")
        print("✅ 工具调用解析正常")
        print("✅ Agent可以正常运行")
        
    else:
        print(f"❌ 部分测试失败 ({passed}/{total})")
        print("可能需要进一步调整提示模板")

if __name__ == "__main__":
    main()
