#!/usr/bin/env python3
"""
简单测试增强型agent
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_agent():
    """测试增强型agent"""
    print("🧪 测试增强型Agent...")
    
    try:
        from run import create_enhanced_agent
        
        # 创建增强型agent
        print("📋 创建增强型agent...")
        agent = create_enhanced_agent(model_id="gpt-4-0409")
        
        # 测试查询
        test_query = "运动鞋"
        print(f"🔍 测试查询: {test_query}")
        
        # 运行agent
        result = agent.run(test_query)
        print(f"\n✅ Agent执行完成")
        print(f"📄 最终结果: {result}")
        
        # 检查结果是否包含详细信息
        if isinstance(result, str):
            has_details = any(keyword in result.lower() for keyword in [
                '图片', 'image', '店铺', 'shop', '销量', '付款', '¥'
            ])
            if has_details:
                print("✅ 结果包含详细信息")
            else:
                print("⚠️ 结果可能缺少详细信息")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_agent()
