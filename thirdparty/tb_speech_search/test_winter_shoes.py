#!/usr/bin/env python3
"""
测试"冬天的跑鞋推荐"查询的处理
"""

import json
from interactive_agent import InteractiveSearchAgent, InteractionType
from run import create_intelligent_agent


def test_winter_shoes_query():
    """测试冬天跑鞋推荐查询"""
    
    # 创建基础agent
    base_agent = create_intelligent_agent()
    tools_list = list(base_agent.tools.values())
    
    # 创建交互式agent
    agent = InteractiveSearchAgent(
        model=base_agent.model,
        tools=tools_list,
        max_steps=base_agent.max_steps,
        verbosity_level=1,
        name="test_agent",
        description="测试交互式商品搜索助手"
    )
    
    # 测试查询
    test_query = "冬天的跑鞋推荐"
    print(f"测试查询: {test_query}")
    
    # 1. 分析查询
    print("\n1. 查询分析:")
    analysis = agent._analyze_query_clarity(test_query)
    print(f"   clarity_score: {analysis.get('query_clarity_score')}")
    print(f"   needs_clarification: {analysis.get('needs_clarification')}")
    print(f"   is_recommendation_query: {analysis.get('is_recommendation_query')}")
    print(f"   ambiguous_keywords: {analysis.get('ambiguous_keywords')}")
    
    # 2. 判断是否需要交互
    print("\n2. 交互判断:")
    interaction_context = {
        **analysis,
        'multiple_search_directions': False,
        'user_expects_interaction': '?' in test_query or '推荐' in test_query
    }
    
    should_interact = agent._should_interact_with_user(interaction_context)
    print(f"   should_interact: {should_interact}")
    print(f"   user_expects_interaction: {interaction_context['user_expects_interaction']}")
    
    # 3. 生成交互
    if should_interact and analysis.get('needs_clarification'):
        print("\n3. 生成澄清问题:")
        interaction = agent._generate_clarification_question(test_query, analysis.get('analysis', {}))
        print(f"   类型: {interaction.interaction_type}")
        print(f"   消息: {interaction.message}")
        print(f"   选项数量: {len(interaction.options) if interaction.options else 0}")
        if interaction.options:
            print("   选项:")
            for i, option in enumerate(interaction.options, 1):
                print(f"     {i}. {option}")
        else:
            print("   ❌ 没有生成选项")
    else:
        print("\n3. 不需要澄清，可能生成确认问题")
        interaction = agent._generate_confirmation_question([], test_query)
        print(f"   类型: {interaction.interaction_type}")
        print(f"   消息: {interaction.message}")
        print(f"   选项数量: {len(interaction.options) if interaction.options else 0}")
        if interaction.options:
            print("   选项:")
            for i, option in enumerate(interaction.options, 1):
                print(f"     {i}. {option}")
    
    # 4. 测试关键词匹配
    print("\n4. 关键词匹配测试:")
    
    # 冬天关键词
    winter_keywords = ['冬天', '冬季', '寒冷']
    winter_match = any(keyword in test_query for keyword in winter_keywords)
    print(f"   冬天关键词匹配: {winter_match}")
    
    # 运动关键词
    sport_keywords = ['运动', '健身', '锻炼', '跑鞋', '运动鞋', '跑步']
    sport_match = any(keyword in test_query for keyword in sport_keywords)
    print(f"   运动关键词匹配: {sport_match}")
    
    # 组合匹配
    winter_shoe_match = (any(keyword in test_query for keyword in ['冬', '保暖']) and 
                        any(keyword in test_query for keyword in ['跑鞋', '运动鞋', '鞋子']))
    print(f"   冬天鞋子组合匹配: {winter_shoe_match}")
    
    return True


if __name__ == "__main__":
    print("开始测试冬天跑鞋推荐查询...")
    print("=" * 50)
    
    success = test_winter_shoes_query()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试完成")
    else:
        print("❌ 测试失败")
