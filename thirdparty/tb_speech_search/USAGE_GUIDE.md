# 🛍️ 淘宝商品搜索助手 - 使用指南

## 🎯 快速开始

### 1. 启动带图片展示的Web UI

```bash
cd smolagents/examples/tb_speech_search
python app_with_images.py
```

或者使用启动脚本：

```bash
python start_image_ui.py
```

### 2. 访问界面

在浏览器中打开：http://localhost:7860

## ✨ 新功能亮点

### 🖼️ 商品图片展示
- **自动解析**：从AI回复中提取商品图片URL
- **高清展示**：220x220像素的清晰商品图片
- **错误处理**：图片加载失败时显示占位符

### 🎨 美观界面设计
- **卡片布局**：每个商品独立的卡片展示
- **渐变效果**：现代化的视觉设计
- **悬停动画**：鼠标悬停时的交互效果
- **响应式**：支持桌面和移动端

### 🛒 完整购物信息
- **价格标签**：醒目的价格展示（红色渐变背景）
- **产品特点**：详细的功能特性说明
- **店铺信息**：清晰的商家信息展示
- **购买链接**：一键跳转到商品页面

## 📱 界面功能

### 主要区域
1. **搜索框**：输入商品关键词
2. **商品展示**：带图片的商品卡片列表
3. **原始回复**：AI助手的完整文本回复

### 商品卡片内容
- 🖼️ **商品图片**：左侧展示商品外观
- 📝 **商品标题**：商品名称和型号
- 💰 **价格信息**：突出显示的价格标签
- 🔧 **产品特点**：关键配置和功能
- 🏪 **店铺名称**：商家信息
- 🛒 **购买按钮**：直达商品页面

## 🔍 搜索示例

### 1. 具体商品搜索
```
输入：iPhone 15
效果：显示iPhone 15相关商品，包含图片、价格、配置等
```

### 2. 价格范围搜索
```
输入：2000左右的手机
效果：推荐2000元价位的手机，自动筛选合适商品
```

### 3. 推荐类搜索
```
输入：秋天水果推荐
效果：先了解时令水果，然后搜索相关商品
```

### 4. 类别搜索
```
输入：运动鞋推荐
效果：展示各种运动鞋商品，包含图片和详细信息
```

## 🎨 界面特色

### 视觉设计
- **现代化风格**：简洁美观的界面设计
- **色彩搭配**：蓝色主题色，红色价格标签
- **圆角设计**：柔和的视觉效果
- **阴影效果**：增强层次感

### 交互体验
- **悬停效果**：卡片和按钮的悬停动画
- **响应式布局**：自适应不同屏幕尺寸
- **加载状态**：搜索过程中的状态提示
- **错误处理**：友好的错误信息展示

## 🔧 技术特点

### 智能解析
- **正则表达式**：精确提取图片URL、价格、链接等信息
- **结构化处理**：将AI回复转换为结构化数据
- **容错机制**：处理信息缺失或格式异常的情况

### 图片处理
- **URL提取**：从Markdown格式中提取图片链接
- **加载优化**：图片加载失败时的优雅降级
- **尺寸适配**：统一的图片展示尺寸

### 链接匹配
- **智能关联**：自动匹配商品标题和购买链接
- **安全跳转**：新窗口打开购买页面
- **链接验证**：确保链接的有效性

## 📊 与原版对比

| 功能特性 | 原版app.py | 新版app_with_images.py |
|----------|------------|-------------------------|
| 商品图片 | ❌ 无图片 | ✅ 高清图片展示 |
| 界面设计 | ⚠️ 基础文本 | ✅ 现代卡片设计 |
| 价格展示 | ⚠️ 普通文本 | ✅ 醒目价格标签 |
| 购买链接 | ⚠️ 文本链接 | ✅ 按钮式链接 |
| 移动适配 | ⚠️ 基础响应 | ✅ 完全响应式 |
| 用户体验 | ⚠️ 一般 | ✅ 优秀 |

## 🛠️ 自定义配置

### 修改端口
```python
# 在app_with_images.py最后修改
demo.launch(server_port=8080)  # 改为其他端口
```

### 调整图片尺寸
```python
# 在parse_and_display_products函数中修改
width: 300px;  # 调整宽度
height: 300px; # 调整高度
```

### 自定义主题色
```python
# 修改CSS中的颜色值
background: #your-color;  # 自定义背景色
color: #your-color;       # 自定义文字色
```

## 🐛 常见问题

### Q: 图片不显示怎么办？
A: 
1. 检查网络连接
2. 确认图片URL有效
3. 查看浏览器控制台是否有错误

### Q: 界面布局异常？
A:
1. 清除浏览器缓存
2. 尝试刷新页面
3. 检查浏览器兼容性

### Q: 搜索没有结果？
A:
1. 检查API配置
2. 确认网络连接
3. 查看控制台日志

### Q: 购买链接无法打开？
A:
1. 确认链接有效性
2. 检查浏览器弹窗拦截
3. 尝试复制链接手动打开

## 📞 技术支持

如遇到问题，请检查：
1. **Python环境**：确保Python 3.8+
2. **依赖包**：确保gradio等包已安装
3. **API配置**：检查OpenAI API密钥
4. **网络连接**：确保网络畅通

---

🎉 **享受全新的商品搜索体验！**
