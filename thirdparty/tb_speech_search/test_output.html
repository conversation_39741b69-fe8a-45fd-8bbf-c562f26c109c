
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>商品展示测试</title>
            </head>
            <body>
                
    <div style="padding: 20px;">
        <h2 style="color: #1890ff; margin-bottom: 25px; text-align: center;">🛍️ 商品推荐结果</h2>
    
        <div style="
            border: 1px solid #e1e5e9; 
            border-radius: 12px; 
            padding: 20px; 
            margin-bottom: 25px; 
            background: white; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <div style="flex-shrink: 0;">
                    
                    <img src="http://g.search.alicdn.com/img/bao/uploaded/i4/i3/2218639436989/O1CN01oBt6yQ21V19zYgrxE_!!2218639436989.jpg" 
                         style="
                             width: 220px; 
                             height: 220px; 
                             object-fit: cover; 
                             border-radius: 10px; 
                             border: 2px solid #f0f0f0;
                             box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                         " 
                         alt="商品图片"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                    <div style="
                        width: 220px; 
                        height: 220px; 
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); 
                        border-radius: 10px; 
                        display: none; 
                        align-items: center; 
                        justify-content: center; 
                        color: #999;
                        font-size: 14px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 24px; margin-bottom: 8px;">📷</div>
                            暂无图片
                        </div>
                    </div>
                    
                </div>
                <div style="flex: 1; min-width: 0;">
                    <h3 style="
                        color: #2c3e50; 
                        margin: 0 0 15px 0; 
                        font-size: 20px; 
                        font-weight: 600;
                        line-height: 1.4;
                        word-wrap: break-word;
                    ">iQOO Z10 Turbo</h3>
                    
                    <div style="margin-bottom: 15px;">
                        <span style="
                            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); 
                            color: white; 
                            padding: 8px 16px; 
                            border-radius: 20px; 
                            font-weight: bold; 
                            font-size: 18px;
                            box-shadow: 0 2px 4px rgba(238, 90, 36, 0.3);
                        ">💰 ¥1999.00</span>
                    </div>
                    
                    <div style="color: #555; margin: 12px 0; line-height: 1.5;"><strong>🔧 12GB+512GB, 144Hz屏幕刷新率, 5000万像素相机, 天玑8400 CPU, 7620mAh大电池</strong></div>
                    
                    <div style="color: #666; margin: 10px 0;">
                        <span style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                            🏪 优力国货商贸
                        </span>
                    </div>
                    
                    
                    <a href="http://a.m.taobao.com/i924724901284.htm" 
                       target="_blank" 
                       style="
                           display: inline-block; 
                           background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); 
                           color: white; 
                           padding: 12px 24px; 
                           text-decoration: none; 
                           border-radius: 25px; 
                           margin-top: 15px;
                           font-weight: 500;
                           box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
                           transition: all 0.3s ease;
                       "
                       onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 6px 16px rgba(24, 144, 255, 0.4)'"
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(24, 144, 255, 0.3)'">
                        🛒 立即购买
                    </a>
                    
                </div>
            </div>
        </div>
        
        <div style="
            border: 1px solid #e1e5e9; 
            border-radius: 12px; 
            padding: 20px; 
            margin-bottom: 25px; 
            background: white; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <div style="flex-shrink: 0;">
                    
                    <img src="http://g.search2.alicdn.com/img/bao/uploaded/i4/i4/2218639436989/O1CN01rDYLd521V19yODvyv_!!2218639436989.jpg" 
                         style="
                             width: 220px; 
                             height: 220px; 
                             object-fit: cover; 
                             border-radius: 10px; 
                             border: 2px solid #f0f0f0;
                             box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                         " 
                         alt="商品图片"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                    <div style="
                        width: 220px; 
                        height: 220px; 
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); 
                        border-radius: 10px; 
                        display: none; 
                        align-items: center; 
                        justify-content: center; 
                        color: #999;
                        font-size: 14px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 24px; margin-bottom: 8px;">📷</div>
                            暂无图片
                        </div>
                    </div>
                    
                </div>
                <div style="flex: 1; min-width: 0;">
                    <h3 style="
                        color: #2c3e50; 
                        margin: 0 0 15px 0; 
                        font-size: 20px; 
                        font-weight: 600;
                        line-height: 1.4;
                        word-wrap: break-word;
                    ">iQOO Z10 Turbo 16+256G</h3>
                    
                    <div style="margin-bottom: 15px;">
                        <span style="
                            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); 
                            color: white; 
                            padding: 8px 16px; 
                            border-radius: 20px; 
                            font-weight: bold; 
                            font-size: 18px;
                            box-shadow: 0 2px 4px rgba(238, 90, 36, 0.3);
                        ">💰 ¥2199.00</span>
                    </div>
                    
                    <div style="color: #555; margin: 12px 0; line-height: 1.5;"><strong>🔧 16GB+256GB, 144Hz屏幕刷新率, 5000万像素相机, 天玑8400 CPU, 7620mAh大电池</strong></div>
                    
                    <div style="color: #666; margin: 10px 0;">
                        <span style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                            🏪 优力国货商贸
                        </span>
                    </div>
                    
                    
                    <a href="http://a.m.taobao.com/i924724901284.htm" 
                       target="_blank" 
                       style="
                           display: inline-block; 
                           background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); 
                           color: white; 
                           padding: 12px 24px; 
                           text-decoration: none; 
                           border-radius: 25px; 
                           margin-top: 15px;
                           font-weight: 500;
                           box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
                           transition: all 0.3s ease;
                       "
                       onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 6px 16px rgba(24, 144, 255, 0.4)'"
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(24, 144, 255, 0.3)'">
                        🛒 立即购买
                    </a>
                    
                </div>
            </div>
        </div>
        
    </div>
    <style>
        @media (max-width: 768px) {
            div[style*="display: flex"] {
                flex-direction: column !important;
            }
            img, div[style*="width: 220px"] {
                width: 100% !important;
                max-width: 300px !important;
                margin: 0 auto !important;
            }
        }
    </style>
    
            </body>
            </html>
            