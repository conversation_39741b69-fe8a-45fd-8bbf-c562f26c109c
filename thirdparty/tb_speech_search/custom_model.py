import os
import requests
import time

from openai.types.chat import Chat<PERSON><PERSON>pletion

from smolagents import LiteLLMModel
from smolagents.models import ChatMessage
from smolagents.monitoring import TokenUsage
from smolagents.tools import Tool



class CustomModel(LiteLLMModel):
    def __init__(self, 
                 api_base, 
                 api_key, 
                 model_id, 
                 custom_role_conversions, 
                 flatten_messages_as_text=None, 
                 **kwargs):
        super().__init__(
            model_id=model_id,
            api_base=api_base,
            api_key=api_key,
            custom_role_conversions=custom_role_conversions,
            flatten_messages_as_text=flatten_messages_as_text,
            **kwargs
        )
        self.api_base = api_base
        self.api_key = api_key
        self.model_id = model_id

    def generate(
        self,
        messages: list[dict[str, str | list[dict]] | ChatMessage],
        stop_sequences: list[str] | None = None,
        response_format: dict[str, str] | None = None,
        tools_to_call_from: list[Tool] | None = None,
        **kwargs,
    ) -> ChatMessage:
        kwargs['flatten_messages_as_text'] = True
        completion_kwargs = self._prepare_completion_kwargs(
            messages=messages,
            stop_sequences=stop_sequences,
            response_format=response_format,
            tools_to_call_from=tools_to_call_from,
            model=self.model_id,
            api_base=self.api_base,
            api_key=self.api_key,
            convert_images_to_image_urls=True,
            custom_role_conversions=self.custom_role_conversions,
            **kwargs,
        )
        messages = completion_kwargs['messages']

        if self.api_base.endswith('completions'):
            url  = self.api_base
        else:
            url = f"{self.api_base}/openai/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "messages": messages,
            "model": self.model_id
        }
        
        resp = requests.post(url, json=payload, headers=headers)
        resp.raise_for_status()
        # Assuming the response follows OpenAI's format
        resp_json = resp.json()
        resp_json['id'] = str(time.time())
        resp_json['created'] = int(time.time())
        resp_json['model'] = self.model_id
        resp_json['object'] = 'chat.completion'
        response = ChatCompletion.model_validate(resp_json)
        # return resp.json()["choices"][0]["message"]["content"]
        self._last_input_token_count = response.usage.prompt_tokens
        self._last_output_token_count = response.usage.completion_tokens
        print(response.choices[0].message)
        return ChatMessage.from_dict(
            response.choices[0].message.model_dump(include={"role", "content", "tool_calls"}),
            raw=resp_json,
            token_usage=TokenUsage(
                input_tokens=response.usage.prompt_tokens,
                output_tokens=response.usage.completion_tokens,
            ),
        )

    

if __name__ == "__main__":
    # 示例用法
    api_base = os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api")
    api_key = os.getenv("AZURE_OPENAI_API_KEY", "27db1fc058c1861870be4c21a7f93cdc")
    model_id = os.getenv("AZURE_OPENAI_MODEL_NAME", "gpt-4o-0806")  # "gpt-4-0409"
    model = CustomModel(api_base, api_key, model_id)
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "你好，请用一句话介绍你自己。"}
    ]
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "你好，请用一句话介绍你自己。"},
        {"role": "assistant", "content": "我是gpt大模型"},
        {"role": "user", "content": "北京是哪个国家的首都"}
    ]
    messages = [
        {
            "role": "system",
            "content": [
                {
                    "type": "text",
                    "text": "You are an expert assistant who can solve any task using code blobs. You will be given a task to solve as best you can.\nTo do so, you have been given access to a list of tools: these tools are basically Python functions which you can call with code.\nTo solve the task, you must plan forward to proceed in a series of steps, in a cycle of 'Thought:', 'Code:', and 'Observation:' sequences.\n\nAt each step, in the 'Thought:' sequence, you should first explain your reasoning towards solving the task and the tools that you want to use.\nThen in the 'Code:' sequence, you should write the code in simple Python. The code sequence must end with '<end_code>' sequence.\nDuring each intermediate step, you can use 'print()' to save whatever important information you will then need.\nThese print outputs will then appear in the 'Observation:' field, which will be available as input for the next step.\nIn the end you have to return a final answer using the `final_answer` tool.\n\nHere are a few examples using notional tools:\n---\nTask: \"Generate an image of the oldest person in this document.\"\n\nThought: I will proceed step by step and use the following tools: `document_qa` to find the oldest person in the document, then `image_generator` to generate an image according to the answer.\nCode:\n```py\nanswer = document_qa(document=document, question=\"Who is the oldest person mentioned?\")\nprint(answer)\n```<end_code>\nObservation: \"The oldest person in the document is John Doe, a 55 year old lumberjack living in Newfoundland.\"\n\nThought: I will now generate an image showcasing the oldest person.\nCode:\n```py\nimage = image_generator(\"A portrait of John Doe, a 55-year-old man living in Canada.\")\nfinal_answer(image)\n```<end_code>\n\n---\nTask: \"What is the result of the following operation: 5 + 3 + 1294.678?\"\n\nThought: I will use python code to compute the result of the operation and then return the final answer using the `final_answer` tool\nCode:\n```py\nresult = 5 + 3 + 1294.678\nfinal_answer(result)\n```<end_code>\n\n---\nTask:\n\"Answer the question in the variable `question` about the image stored in the variable `image`. The question is in French.\nYou have been provided with these additional arguments, that you can access using the keys as variables in your python code:\n{'question': 'Quel est l'animal sur l'image?', 'image': 'path/to/image.jpg'}\"\n\nThought: I will use the following tools: `translator` to translate the question into English and then `image_qa` to answer the question on the input image.\nCode:\n```py\ntranslated_question = translator(question=question, src_lang=\"French\", tgt_lang=\"English\")\nprint(f\"The translated question is {translated_question}.\")\nanswer = image_qa(image=image, question=translated_question)\nfinal_answer(f\"The answer is {answer}\")\n```<end_code>\n\n---\nTask:\nIn a 1979 interview, Stanislaus Ulam discusses with Martin Sherwin about other great physicists of his time, including Oppenheimer.\nWhat does he say was the consequence of Einstein learning too much math on his creativity, in one word?\n\nThought: I need to find and read the 1979 interview of Stanislaus Ulam with Martin Sherwin.\nCode:\n```py\npages = search(query=\"1979 interview Stanislaus Ulam Martin Sherwin physicists Einstein\")\nprint(pages)\n```<end_code>\nObservation:\nNo result found for query \"1979 interview Stanislaus Ulam Martin Sherwin physicists Einstein\".\n\nThought: The query was maybe too restrictive and did not find any results. Let's try again with a broader query.\nCode:\n```py\npages = search(query=\"1979 interview Stanislaus Ulam\")\nprint(pages)\n```<end_code>\nObservation:\nFound 6 pages:\n[Stanislaus Ulam 1979 interview](https://ahf.nuclearmuseum.org/voices/oral-histories/stanislaus-ulams-interview-1979/)\n\n[Ulam discusses Manhattan Project](https://ahf.nuclearmuseum.org/manhattan-project/ulam-manhattan-project/)\n\n(truncated)\n\nThought: I will read the first 2 pages to know more.\nCode:\n```py\nfor url in [\"https://ahf.nuclearmuseum.org/voices/oral-histories/stanislaus-ulams-interview-1979/\", \"https://ahf.nuclearmuseum.org/manhattan-project/ulam-manhattan-project/\"]:\n    whole_page = visit_webpage(url)\n    print(whole_page)\n    print(\"\\n\" + \"=\"*80 + \"\\n\")  # Print separator between pages\n```<end_code>\nObservation:\nManhattan Project Locations:\nLos Alamos, NM\nStanislaus Ulam was a Polish-American mathematician. He worked on the Manhattan Project at Los Alamos and later helped design the hydrogen bomb. In this interview, he discusses his work at\n(truncated)\n\nThought: I now have the final answer: from the webpages visited, Stanislaus Ulam says of Einstein: \"He learned too much mathematics and sort of diminished, it seems to me personally, it seems to me his purely physics creativity.\" Let's answer in one word.\nCode:\n```py\nfinal_answer(\"diminished\")\n```<end_code>\n\n---\nTask: \"Which city has the highest population: Guangzhou or Shanghai?\"\n\nThought: I need to get the populations for both cities and compare them: I will use the tool `search` to get the population of both cities.\nCode:\n```py\nfor city in [\"Guangzhou\", \"Shanghai\"]:\n    print(f\"Population {city}:\", search(f\"{city} population\")\n```<end_code>\nObservation:\nPopulation Guangzhou: ['Guangzhou has a population of 15 million inhabitants as of 2021.']\nPopulation Shanghai: '26 million (2019)'\n\nThought: Now I know that Shanghai has the highest population.\nCode:\n```py\nfinal_answer(\"Shanghai\")\n```<end_code>\n\n---\nTask: \"What is the current age of the pope, raised to the power 0.36?\"\n\nThought: I will use the tool `wiki` to get the age of the pope, and confirm that with a web search.\nCode:\n```py\npope_age_wiki = wiki(query=\"current pope age\")\nprint(\"Pope age as per wikipedia:\", pope_age_wiki)\npope_age_search = web_search(query=\"current pope age\")\nprint(\"Pope age as per google search:\", pope_age_search)\n```<end_code>\nObservation:\nPope age: \"The pope Francis is currently 88 years old.\"\n\nThought: I know that the pope is 88 years old. Let's compute the result using python code.\nCode:\n```py\npope_current_age = 88 ** 0.36\nfinal_answer(pope_current_age)\n```<end_code>\n\nAbove example were using notional tools that might not exist for you. On top of performing computations in the Python code snippets that you create, you only have access to these tools, behaving like regular python functions:\n```python\ndef visualizer(image_path: string, question: string) -> string:\n    \"\"\"A tool that can answer questions about attached images.\n\n    Args:\n        image_path: The path to the image on which to answer the question. This should be a local path to downloaded image.\n        question: The question to answer.\n    \"\"\"\n\ndef inspect_file_as_text(file_path: string, question: string) -> string:\n    \"\"\"\nYou cannot load files yourself: instead call this tool to read a file as markdown text and ask questions about it.\nThis tool handles the following file extensions: [\".html\", \".htm\", \".xlsx\", \".pptx\", \".wav\", \".mp3\", \".m4a\", \".flac\", \".pdf\", \".docx\"], and all other types of text files. IT DOES NOT HANDLE IMAGES.\n\n    Args:\n        file_path: The path to the file you want to read as text. Must be a '.something' file, like '.pdf'. If it is an image, use the visualizer tool instead! DO NOT use this tool for an HTML webpage: use the web_search tool instead!\n        question: [Optional]: Your question, as a natural language sentence. Provide as much context as possible. Do not pass this parameter if you just want to directly return the content of the file.\n    \"\"\"\n\ndef final_answer(answer: any) -> any:\n    \"\"\"Provides a final answer to the given problem.\n\n    Args:\n        answer: The final answer to the problem\n    \"\"\"\n\n```\nYou can also give tasks to team members.\nCalling a team member works the same as for calling a tool: simply, the only argument you can give in the call is 'task'.\nGiven that this team member is a real human, you should be very verbose in your task, it should be a long string providing informations as detailed as necessary.\nHere is a list of the team members that you can call:\n```python\ndef search_agent(\"Your query goes here.\") -> str:\n    \"\"\"A team member that will search the internet to answer your question.\n    Ask him for all your questions that require browsing the web.\n    Provide him as much context as possible, in particular if you need to search on a specific timeframe!\n    And don't hesitate to provide him with a complex search task, like finding a difference between two webpages.\n    Your request must be a real sentence, not a google search! Like \"Find me this information (...)\" rather than a few keywords.\n    \"\"\"\n\n```\n\nHere are the rules you should always follow to solve your task:\n1. Always provide a 'Thought:' sequence, and a 'Code:\\n```py' sequence ending with '```<end_code>' sequence, else you will fail.\n2. Use only variables that you have defined!\n3. Always use the right arguments for the tools. DO NOT pass the arguments as a dict as in 'answer = wiki({'query': \"What is the place where James Bond lives?\"})', but use the arguments directly as in 'answer = wiki(query=\"What is the place where James Bond lives?\")'.\n4. Take care to not chain too many sequential tool calls in the same code block, especially when the output format is unpredictable. For instance, a call to search has an unpredictable return format, so do not have another tool call that depends on its output in the same block: rather output results with print() to use them in the next block.\n5. Call a tool only when needed, and never re-do a tool call that you previously did with the exact same parameters.\n6. Don't name any new variable with the same name as a tool: for instance don't name a variable 'final_answer'.\n7. Never create any notional variables in our code, as having these in your logs will derail you from the true variables.\n8. You can use imports in your code, but only from the following list of modules: You can import from any package you want.\n9. The state persists between code executions: so if in one step you've created variables or imported modules, these will all persist.\n10. Don't give up! You're in charge of solving the task, not providing directions to solve it.\n\nNow Begin!"
                }
            ]
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "New task:\n夏天要穿什么衣服"
                }
            ]
        },
        {
            "role": "assistant",
            "content": [
                {
                    "type": "text",
                    "text": "Here are the facts I know and the plan of action that I will follow to solve the task:\n```\n## 1. Facts survey\n### 1.1. Facts given in the task\nThe task presents a sentence in Chinese: \"夏天要穿什么衣服\" which translates to \"What clothes to wear in summer?\"\n\n### 1.2. Facts to look up\n- Information on typical weather conditions during summer months in various regions to determine appropriate clothing.\n  - Source: Search agent can be used to look up weather conditions on trusted weather forecast and climate websites.\n- Options and suggestions for summer clothing based on fashion trends and practical considerations.\n  - Source: Search agent can be deployed to gather information from fashion websites, blogs, and articles.\n\n### 1.3. Facts to derive\n- From the weather conditions and clothing options, deduce the ideal types of clothing suitable for summer in different regions.\n\n## 2. Plan\n1. Utilize the search agent to find information on typical weather conditions during summer months. The query could be \"What are the typical weather conditions in various regions during summer months?\"\n2. Deploy the search agent again to look for suggestions on what clothes to wear in the summer, keeping in mind fashion trends and practical needs. The question could be \"What are the recommended clothes to wear in summer according to the latest fashion trends and practical considerations?\"\n3. Once the information from both queries is obtained, analyze the results to compile a comprehensive list of suitable summer clothing options based on the typical weather conditions in different regions.\n4. Conclude with a final answer that summarizes the ideal clothing choices for summer based on the data collected and analyzed.\n\n<end_plan>\n```"
                }
            ]
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Now proceed and carry out this plan."
                }
            ]
        }
    ]
    # messages = [
    #     {
    #         "role": "system",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "你是一个AI助手"
    #             }
    #         ]
    #     },
    #     {
    #         "role": "user",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "你好"
    #             }
    #         ]
    #     },
    #     {
    #         "role": "assistant",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "我是AI助手，可以回答问题"
    #             }
    #         ]
    #     },
    #     {
    #         "role": "user",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "中国的首都是哪儿"
    #             }
    #         ]
    #     }
    # ]
    # messages = [
    #     {
    #         "role": "user",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "中国的首都是哪儿"
    #             }
    #         ]
    #     }
    # ]
    # messages = [
    #     {
    #         "role": "system",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "你是AI助手，可以回答任何问题。"
    #             }
    #         ]
    #     },
    #     {
    #         "role": "user",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "西瓜的原产地是哪里"
    #             }
    #         ]
    #     }
    # ]
    # messages = [
    #     {
    #         "role": "system",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "你是AI助手，可以回答任何问题。"
    #             }
    #         ]
    #     }
    # ]
    # messages = [
    #     {
    #         "role": "user",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "图片里面有什么？"
    #             },
    #             {
    #                 "type": "image_url",
    #                 "image_url": {
    #                     "url": "https://idealab-platform.oss-accelerate.aliyuncs.com/20231125/33808fdb-10ad-428c-8a76-11532ad93b15_idealab2.png?Expires=4102329600&OSSAccessKeyId=LTAI5tFJF3QLwHzEmkhLs9dB&Signature=YcbZrM98pHRRMd%2BGFaI2OiFf8Z8%3D",
    #                     "detail": "high"
    #                 }
    #             }
    #         ]
    #     },
    #     {
    #         "role": "assistant",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "这张图片展示了一个标志，其中有“ideaLAB”这个词。字母“i”的上方有一个像素风格的火焰图案。整个图案使用蓝色和橙色的颜色搭配。"
    #             }
    #         ]
    #     },
    #     {
    #         "role": "user",
    #         "content": [
    #             {
    #                 "type": "text",
    #                 "text": "更加详细的描述这张图片"
    #             },
    #         ]
    #     }
    # ]



    result = model.generate(messages)
    print("模型输出:", result)
