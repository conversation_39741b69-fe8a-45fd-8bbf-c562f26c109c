#!/usr/bin/env python3
"""
测试增强后的SpeechSearchAgent，验证是否会自动调用TaobaoItemDetailsTool
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run import create_agent, create_intelligent_agent

def test_enhanced_agent():
    """测试增强后的agent"""
    print("🧪 测试增强后的SpeechSearchAgent...")
    
    # 创建agent
    print("\n📋 创建智能搜索agent...")
    agent = create_intelligent_agent(model_id="gpt-4-0409")
    
    # 测试查询
    test_query = "适合夏天穿的跑鞋"
    print(f"\n🔍 测试查询: {test_query}")
    
    try:
        # 运行agent
        result = agent.run(test_query)
        print(f"\n✅ Agent执行完成")
        print(f"📄 最终结果: {result}")
        
        # 检查是否有待处理的商品ID
        if hasattr(agent, '_pending_item_ids'):
            print(f"\n📦 待处理商品ID: {agent._pending_item_ids}")
            if agent.should_call_item_details():
                print("⚠️ 还有未处理的商品ID，可能需要手动调用详情工具")
            else:
                print("✅ 所有商品ID已处理完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_simple_search():
    """测试简单的商品搜索"""
    print("\n🧪 测试简单商品搜索...")
    
    # 创建传统agent
    print("\n📋 创建传统ReAct agent...")
    agent = create_agent(model_id="gpt-4-0409")
    
    # 测试查询
    test_query = "运动鞋"
    print(f"\n🔍 测试查询: {test_query}")
    
    try:
        # 运行agent
        result = agent.run(test_query)
        print(f"\n✅ Agent执行完成")
        print(f"📄 最终结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试增强后的Agent...")
    
    # 测试智能搜索agent
    test_enhanced_agent()
    
    print("\n" + "="*50)
    
    # 测试传统agent
    test_simple_search()
    
    print("\n🎉 测试完成！")
