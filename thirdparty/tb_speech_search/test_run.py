#!/usr/bin/env python3
"""
测试 run.py 的基本功能
"""

def test_react_agent():
    """测试传统ReAct agent"""
    print("测试传统ReAct agent...")
    
    try:
        from run import create_agent
        print("✓ create_agent 导入成功")
        
        # 不实际创建agent，只测试导入
        print("✓ 传统ReAct agent 测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 传统ReAct agent 测试失败: {e}")
        return False

def test_intelligent_agent():
    """测试智能搜索agent"""
    print("\n测试智能搜索agent...")
    
    try:
        from run import create_intelligent_agent
        print("✓ create_intelligent_agent 导入成功")
        
        # 不实际创建agent，只测试导入
        print("✓ 智能搜索agent 测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 智能搜索agent 测试失败: {e}")
        return False

def test_argument_parsing():
    """测试参数解析"""
    print("\n测试参数解析...")
    
    try:
        from run import parse_args
        
        # 模拟命令行参数
        import sys
        original_argv = sys.argv
        
        # 测试默认参数
        sys.argv = ['run.py']
        args = parse_args()
        print(f"✓ 默认参数: question='{args.question}', model_id='{args.model_id}', agent_type='{args.agent_type}'")
        
        # 测试自定义参数
        sys.argv = ['run.py', '--question', '测试查询', '--model-id', 'gpt-4-0409', '--agent-type', 'intelligent']
        args = parse_args()
        print(f"✓ 自定义参数: question='{args.question}', model_id='{args.model_id}', agent_type='{args.agent_type}'")
        
        # 恢复原始参数
        sys.argv = original_argv
        
        print("✓ 参数解析测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 参数解析测试失败: {e}")
        return False

def test_main_function():
    """测试主函数逻辑"""
    print("\n测试主函数逻辑...")
    
    try:
        # 测试主函数的导入和基本结构
        import run
        
        # 检查主要函数是否存在
        assert hasattr(run, 'main'), "main函数不存在"
        assert hasattr(run, 'create_agent'), "create_agent函数不存在"
        assert hasattr(run, 'create_intelligent_agent'), "create_intelligent_agent函数不存在"
        assert hasattr(run, 'parse_args'), "parse_args函数不存在"
        
        print("✓ 所有必要函数都存在")
        print("✓ 主函数逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 主函数逻辑测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试 run.py 的功能...")
    print("=" * 50)
    
    results = []
    
    # 运行各项测试
    results.append(test_argument_parsing())
    results.append(test_main_function())
    results.append(test_react_agent())
    results.append(test_intelligent_agent())
    
    print("\n" + "=" * 50)
    print("测试总结:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！run.py 功能正常。")
        print("\n可以使用以下命令运行:")
        print("  python run.py --agent-type react --question '您的查询'")
        print("  python run.py --agent-type intelligent --question '您的查询'")
    else:
        print("✗ 部分测试失败，需要检查相关功能。")

if __name__ == "__main__":
    main()
