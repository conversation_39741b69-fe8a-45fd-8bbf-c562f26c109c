#!/usr/bin/env python3
"""
调试工具调用问题的脚本
"""

print("🧪 测试工具调用...")
try:
    from custom_tools import QueryAnalysisTool
    from custom_model import CustomModel

    custom_role_conversions = {"tool-call": "assistant", "tool-response": "user"}
    model = CustomModel(
        model_id="gpt-4-0409",
        custom_role_conversions=custom_role_conversions,
        api_base="https://idealab.alibaba-inc.com/api",
        api_key="27db1fc058c1861870be4c21a7f93cdc"
    )

    tool = QueryAnalysisTool(model)
    result = tool.forward("春游要带什么")
    print(f"✓ forward() 调用成功: {result[:100]}...")
except Exception as e:
    print(f"✗ forward() 调用失败: {e}")
    import traceback
    traceback.print_exc()

print("🎯 调试完成")
