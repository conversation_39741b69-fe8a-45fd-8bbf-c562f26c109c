#!/usr/bin/env python3
"""
直接调试工具 - 绕过Web界面直接测试Agent
"""

import sys
import traceback
from interactive_agent import InteractiveSearchAgent
from run import create_intelligent_agent

def test_agent_creation():
    """测试Agent创建"""
    print("🧪 测试Agent创建...")
    
    try:
        # 创建基础agent
        base_agent = create_intelligent_agent()
        print("✓ 基础Agent创建成功")
        
        # 创建交互式agent
        agent = InteractiveSearchAgent(
            model=base_agent.model,
            tools=list(base_agent.tools.values()),
            max_steps=5,
            verbosity_level=1,
            name="debug_agent"
        )
        print("✓ 交互式Agent创建成功")
        
        return agent
        
    except Exception as e:
        print(f"✗ Agent创建失败: {e}")
        traceback.print_exc()
        return None

def test_query_analysis(agent, query="春游要买什么"):
    """测试查询分析"""
    print(f"\n🔍 测试查询分析: '{query}'")
    
    try:
        analysis = agent._analyze_query_clarity(query)
        print(f"✓ 查询分析成功:")
        print(f"   明确度分数: {analysis.get('query_clarity_score', 0):.2f}")
        print(f"   需要澄清: {analysis.get('needs_clarification', False)}")
        print(f"   模糊关键词数: {analysis.get('ambiguous_keywords', 0)}")
        
        return analysis
        
    except Exception as e:
        print(f"✗ 查询分析失败: {e}")
        traceback.print_exc()
        return None

def test_interaction_decision(agent, analysis):
    """测试交互决策"""
    print(f"\n🤔 测试交互决策...")
    
    try:
        interaction_context = {
            **analysis,
            'multiple_search_directions': False,
            'user_expects_interaction': '?' in "春游要买什么"
        }
        
        should_interact = agent._should_interact_with_user(interaction_context)
        print(f"✓ 交互决策成功:")
        print(f"   需要交互: {should_interact}")
        print(f"   交互上下文: {interaction_context}")
        
        return should_interact
        
    except Exception as e:
        print(f"✗ 交互决策失败: {e}")
        traceback.print_exc()
        return False

def test_interaction_generation(agent, analysis):
    """测试交互生成"""
    print(f"\n💬 测试交互生成...")
    
    try:
        if analysis.get('needs_clarification'):
            interaction = agent._generate_clarification_question(
                "春游要买什么", 
                analysis.get('analysis', {})
            )
            print(f"✓ 交互生成成功:")
            print(f"   交互ID: {interaction.interaction_id}")
            print(f"   交互类型: {interaction.interaction_type}")
            print(f"   消息: {interaction.message}")
            print(f"   选项: {interaction.options}")
            
            return interaction
        else:
            print("ℹ️  查询不需要澄清，跳过交互生成")
            return None
            
    except Exception as e:
        print(f"✗ 交互生成失败: {e}")
        traceback.print_exc()
        return None

def test_simple_search(agent, query="春游要买什么"):
    """测试简单搜索"""
    print(f"\n🔍 测试简单搜索: '{query}'")
    
    try:
        # 使用同步版本
        result = agent.run_interactive(query)
        print(f"✓ 搜索完成:")
        print(f"   结果长度: {len(result) if result else 0} 字符")
        print(f"   结果预览: {str(result)[:300]}...")
        
        return result
        
    except Exception as e:
        print(f"✗ 搜索失败: {e}")
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🐛 直接调试工具 - 交互式搜索Agent")
    print("=" * 60)
    
    # 测试Agent创建
    agent = test_agent_creation()
    if not agent:
        print("\n❌ Agent创建失败，无法继续测试")
        return
    
    # 测试查询分析
    analysis = test_query_analysis(agent)
    if not analysis:
        print("\n❌ 查询分析失败，无法继续测试")
        return
    
    # 测试交互决策
    should_interact = test_interaction_decision(agent, analysis)
    
    # 测试交互生成（如果需要）
    if should_interact:
        interaction = test_interaction_generation(agent, analysis)
    
    # 测试简单搜索
    result = test_simple_search(agent)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    tests = [
        ("Agent创建", agent is not None),
        ("查询分析", analysis is not None),
        ("交互决策", True),  # 这个测试总是成功
        ("简单搜索", result is not None)
    ]
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Agent工作正常")
        print("\n💡 现在可以在Web界面中测试:")
        print("   1. 访问 http://127.0.0.1:8000")
        print("   2. 输入 '春游要买什么'")
        print("   3. 应该不会再出现 'AgentLogger' 错误")
    else:
        print("⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
