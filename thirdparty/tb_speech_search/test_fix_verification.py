#!/usr/bin/env python3
"""
验证修复后的agent是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_query():
    """测试简单查询"""
    print("🧪 测试简单查询...")
    
    try:
        from run import create_enhanced_agent
        
        # 创建增强型agent
        print("📋 创建增强型agent...")
        agent = create_enhanced_agent(model_id="gpt-4-0409")
        print("✅ Agent创建成功")
        
        # 测试查询
        query = "手机"
        print(f"\n🔍 测试查询: {query}")
        
        # 运行agent
        result = agent.run(query)
        
        print(f"\n✅ 查询完成")
        print(f"📄 最终结果:")
        print(result)
        
        # 检查结果
        if isinstance(result, str):
            has_product_info = any(keyword in result.lower() for keyword in ['¥', '店铺', '付款', 'price', '商品'])
            if has_product_info:
                print("✅ 结果包含商品信息")
            else:
                print("⚠️ 结果可能缺少商品信息")
        
        # 检查agent状态
        print(f"\n📊 Agent状态:")
        print(f"  已搜索商品: {getattr(agent, '_has_searched_products', False)}")
        print(f"  已获取详情: {getattr(agent, '_has_detailed_info', False)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_recommendation_query():
    """测试推荐类查询"""
    print("\n🧪 测试推荐类查询...")
    
    try:
        from run import create_enhanced_agent
        
        # 创建增强型agent
        agent = create_enhanced_agent(model_id="gpt-4-0409")
        
        # 测试查询
        query = "学生用品推荐"
        print(f"\n🔍 测试查询: {query}")
        
        # 运行agent
        result = agent.run(query)
        
        print(f"\n✅ 查询完成")
        print(f"📄 结果长度: {len(result)} 字符")
        
        # 检查结果
        has_product_info = any(keyword in result.lower() for keyword in ['¥', '店铺', '付款', 'price', '商品'])
        if has_product_info:
            print("✅ 结果包含商品信息")
        else:
            print("❌ 结果缺少商品信息")
            print(f"结果内容: {result[:200]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始验证修复...")
    
    # 测试简单查询
    test_simple_query()
    
    # 测试推荐类查询
    test_recommendation_query()
    
    print("\n🎉 验证完成！")
