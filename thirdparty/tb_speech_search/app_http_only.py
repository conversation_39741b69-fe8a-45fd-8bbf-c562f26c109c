#!/usr/bin/env python3
"""
HTTP专用版本 - 跳过SSL，直接使用HTTP解决网络问题
"""

import os
import gradio as gr

# 设置环境变量避免网络问题
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['PYTHONHTTPSVERIFY'] = '0'

def search_products(query: str):
    """搜索商品"""
    if not query.strip():
        return "请输入搜索关键词", ""
    
    try:
        from run import create_enhanced_agent
        from app_with_images import parse_and_display_products
        
        # 创建agent并搜索
        agent = create_enhanced_agent()
        response = agent.run(query)
        
        # 生成带图片的HTML展示
        html_display = parse_and_display_products(response)
        
        return response, html_display
        
    except Exception as e:
        error_msg = f"搜索过程中出现错误: {str(e)}"
        error_html = f"""
        <div style="padding: 20px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 8px; color: #a8071a;">
            <h3 style="color: #cf1322; margin-bottom: 10px;">❌ 搜索失败</h3>
            <p>{error_msg}</p>
        </div>
        """
        return error_msg, error_html

# 创建界面
with gr.Blocks(title="淘宝商品搜索助手 (HTTP版)", theme=gr.themes.Soft()) as demo:
    gr.Markdown("""
    # 🛍️ 淘宝商品搜索助手 (HTTP版)
    
    🌐 **使用HTTP协议，避免SSL连接问题**
    
    输入您想要搜索的商品，AI助手将为您推荐相关商品，**包括商品图片展示**！
    """)
    
    with gr.Row():
        with gr.Column(scale=4):
            query_input = gr.Textbox(
                label="🔍 搜索关键词",
                placeholder="请输入您想要搜索的商品，如：2000左右的手机、运动鞋推荐等...",
                lines=2
            )
        with gr.Column(scale=1):
            search_btn = gr.Button("🚀 开始搜索", variant="primary", size="lg")
    
    with gr.Tabs():
        with gr.TabItem("🎨 商品展示 (带图片)"):
            product_display = gr.HTML(label="商品信息展示")
        
        with gr.TabItem("📝 原始回复"):
            raw_output = gr.Textbox(
                label="AI助手原始回复",
                lines=15,
                max_lines=25
            )
    
    # 绑定搜索事件
    search_btn.click(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    query_input.submit(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    # 示例查询
    gr.Examples(
        examples=[
            ["2000左右的手机"],
            ["运动鞋推荐"],
            ["秋天水果推荐"],
            ["无线蓝牙耳机"],
            ["学生用品推荐"]
        ],
        inputs=[query_input],
        label="💡 试试这些搜索示例"
    )

def main():
    """主函数"""
    print("🚀 启动HTTP版淘宝商品搜索助手...")
    print("=" * 60)
    print("🌐 使用HTTP协议，避免SSL连接问题")
    print("🔧 已设置环境变量避免代理冲突")
    
    # 尝试不同的HTTP配置
    http_configs = [
        {"port": 7860, "host": "127.0.0.1", "name": "主端口"},
        {"port": 7861, "host": "127.0.0.1", "name": "备用端口1"},
        {"port": 8080, "host": "127.0.0.1", "name": "备用端口2"},
        {"port": 8000, "host": "localhost", "name": "localhost端口"}
    ]
    
    for config in http_configs:
        try:
            print(f"\n🔄 尝试{config['name']}: {config['host']}:{config['port']}...")
            
            demo.launch(
                server_name=config["host"],
                server_port=config["port"],
                share=False,
                show_error=True,
                quiet=False,
                inbrowser=True,
                prevent_thread_lock=False
            )
            
            print(f"✅ HTTP启动成功！")
            print(f"🌐 访问地址: http://{config['host']}:{config['port']}")
            return
            
        except Exception as e:
            print(f"❌ {config['name']}失败: {e}")
            continue
    
    print("\n❌ 所有HTTP端口都启动失败")
    print("🛠️ 最后尝试：使用Gradio默认配置...")
    
    try:
        demo.launch()
        print("✅ 默认配置启动成功！")
    except Exception as e:
        print(f"❌ 默认配置也失败: {e}")
        print("\n🆘 所有启动方式都失败，请检查:")
        print("1. Python环境和Gradio版本")
        print("2. 系统网络配置")
        print("3. 防火墙设置")
        print("4. 端口占用情况")

if __name__ == "__main__":
    main()
