# 模仿smolagents/examples/open_deep_research/run.py，实现淘宝语音搜索商品功能
"""
主搜接口（替换{query}部分）：https://tppwork.taobao.com/center/recommend?appid=36935&code=aiSrp_itemSearchTool&_input_charset=UTF-8&_output_charset=UTF-8&searchType=recall&from=voiceTest&outputType=recall&userId=&closeItemRelevanceCheck=true&gotoOld=true&q={query}
"""

import argparse
import os
import threading

from dotenv import load_dotenv
from huggingface_hub import login
from scripts.text_inspector_tool import TextInspectorTool
from scripts.text_web_browser import (
    ArchiveSearchTool,
    FinderTool,
    FindNextTool,
    PageDownTool,
    PageUpTool,
    SimpleTextBrowser,
    VisitTool,
)
from scripts.visual_qa import visualizer

from smolagents import (
    CodeAgent,
    GoogleSearchTool,
    # InferenceClientModel,
    LiteLLMModel,
    ToolCallingAgent,
)
from custom_tools import TaobaoMainSearchTool, SearchSummaryTool, TaobaoItemDetailsTool
from custom_model import CustomModel
from custom_agent import SpeechSearchAgent

load_dotenv(override=True)
# login("*************************************")
# login(os.getenv("HF_TOKEN"))

append_answer_lock = threading.Lock()



def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--question", type=str, default="适合夏天穿的跑鞋"
    )
    parser.add_argument("--model-id", type=str, default="gpt-4-0409")
    parser.add_argument("--agent-type", type=str, default="enhanced",
                       choices=["enhanced", "intelligent", "react"],
                       help="选择agent类型: enhanced(增强搜索，自动获取详情) intelligent(智能搜索) 或 react(传统ReAct)")
    return parser.parse_args()

custom_role_conversions = {"tool-call": "assistant", "tool-response": "user"}

user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0"

BROWSER_CONFIG = {
    "viewport_size": 1024 * 5,
    "downloads_folder": "downloads_folder",
    "request_kwargs": {
        "headers": {"User-Agent": user_agent},
        "timeout": 300,
    },
}

os.makedirs(f"./{BROWSER_CONFIG['downloads_folder']}", exist_ok=True)

API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "27db1fc058c1861870be4c21a7f93cdc")
ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api")
MODEL_NAME = os.getenv("AZURE_OPENAI_MODEL_NAME", "gpt-4-0409")

def create_agent(model_id="gpt-4-0409"):
    model_params = {
        "model_id": model_id,
        "custom_role_conversions": custom_role_conversions,
        "max_completion_tokens": 8192,
        "api_base": ENDPOINT,
        "api_key": API_KEY,
    }
    if model_id == "o1":
        model_params["reasoning_effort"] = "high"
    elif model_id == "gpt-4-0409":
        model_params["reasoning_effort"] = "low"
    model = CustomModel(**model_params)

    text_limit = 100000
    browser = SimpleTextBrowser(**BROWSER_CONFIG)
    WEB_TOOLS = [
        GoogleSearchTool(provider="serper"),
        VisitTool(browser),
        PageUpTool(browser),
        PageDownTool(browser),
        FinderTool(browser),
        FindNextTool(browser),
        ArchiveSearchTool(browser),
        TextInspectorTool(model, text_limit),
        SearchSummaryTool(),
        TaobaoMainSearchTool(),
        TaobaoItemDetailsTool(),
    ]

    # TODO:ToolCallingAgent 是ReAct模式，需要自己写一个planner模式的
    agent = SpeechSearchAgent(
        model=model,
        tools=WEB_TOOLS,
        max_steps=10,
        verbosity_level=2,
        planning_interval=4,
        name="search_agent",
        description="""你是一个搜索agent，用于搜索通用信息和淘宝商品信息，请先搜索通用信息，再搜索淘宝商品信息，最后返回商品信息。
        如果用户的问题是关于淘宝商品的，请使用淘宝商品搜索工具，如果用户的问题是关于通用信息的，请使用通用信息搜索工具。
        如果用户觉得商品信息不准确，请使用淘宝商品搜索工具，重新搜索商品信息。""",
        provide_run_summary=True,
    )
    return agent


def create_intelligent_agent(model_id="gpt-4-0409"):
    """创建智能搜索agent（简化版本，基于现有的SpeechSearchAgent）"""
    # 首先设置SERPER_API_KEY环境变量
    serper_api_key = os.getenv("SERPER_API_KEY")
    if not serper_api_key:
        serper_api_key = "4b1915a54600d6c6a2d84742f0332346dbb0a6d6"
        os.environ["SERPER_API_KEY"] = serper_api_key
        print(f"🔑 设置SERPER_API_KEY: {serper_api_key[:10]}...")

    # 在函数内部导入，避免模块级别的导入错误
    from custom_tools import QueryAnalysisTool, UserQuestionTool, QueryEnhancementTool, WebSearchTool

    model_params = {
        "model_id": model_id,
        "custom_role_conversions": custom_role_conversions,
        "max_completion_tokens": 8192,
        "api_base": ENDPOINT,
        "api_key": API_KEY,
    }
    if model_id == "o1":
        model_params["reasoning_effort"] = "high"
    elif model_id == "gpt-4-0409":
        model_params["reasoning_effort"] = "low"
    model = CustomModel(**model_params)

    text_limit = 100000
    browser = SimpleTextBrowser(**BROWSER_CONFIG)

    # 为智能搜索agent准备工具
    INTELLIGENT_TOOLS = [
        QueryAnalysisTool(model),
        UserQuestionTool(model),
        QueryEnhancementTool(model),
        WebSearchTool(),
        SearchSummaryTool(),
        TaobaoMainSearchTool(),
        TaobaoItemDetailsTool(),
        VisitTool(browser),
        TextInspectorTool(model, text_limit),
    ]

    # 尝试添加GoogleSearchTool
    try:
        google_search_tool = GoogleSearchTool(provider="serper")
        INTELLIGENT_TOOLS.insert(3, google_search_tool)  # 插入到合适位置，替换自定义WebSearchTool
        print("✓ GoogleSearchTool 已添加")
    except Exception as e:
        print(f"⚠️  跳过GoogleSearchTool（错误: {e}）")
        # 如果GoogleSearchTool失败，保留自定义WebSearchTool

    # 使用现有的SpeechSearchAgent，使用默认提示模板以确保工具调用格式正确
    agent = SpeechSearchAgent(
        model=model,
        tools=INTELLIGENT_TOOLS,
        max_steps=8,
        verbosity_level=2,
        planning_interval=3,
        name="intelligent_search_agent",
        description="""智能电商搜索助手，能够：
        1. 分析用户查询的搜索意图
        2. 对于明确查询直接搜索商品
        3. 对于不明确查询智能反问或补全信息
        4. 提供精准的商品搜索结果

        优先使用query_analysis工具分析用户查询，然后根据分析结果选择合适的后续行动。""",
        provide_run_summary=True,
    )
    return agent


def create_enhanced_agent(model_id="gpt-4-0409"):
    """创建增强型搜索agent，确保获取商品ID后一定调用详情工具"""
    # 首先设置SERPER_API_KEY环境变量
    serper_api_key = os.getenv("SERPER_API_KEY")
    if not serper_api_key:
        serper_api_key = "4b1915a54600d6c6a2d84742f0332346dbb0a6d6"
        os.environ["SERPER_API_KEY"] = serper_api_key
        print(f"🔑 设置SERPER_API_KEY: {serper_api_key[:10]}...")

    model_params = {
        "model_id": model_id,
        "custom_role_conversions": custom_role_conversions,
        "max_completion_tokens": 8192,
        "api_base": ENDPOINT,
        "api_key": API_KEY,
    }
    if model_id == "o1":
        model_params["reasoning_effort"] = "high"
    elif model_id == "gpt-4-0409":
        model_params["reasoning_effort"] = "low"
    model = CustomModel(**model_params)

    text_limit = 100000
    browser = SimpleTextBrowser(**BROWSER_CONFIG)

    # 为增强搜索agent准备工具
    ENHANCED_TOOLS = [
        SearchSummaryTool(),
        TaobaoMainSearchTool(),
        TaobaoItemDetailsTool(),
        VisitTool(browser),
        TextInspectorTool(model, text_limit),
    ]

    # 尝试添加GoogleSearchTool
    try:
        google_search_tool = GoogleSearchTool(provider="serper")
        ENHANCED_TOOLS.insert(0, google_search_tool)
        print("✓ GoogleSearchTool 已添加")
    except Exception as e:
        print(f"⚠️  跳过GoogleSearchTool（错误: {e}）")

    # 使用增强型提示模板
    import yaml
    enhanced_prompt_templates = yaml.safe_load(
        open('prompts/enhanced_search_agent.yaml').read()
    )

    # 使用SpeechSearchAgent，但使用增强型提示模板
    agent = SpeechSearchAgent(
        model=model,
        tools=ENHANCED_TOOLS,
        prompt_templates=enhanced_prompt_templates,
        max_steps=6,
        verbosity_level=2,
        planning_interval=2,
        name="enhanced_search_agent",
        description="""增强型电商搜索助手，确保：
        1. 获取商品ID后立即调用详情工具补全信息
        2. 提供包含图片、店铺、销量等详细信息的完整结果
        3. 用户友好的商品推荐和说明""",
        provide_run_summary=True,
    )
    return agent


def main():
    args = parse_args()

    if args.agent_type == "enhanced":
        agent = create_enhanced_agent(model_id=args.model_id)
        print("🚀 使用增强型搜索Agent（自动获取商品详情）")
    elif args.agent_type == "intelligent":
        agent = create_intelligent_agent(model_id=args.model_id)
        print("🧠 使用智能搜索Agent")
    else:
        agent = create_agent(model_id=args.model_id)
        print("⚙️ 使用传统ReAct Agent")

    print(f"📝 处理查询: {args.question}")
    answer = agent.run(args.question)

    print(f"\n✅ 最终答案: {answer}")

if __name__ == "__main__":
    main()
    
    