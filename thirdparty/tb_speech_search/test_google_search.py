#!/usr/bin/env python3
"""
测试GoogleSearchTool的创建
"""

import os
from smolagents import GoogleSearchTool


def test_google_search_tool():
    """测试GoogleSearchTool创建"""
    
    print("测试GoogleSearchTool创建...")
    
    # 1. 检查环境变量
    print(f"1. 检查环境变量:")
    serper_key = os.getenv("SERPER_API_KEY")
    print(f"   SERPER_API_KEY: {serper_key[:10] + '...' if serper_key else 'None'}")
    
    # 2. 设置API密钥
    if not serper_key:
        serper_key = "4b1915a54600d6c6a2d84742f0332346dbb0a6d6"
        os.environ["SERPER_API_KEY"] = serper_key
        print(f"   设置SERPER_API_KEY: {serper_key[:10]}...")
    
    # 3. 尝试创建GoogleSearchTool
    try:
        print(f"2. 尝试创建GoogleSearchTool...")
        google_search_tool = GoogleSearchTool(provider="serper")
        print(f"   ✅ 成功创建GoogleSearchTool")
        print(f"   工具名称: {google_search_tool.name}")
        print(f"   工具描述: {google_search_tool.description}")
        
        # 4. 测试搜索功能
        print(f"3. 测试搜索功能...")
        try:
            result = google_search_tool.forward("test search")
            print(f"   ✅ 搜索测试成功")
            print(f"   结果长度: {len(result)} 字符")
            return True
        except Exception as e:
            print(f"   ❌ 搜索测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ 创建GoogleSearchTool失败: {e}")
        print(f"   错误类型: {type(e)}")
        return False


if __name__ == "__main__":
    success = test_google_search_tool()
    if success:
        print("\n🎉 GoogleSearchTool测试成功！")
    else:
        print("\n💥 GoogleSearchTool测试失败！")
