#!/usr/bin/env python3
"""
验证修复是否有效的简单脚本
"""

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        # 测试参数解析
        from run import parse_args
        print("✓ parse_args 导入成功")
        
        # 测试参数解析功能
        import sys
        original_argv = sys.argv
        sys.argv = ['run.py', '--agent-type', 'react']
        args = parse_args()
        print(f"✓ react模式参数解析成功: {args.agent_type}")
        
        sys.argv = ['run.py', '--agent-type', 'intelligent']
        args = parse_args()
        print(f"✓ intelligent模式参数解析成功: {args.agent_type}")
        
        sys.argv = original_argv
        return True
        
    except Exception as e:
        print(f"✗ 基本导入失败: {e}")
        return False

def test_agent_type_selection():
    """测试agent类型选择逻辑"""
    print("\n测试agent类型选择逻辑...")
    
    try:
        # 模拟main函数的逻辑
        from run import parse_args
        import sys
        
        original_argv = sys.argv
        
        # 测试react类型
        sys.argv = ['run.py', '--agent-type', 'react']
        args = parse_args()
        
        if args.agent_type == "intelligent":
            print("✓ intelligent模式将被选择")
        else:
            print("✓ react模式将被选择")
        
        # 测试intelligent类型
        sys.argv = ['run.py', '--agent-type', 'intelligent']
        args = parse_args()
        
        if args.agent_type == "intelligent":
            print("✓ intelligent模式将被选择")
        else:
            print("✓ react模式将被选择")
        
        sys.argv = original_argv
        return True
        
    except Exception as e:
        print(f"✗ agent类型选择测试失败: {e}")
        return False

def main():
    """运行验证"""
    print("验证修复效果")
    print("=" * 40)
    
    results = []
    results.append(test_basic_imports())
    results.append(test_agent_type_selection())
    
    print("\n" + "=" * 40)
    print("验证结果:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ 所有验证通过 ({passed}/{total})")
        print("\n修复成功！现在可以使用:")
        print("  python run.py --agent-type react --question '您的查询'")
        print("  python run.py --agent-type intelligent --question '您的查询'")
    else:
        print(f"✗ 部分验证失败 ({passed}/{total})")

if __name__ == "__main__":
    main()
