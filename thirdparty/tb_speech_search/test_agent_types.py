#!/usr/bin/env python3
"""
测试两种agent类型的基本功能
"""

import sys
import os

def test_agent_creation():
    """测试agent创建功能"""
    print("测试Agent创建功能...")
    print("=" * 50)
    
    # 测试传统ReAct agent
    print("\n1. 测试传统ReAct Agent创建:")
    try:
        from run import create_agent
        print("   ✓ create_agent 函数导入成功")
        
        # 注意：这里不实际创建agent，因为需要API密钥
        print("   ✓ 传统ReAct Agent 创建函数可用")
        
    except Exception as e:
        print(f"   ✗ 传统ReAct Agent 创建失败: {e}")
        return False
    
    # 测试智能搜索agent
    print("\n2. 测试智能搜索Agent创建:")
    try:
        from run import create_intelligent_agent
        print("   ✓ create_intelligent_agent 函数导入成功")
        
        # 注意：这里不实际创建agent，因为需要API密钥
        print("   ✓ 智能搜索Agent 创建函数可用")
        
    except Exception as e:
        print(f"   ✗ 智能搜索Agent 创建失败: {e}")
        return False
    
    return True

def test_command_line_interface():
    """测试命令行接口"""
    print("\n\n测试命令行接口...")
    print("=" * 50)
    
    # 测试参数解析
    try:
        from run import parse_args
        
        # 保存原始命令行参数
        original_argv = sys.argv
        
        # 测试react模式
        print("\n1. 测试react模式参数:")
        sys.argv = ['run.py', '--agent-type', 'react', '--question', '测试查询']
        args = parse_args()
        print(f"   ✓ agent_type: {args.agent_type}")
        print(f"   ✓ question: {args.question}")
        
        # 测试intelligent模式
        print("\n2. 测试intelligent模式参数:")
        sys.argv = ['run.py', '--agent-type', 'intelligent', '--question', '智能测试']
        args = parse_args()
        print(f"   ✓ agent_type: {args.agent_type}")
        print(f"   ✓ question: {args.question}")
        
        # 恢复原始参数
        sys.argv = original_argv
        
        print("\n   ✓ 命令行接口测试通过")
        return True
        
    except Exception as e:
        print(f"   ✗ 命令行接口测试失败: {e}")
        return False

def test_main_logic():
    """测试主逻辑"""
    print("\n\n测试主逻辑...")
    print("=" * 50)
    
    try:
        import run
        
        # 检查main函数存在
        assert hasattr(run, 'main'), "main函数不存在"
        print("   ✓ main函数存在")
        
        # 检查所有必要的函数都存在
        required_functions = [
            'parse_args',
            'create_agent', 
            'create_intelligent_agent',
            'main'
        ]
        
        for func_name in required_functions:
            assert hasattr(run, func_name), f"{func_name}函数不存在"
            print(f"   ✓ {func_name}函数存在")
        
        print("\n   ✓ 主逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"   ✗ 主逻辑测试失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n\n使用示例...")
    print("=" * 50)
    
    examples = [
        {
            "title": "使用传统ReAct Agent",
            "command": "python run.py --agent-type react --question '适合夏天穿的跑鞋'",
            "description": "使用传统的ReAct模式进行商品搜索"
        },
        {
            "title": "使用智能搜索Agent", 
            "command": "python run.py --agent-type intelligent --question '苹果手机'",
            "description": "使用新的智能搜索模式，自动判断查询意图"
        },
        {
            "title": "测试模糊查询",
            "command": "python run.py --agent-type intelligent --question '买个东西'",
            "description": "测试智能Agent如何处理模糊查询"
        },
        {
            "title": "测试需要补全信息的查询",
            "command": "python run.py --agent-type intelligent --question '春游需要什么'",
            "description": "测试智能Agent如何通过web search补全信息"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")

def main():
    """运行所有测试"""
    print("Agent类型功能测试")
    print("=" * 60)
    
    results = []
    
    # 运行测试
    results.append(test_agent_creation())
    results.append(test_command_line_interface())
    results.append(test_main_logic())
    
    # 显示结果
    print("\n\n测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！")
        print("\n现在您可以使用以下两种模式:")
        print("  1. --agent-type react     (传统ReAct模式)")
        print("  2. --agent-type intelligent (新的智能搜索模式)")
        
        show_usage_examples()
        
        print("\n" + "=" * 60)
        print("注意事项:")
        print("- 实际运行需要配置API密钥和网络连接")
        print("- 智能搜索模式针对您的需求进行了优化")
        print("- 建议优先使用intelligent模式获得更好的用户体验")
        
    else:
        print("✗ 部分测试失败，请检查相关配置")

if __name__ == "__main__":
    main()
