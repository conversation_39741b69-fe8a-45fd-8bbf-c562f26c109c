#!/usr/bin/env python3
"""
交互式商品搜索Agent
支持与用户的智能交互，避免不必要的打扰
"""

import json
import time
import asyncio
from typing import Dict, List, Optional, Any, Generator
from dataclasses import dataclass
from enum import Enum

from custom_agent import SpeechSearchAgent
from smolagents.memory import ActionStep, ChatMessage, MessageRole
from smolagents.agents import FinalOutput


class InteractionType(Enum):
    """交互类型"""
    CLARIFICATION = "clarification"  # 澄清查询意图
    CONFIRMATION = "confirmation"    # 确认选择
    FEEDBACK = "feedback"           # 获取反馈
    SUGGESTION = "suggestion"       # 提供建议


@dataclass
class UserInteraction:
    """用户交互数据结构"""
    interaction_id: str
    interaction_type: InteractionType
    message: str
    options: Optional[List[str]] = None
    context: Optional[Dict] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class UserResponse:
    """用户响应数据结构"""
    interaction_id: str
    response_text: str
    selected_option: Optional[str] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class InteractiveSearchAgent(SpeechSearchAgent):
    """
    交互式商品搜索Agent
    能够智能判断何时需要与用户交互，并进行有意义的对话
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 交互状态管理
        self.pending_interactions: Dict[str, UserInteraction] = {}
        self.interaction_history: List[UserInteraction] = []
        self.user_responses: Dict[str, UserResponse] = {}
        
        # 交互策略配置
        self.interaction_threshold = {
            'query_clarity_score': 0.6,  # 查询明确度阈值
            'result_confidence_score': 0.7,  # 结果置信度阈值
            'max_interactions_per_session': 3,  # 每次会话最大交互次数
        }
        
        # 当前会话状态
        self.session_interaction_count = 0
        self.current_query_context = {}
        self.search_results_cache = []
        
        # 回调函数（用于与Web UI通信）
        self.on_interaction_needed = None
        self.on_status_update = None

    def set_interaction_callbacks(self, 
                                on_interaction_needed=None, 
                                on_status_update=None):
        """设置交互回调函数"""
        self.on_interaction_needed = on_interaction_needed
        self.on_status_update = on_status_update

    def _should_interact_with_user(self, context: Dict) -> bool:
        """
        智能判断是否需要与用户交互
        避免不必要的交互
        """
        # 检查交互次数限制
        if self.session_interaction_count >= self.interaction_threshold['max_interactions_per_session']:
            return False
        
        # 检查查询明确度
        query_clarity = context.get('query_clarity_score', 0.5)
        if query_clarity < self.interaction_threshold['query_clarity_score']:
            return True
        
        # 检查搜索结果置信度
        result_confidence = context.get('result_confidence_score', 0.8)
        if result_confidence < self.interaction_threshold['result_confidence_score']:
            return True
        
        # 检查是否有多个可能的搜索方向
        if context.get('multiple_search_directions', False):
            return True
        
        # 检查用户是否明确表达了交互意愿
        if context.get('user_expects_interaction', False):
            return True
        
        return False

    def _analyze_query_clarity(self, query: str) -> Dict:
        """分析查询的明确度"""
        # 使用现有的查询分析工具
        try:
            analysis_tool = self.tools.get('query_analysis')
            if analysis_tool:
                result = analysis_tool.forward(query)
                analysis = json.loads(result)

                # 检查是否是推荐类查询
                recommendation_keywords = ['推荐', '建议', '选择']
                has_recommendation = any(keyword in query for keyword in recommendation_keywords)

                # 如果是推荐类查询，即使intent_clarity是clear，也需要澄清用户偏好
                if has_recommendation and analysis.get('intent_clarity') == 'clear':
                    clarity_score = 0.5  # 中等清晰度，需要澄清偏好
                    needs_clarification = True
                else:
                    clarity_score = 0.8 if analysis.get('intent_clarity') == 'clear' else 0.3
                    needs_clarification = analysis.get('suggested_action') == 'ask_user'

                return {
                    'query_clarity_score': clarity_score,
                    'analysis': analysis,
                    'needs_clarification': needs_clarification,
                    'is_recommendation_query': has_recommendation
                }
        except Exception as e:
            self.logger.log_error(f"查询分析失败: {e}")

        # 简单的启发式分析
        ambiguous_keywords = ['什么', '怎么', '哪个', '好的', '合适']
        recommendation_keywords = ['推荐', '建议', '选择']

        ambiguous_count = sum(1 for keyword in ambiguous_keywords if keyword in query)
        has_recommendation = any(keyword in query for keyword in recommendation_keywords)

        # 对于推荐类查询，降低clarity_score以触发交互
        if has_recommendation:
            clarity_score = max(0.3, 0.8 - (ambiguous_count * 0.2))
            needs_clarification = True  # 推荐类查询总是需要澄清
        else:
            clarity_score = max(0.2, 1.0 - (ambiguous_count * 0.2))
            needs_clarification = clarity_score < 0.6

        return {
            'query_clarity_score': clarity_score,
            'needs_clarification': needs_clarification,
            'ambiguous_keywords': ambiguous_count,
            'is_recommendation_query': has_recommendation
        }

    def _create_interaction(self, 
                          interaction_type: InteractionType,
                          message: str,
                          options: Optional[List[str]] = None,
                          context: Optional[Dict] = None) -> UserInteraction:
        """创建用户交互"""
        interaction_id = f"interaction_{int(time.time() * 1000)}"
        
        interaction = UserInteraction(
            interaction_id=interaction_id,
            interaction_type=interaction_type,
            message=message,
            options=options,
            context=context or {}
        )
        
        self.pending_interactions[interaction_id] = interaction
        self.interaction_history.append(interaction)
        self.session_interaction_count += 1
        
        return interaction

    def _generate_clarification_question(self, query: str, analysis: Dict) -> UserInteraction:
        """生成澄清问题"""
        missing_info = analysis.get('missing_info', [])

        if '商品类型' in missing_info:
            message = f"您想要搜索什么类型的商品呢？我可以帮您搜索："
            options = ["电子产品", "服装鞋包", "家居用品", "食品饮料", "运动户外", "美妆个护"]
        elif '价格范围' in missing_info:
            message = "您希望的价格范围是多少？"
            options = ["50元以下", "50-200元", "200-500元", "500-1000元", "1000元以上", "没有特别要求"]
        elif '品牌偏好' in missing_info:
            message = "您有特别偏好的品牌吗？"
            options = ["知名品牌", "性价比品牌", "没有特别要求"]
        else:
            # 根据查询内容智能生成选项
            if any(keyword in query for keyword in ['春游', '旅游', '出游', '旅行']):
                message = f"关于「{query}」，您想要搜索哪类商品呢？"
                options = ["户外装备", "旅行用品", "服装鞋帽", "食品零食", "电子设备", "其他用品"]
            elif any(keyword in query for keyword in ['夏天', '夏季', '热天']):
                message = f"关于「{query}」，您想要搜索哪类商品呢？"
                options = ["服装鞋帽", "防晒用品", "清凉用品", "运动装备", "家居用品", "其他商品"]
            elif any(keyword in query for keyword in ['冬天', '冬季', '寒冷']) or (any(keyword in query for keyword in ['冬', '保暖']) and any(keyword in query for keyword in ['跑鞋', '运动鞋', '鞋子'])):
                message = f"关于「{query}」，您想要搜索哪类商品呢？"
                if any(keyword in query for keyword in ['跑鞋', '运动鞋', '鞋子']):
                    options = ["运动鞋", "保暖服装", "冬季装备", "护肤用品", "家居用品", "其他商品"]
                else:
                    options = ["保暖服装", "取暖用品", "冬季装备", "护肤用品", "家居用品", "其他商品"]
            elif any(keyword in query for keyword in ['运动', '健身', '锻炼', '跑鞋', '运动鞋', '跑步']):
                message = f"关于「{query}」，您想要搜索哪类商品呢？"
                options = ["运动服装", "运动鞋", "健身器材", "运动配件", "营养补剂", "其他用品"]
            elif any(keyword in query for keyword in ['学习', '办公', '工作']):
                message = f"关于「{query}」，您想要搜索哪类商品呢？"
                options = ["文具用品", "电子设备", "办公用品", "学习资料", "家具用品", "其他商品"]
            elif any(keyword in query for keyword in ['推荐', '建议', '选择']):
                # 对于推荐类查询，提供更具体的选择
                message = f"关于「{query}」，为了给您更精准的推荐，请告诉我您的偏好："
                if any(keyword in query for keyword in ['跑鞋', '运动鞋', '鞋子']):
                    options = ["价格优先", "品牌优先", "功能优先", "外观优先", "性价比优先", "其他要求"]
                elif any(keyword in query for keyword in ['服装', '衣服']):
                    options = ["价格范围", "品牌偏好", "风格偏好", "尺码要求", "颜色偏好", "其他要求"]
                else:
                    options = ["价格优先", "品牌优先", "质量优先", "外观优先", "功能优先", "其他要求"]
            else:
                message = f"关于「{query}」，您能提供更多具体信息吗？比如用途、场景、预算等。"
                options = ["电子产品", "服装鞋包", "家居用品", "食品饮料", "运动户外", "美妆个护", "其他类别"]

        return self._create_interaction(
            InteractionType.CLARIFICATION,
            message,
            options,
            {'original_query': query, 'analysis': analysis}
        )

    def _generate_confirmation_question(self, search_results: List, query: str) -> UserInteraction:
        """生成确认问题"""
        if len(search_results) > 10:
            message = f"我找到了 {len(search_results)} 个相关商品。您希望我："
            options = [
                "显示价格最低的商品",
                "显示销量最高的商品",
                "显示评价最好的商品",
                "缩小搜索范围",
                "查看所有结果"
            ]
        elif len(search_results) > 0:
            message = f"我找到了 {len(search_results)} 个商品，您觉得这些结果符合您的需求吗？"
            options = ["很满意", "还可以", "不太满意，需要调整", "查看更多选项", "重新搜索"]
        else:
            # 当没有搜索结果时，提供更多选择
            message = f"关于「{query}」，我想为您提供更精准的搜索。您希望："
            options = [
                "调整搜索关键词",
                "扩大搜索范围",
                "查看相关推荐",
                "换个搜索方式",
                "重新描述需求"
            ]

        return self._create_interaction(
            InteractionType.CONFIRMATION,
            message,
            options,
            {'search_results_count': len(search_results), 'query': query}
        )

    async def wait_for_user_response(self, interaction: UserInteraction, timeout: int = 300) -> Optional[UserResponse]:
        """等待用户响应"""
        if self.on_interaction_needed:
            # 通知Web UI需要用户交互
            await self.on_interaction_needed(interaction)
        
        # 等待用户响应
        start_time = time.time()
        while time.time() - start_time < timeout:
            if interaction.interaction_id in self.user_responses:
                response = self.user_responses.pop(interaction.interaction_id)
                self.pending_interactions.pop(interaction.interaction_id, None)
                return response
            
            await asyncio.sleep(0.5)
        
        # 超时处理
        self.pending_interactions.pop(interaction.interaction_id, None)
        return None

    def handle_user_response(self, interaction_id: str, response_text: str, selected_option: str = None, is_custom_input: bool = False):
        """处理用户响应"""
        # 如果已经有响应了，合并新的响应
        if interaction_id in self.user_responses:
            existing_response = self.user_responses[interaction_id]
            # 合并响应文本
            combined_text = existing_response.response_text
            if response_text and response_text != existing_response.response_text:
                combined_text += f"; {response_text}"

            response = UserResponse(
                interaction_id=interaction_id,
                response_text=combined_text,
                selected_option=selected_option or existing_response.selected_option
            )
        else:
            response = UserResponse(
                interaction_id=interaction_id,
                response_text=response_text,
                selected_option=selected_option
            )

        self.user_responses[interaction_id] = response
        return response

    def _update_search_strategy_from_response(self, response: UserResponse, interaction: UserInteraction):
        """根据用户响应更新搜索策略"""
        if interaction.interaction_type == InteractionType.CLARIFICATION:
            # 更新查询上下文
            if response.selected_option:
                self.current_query_context['user_preference'] = response.selected_option
                self.current_query_context['refined_query'] = f"{interaction.context['original_query']} {response.selected_option}"
            else:
                self.current_query_context['additional_info'] = response.response_text
        
        elif interaction.interaction_type == InteractionType.CONFIRMATION:
            # 更新结果过滤策略
            if response.selected_option:
                self.current_query_context['result_filter'] = response.selected_option

    async def _interactive_step(self, memory_step: ActionStep) -> Generator[FinalOutput, None, None]:
        """
        交互式步骤执行
        在必要时与用户交互
        """
        # 分析当前查询
        query_analysis = self._analyze_query_clarity(self.task)
        
        # 更新状态
        if self.on_status_update:
            await self.on_status_update({
                'step': 'analyzing_query',
                'query': self.task,
                'analysis': query_analysis
            })
        
        # 判断是否需要交互
        interaction_context = {
            **query_analysis,
            'multiple_search_directions': len(self.search_results_cache) > 20,
            'user_expects_interaction': '?' in self.task or '推荐' in self.task
        }
        
        if self._should_interact_with_user(interaction_context):
            # 生成交互
            if query_analysis.get('needs_clarification'):
                interaction = self._generate_clarification_question(self.task, query_analysis.get('analysis', {}))
            else:
                interaction = self._generate_confirmation_question(self.search_results_cache, self.task)
            
            # 等待用户响应
            response = await self.wait_for_user_response(interaction)
            
            if response:
                # 处理用户响应
                self._update_search_strategy_from_response(response, interaction)
                
                # 更新任务查询
                if 'refined_query' in self.current_query_context:
                    self.task = self.current_query_context['refined_query']
        
        # 继续执行原有的搜索逻辑
        async for output in super()._step_stream(memory_step):
            yield output

    def run_interactive(self, task: str) -> str:
        """
        运行交互式搜索（同步版本）
        """
        self.task = task
        self.session_interaction_count = 0
        self.current_query_context = {}
        self.search_results_cache = []

        try:
            # 先进行查询分析
            query_analysis = self._analyze_query_clarity(task)

            # 判断是否需要交互
            interaction_context = {
                **query_analysis,
                'multiple_search_directions': False,
                'user_expects_interaction': '?' in task or '推荐' in task
            }

            # 如果需要交互但没有设置回调，直接进行搜索
            if self._should_interact_with_user(interaction_context) and self.on_interaction_needed:
                # 生成交互（同步版本）
                if query_analysis.get('needs_clarification'):
                    interaction = self._generate_clarification_question(task, query_analysis.get('analysis', {}))
                    # 在实际应用中，这里会等待用户响应
                    # 现在直接使用原始查询继续
                    pass

            # 运行标准搜索流程
            return self.run(task)

        except Exception as e:
            self.logger.log_error(f"交互式搜索失败: {e}")
            return f"搜索过程中出现错误: {e}"

    async def run_interactive_async(self, task: str) -> str:
        """
        运行交互式搜索（异步版本）
        """
        self.task = task
        self.session_interaction_count = 0
        self.current_query_context = {}
        self.search_results_cache = []

        try:
            # 先进行查询分析
            query_analysis = self._analyze_query_clarity(task)

            # 更新状态
            if self.on_status_update:
                await self.on_status_update({
                    'step': 'analyzing_query',
                    'query': task,
                    'analysis': query_analysis
                })

            # 判断是否需要交互
            interaction_context = {
                **query_analysis,
                'multiple_search_directions': False,
                'user_expects_interaction': '?' in task or '推荐' in task
            }

            if self._should_interact_with_user(interaction_context):
                # 生成交互
                if query_analysis.get('needs_clarification'):
                    interaction = self._generate_clarification_question(task, query_analysis.get('analysis', {}))
                else:
                    # 如果有搜索结果缓存，生成确认问题
                    interaction = self._generate_confirmation_question([], task)

                # 等待用户响应
                response = await self.wait_for_user_response(interaction)

                if response:
                    # 处理用户响应
                    self._update_search_strategy_from_response(response, interaction)

                    # 更新任务查询
                    if 'refined_query' in self.current_query_context:
                        task = self.current_query_context['refined_query']
                        self.task = task

            # 更新状态
            if self.on_status_update:
                await self.on_status_update({
                    'step': 'starting_search',
                    'query': task
                })

            # 运行标准搜索流程
            result = self.run(task)

            # 更新状态
            if self.on_status_update:
                await self.on_status_update({
                    'step': 'search_completed',
                    'query': task
                })

            return result

        except Exception as e:
            self.logger.log_error(f"交互式搜索失败: {e}")
            return f"搜索过程中出现错误: {e}"
