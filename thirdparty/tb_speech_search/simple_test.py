#!/usr/bin/env python3
"""
简单测试脚本，验证新的智能搜索功能
"""

import json

# 模拟一个简单的模型类用于测试
class MockModel:
    def generate(self, messages):
        # 模拟模型响应
        user_content = messages[-1]["content"]
        
        if "分析以下用户查询的商品搜索意图" in user_content:
            # 模拟查询分析结果
            if "苹果手机" in user_content:
                result = {
                    "can_direct_search": True,
                    "intent_clarity": "clear",
                    "analysis_reason": "用户明确想要搜索苹果手机",
                    "suggested_action": "direct_search",
                    "missing_info": []
                }
            elif "买个东西" in user_content:
                result = {
                    "can_direct_search": False,
                    "intent_clarity": "unclear",
                    "analysis_reason": "用户查询过于模糊",
                    "suggested_action": "ask_user",
                    "missing_info": ["商品类型", "具体需求"]
                }
            else:
                result = {
                    "can_direct_search": False,
                    "intent_clarity": "unclear",
                    "analysis_reason": "需要更多信息",
                    "suggested_action": "web_search_then_search",
                    "missing_info": ["具体规格"]
                }
            
            class MockResponse:
                def __init__(self, content):
                    self.content = content
            
            return MockResponse(json.dumps(result, ensure_ascii=False))
        
        elif "请生成一个友好、有帮助的反问" in user_content:
            class MockResponse:
                def __init__(self, content):
                    self.content = content
            return MockResponse("您想要什么类型的商品呢？比如电子产品、服装、食品等？")
        
        else:
            class MockResponse:
                def __init__(self, content):
                    self.content = content
            return MockResponse("模拟响应")

def test_query_analysis_tool():
    """测试查询分析工具"""
    print("测试查询分析工具...")
    
    try:
        from custom_tools import QueryAnalysisTool
        
        model = MockModel()
        tool = QueryAnalysisTool(model)
        
        # 测试明确查询
        result1 = tool.forward("苹果手机")
        print(f"明确查询结果: {result1}")
        
        # 测试模糊查询
        result2 = tool.forward("买个东西")
        print(f"模糊查询结果: {result2}")
        
        print("✓ 查询分析工具测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 查询分析工具测试失败: {e}")
        return False

def test_user_question_tool():
    """测试用户反问工具"""
    print("\n测试用户反问工具...")
    
    try:
        from custom_tools import UserQuestionTool
        
        model = MockModel()
        tool = UserQuestionTool(model)
        
        result = tool.forward(
            original_query="买个东西",
            missing_info='["商品类型", "具体需求"]',
            use_web_search=False
        )
        print(f"反问结果: {result}")
        
        print("✓ 用户反问工具测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 用户反问工具测试失败: {e}")
        return False

def test_taobao_search_tool():
    """测试淘宝搜索工具（不实际调用API）"""
    print("\n测试淘宝搜索工具...")
    
    try:
        from custom_tools import TaobaoMainSearchTool
        
        tool = TaobaoMainSearchTool()
        print(f"工具名称: {tool.name}")
        print(f"工具描述: {tool.description}")
        print(f"输入参数: {tool.inputs}")
        
        print("✓ 淘宝搜索工具初始化成功")
        return True
        
    except Exception as e:
        print(f"✗ 淘宝搜索工具测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试智能搜索Agent的组件...")
    print("=" * 50)
    
    results = []
    
    # 测试各个工具
    results.append(test_query_analysis_tool())
    results.append(test_user_question_tool())
    results.append(test_taobao_search_tool())
    
    print("\n" + "=" * 50)
    print("测试总结:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！智能搜索Agent的基础组件工作正常。")
    else:
        print("✗ 部分测试失败，需要检查相关组件。")

if __name__ == "__main__":
    main()
