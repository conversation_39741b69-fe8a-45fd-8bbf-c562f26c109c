import requests
import re
import time,json
import datetime
from datetime import datetime as dt

def fetch_rag(query="新疆有什么好吃的", history=None):
    """
    请求夸克RAG接口，目前暂时不支持传入history
    
    Args:
        query (str): 搜索查询字符串，默认为"新疆有什么好吃的"
        
    Returns:
        dict: 如果成功，返回解析后的JSON响应
        None: 如果请求失败
    """
    # 请求 URL
    url = "https://tppwork.taobao.com/center/recommend"
    
    # 请求参数
    params = {
        "appid": "38653",
        "_sid_": "106323",
        "_input_charset": "UTF-8",
        "_output_charset": "UTF-8",
        "code": "ragAgent",
        "ragStrategy": "quark",
        "offline": "true",
        "q": query,
        "channelSrp": "aiUniversalSearch",
        "logLevel": "DEBUG",
        "logPassThrough": "true",
        "DEBUG_": "true",
        "service_plan_play": "true"
    }
    
    try:
        # 发送 GET 请求
        response = requests.get(url, params=params)
        
        # 检查响应状态
        if response.status_code == 200:
            response_json = response.json()
            pluginModeOutPutString = response_json['pluginModeOutPutString']
            pattern = re.compile(r'<文章\d+>标题：(?P<title>.*?) 网页发布时间: (?P<push_time>.*?) 正文内容：(?P<content>.*?)</文章\d+>', re.S)
            matches = pattern.findall(pluginModeOutPutString)
                
            # 输出匹配结果
            doc_lst = []
            index_count = 0
            for index,match in enumerate(matches):
                title, push_time, content = match
                if len(content.strip()) <=2:
                    continue
                index_count += 1
                doc_lst.append({"index":index_count,"title":title, "push_time":push_time, "content": content })
            return doc_lst, pluginModeOutPutString
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None, ""
            
    except Exception as e:
        print(f"发生错误: {e}")
        return None, ""
    
    
def request_llm_idealab(messages):
    api_ak = "27db1fc058c1861870be4c21a7f93cdc"
    headers = {"X-AK": api_ak, "Content-Type": "application/json"}
    idealab_url = 'https://idealab.alibaba-inc.com/api/v1/chat/completions'
    task_id = None
    data = { "messages": messages,
            # "platformInput": {"model": "claude35_sonnet"},
            # "platformInput": {"model": "gpt-4o-0513-global"},
#             "platformInput": {"model": "gpt-4o-0806"},
            "platformInput": {"model": "claude35_sonnet"},
            # "response_format": {"type": "json_object"},
            "temperature": 0.6,
            "top_p": 0.8,
            "max_tokens": 8196,
            "stream": False,
        }


    # 发送GET请求
    response = requests.post(idealab_url, headers=headers, data=json.dumps(data))
    res = response.json()
    message = None
    try:
        content = res['data']['choices'][0]['message']['content']
        return content
    except Exception as e:
        print(f"请求失败，错误信息: {e}")

    return None
    
    
def llm_summary(query, history=[]):
    """
    基于历史对话记录
    """
    
    
    doc_lst,pluginModeOutPutString  = fetch_rag(query)
    
    # 获取当前时间
    now = dt.now()
    formatted_datetime = now.strftime("%Y-%m-%d %H:%M:%S")
    
    
    SYSTEM_PROMPT= f"""
    请结合网页检索得到的材料准确无误地回答用户的提问。
    以下是具体要求：

    - 请用用户提问的语言来回答问题，如用户使用中文提问，则用中文回答，如果用户用英文提问，则用英文来回答。
    - 请聚焦在用户的问题本身，不要发散，简短扼要地回答用户的问题，不用给总结。
    - 若在回答的某片段参考了某一检索文档，请务必在该片段后面标注上对应引用标签，格式为【num†】
    - 回答尽量使用markdown格式，对于回答中的小标题需要加粗。
    - 没有材料或者材料出现明显错误时，你要结合你自身的知识体系回答问题。同时你需要注意材料的时效性。
    - 你的回答应该遵守中华人民共和国的法律，拒绝一切涉及恐怖主义，种族歧视，黄色暴力，政治敏感等问题的回答。
    - 可以尝试使用表格等方式更好的回答用户的问题，让信息更加简单明了。
    - 以正文形式书写，不用使用标题，最后不用总结。

    现在时间是{formatted_datetime}。
    """

    USER_PROMPT=f"""
    以下是检索到的材料：
    {pluginModeOutPutString}

    请记住以上信息。
    用户当前输入：{query}
    """
    
    if len(history)!=0:
        messages = history + [{"role":"user", "content":USER_PROMPT }]
    else:
        messages = [{"role":"system", "content":SYSTEM_PROMPT}] + [{"role":"user", "content":USER_PROMPT}]
        
    summary = request_llm_idealab(messages)
    return summary
            

if __name__=="__main__":
    query = "新疆有什么好吃的？"
    query_summary = llm_summary(query)
    print(query_summary)
    