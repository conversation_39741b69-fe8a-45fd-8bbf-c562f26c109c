import importlib
import inspect
import json
import os
import re
import tempfile
import textwrap
import time
import warnings
from abc import ABC, abstractmethod
from collections.abc import Callable, Generator
from dataclasses import dataclass
from logging import getLogger
from pathlib import Path
from typing import TYPE_CHECKING, Any, Literal, TypedDict

import jinja2
import yaml
from huggingface_hub import create_repo, metadata_update, snapshot_download, upload_folder
from jinja2 import StrictUndefined, Template
from rich.console import Group
from rich.live import Live
from rich.markdown import Markdown
from rich.panel import Panel
from rich.rule import Rule
from rich.text import Text


if TYPE_CHECKING:
    import PIL.Image

from smolagents.agents import MultiStepAgent, PromptTemplates, populate_template, FinalOutput
from smolagents.agent_types import AgentAudio, AgentImage, handle_agent_output_types
from smolagents.default_tools import TOOL_MAPPING, FinalAnswerTool
from smolagents.local_python_executor import BASE_BUILTIN_MODULES, LocalPythonExecutor, PythonExecutor, fix_final_answer_code
from smolagents.memory import (
    ActionStep,
    AgentMemory,
    FinalAnswerStep,
    Message,
    PlanningStep,
    SystemPromptStep,
    TaskStep,
    Timing,
    TokenUsage,
    ToolCall,
)
from smolagents.models import (
    CODEAGENT_RESPONSE_FORMAT,
    ChatMessage,
    ChatMessageStreamDelta,
    MessageRole,
    Model,
    parse_json_if_needed,
)
from smolagents.monitoring import (
    YELLOW_HEX,
    AgentLogger,
    LogLevel,
    Monitor,
)
from smolagents.remote_executors import DockerExecutor, E2BExecutor
from smolagents.tools import Tool
from smolagents.utils import (
    AgentError,
    AgentExecutionError,
    AgentGenerationError,
    AgentMaxStepsError,
    AgentParsingError,
    AgentToolCallError,
    AgentToolExecutionError,
    extract_code_from_text,
    is_valid_name,
    make_init_file,
    parse_code_blobs,
    truncate_content,
)


logger = getLogger(__name__)

class SpeechSearchAgent(MultiStepAgent):
    """
    A custom agent for searching through speech transcripts.
    This agent is designed to handle multi-step interactions with the user.
    Enhanced to automatically call TaobaoItemDetailsTool after getting item IDs.
    """

    def __init__(
        self,
        tools: list[Tool],
        model: Callable[[list[dict[str, str]]], ChatMessage],
        prompt_templates: PromptTemplates | None = None,
        planning_interval: int | None = None,
        **kwargs,
    ):
        prompt_templates = prompt_templates or yaml.safe_load(
            open('prompts/speech_search_agent.yaml').read()
        )
        super().__init__(
            tools=tools,
            model=model,
            prompt_templates=prompt_templates,
            planning_interval=planning_interval,
            **kwargs,
        )
        self._last_search_result = None
        self._pending_item_ids = []
        self._has_searched_products = False
        self._has_detailed_info = False
    
    def initialize_system_prompt(self) -> str:
        system_prompt = populate_template(
            self.prompt_templates["system_prompt"],
            variables={"tools": self.tools, "managed_agents": self.managed_agents},
        )
        return system_prompt

    def _step_stream(self, memory_step: ActionStep) -> Generator[FinalOutput]:
        """
        Perform one step in the ReAct framework: the agent thinks, acts, and observes the result.
        Yields either None if the step is not final, or the final answer.
        """
        memory_messages = self.write_memory_to_messages()

        input_messages = memory_messages.copy()

        # Add new step in logs
        memory_step.model_input_messages = input_messages

        chat_message: ChatMessage = self.model.generate(
            input_messages,
            stop_sequences=["Observation:", "Calling tools:"],
            tools_to_call_from=list(self.tools.values()),
        )

        memory_step.model_output_message = chat_message
        model_output = chat_message.content
        self.logger.log_markdown(
            content=model_output if model_output else str(chat_message.raw),
            title="Output message of the LLM:",
            level=LogLevel.DEBUG,
        )

        memory_step.model_output_message.content = model_output
        memory_step.model_output = model_output

        if chat_message.tool_calls is None or len(chat_message.tool_calls) == 0:
            try:
                chat_message = self.model.parse_tool_calls(chat_message)
            except Exception as e:
                logger.error(
                    f"Error while parsing tool calls from model output: {e}\n"
                    f"Model output: {chat_message.raw}"
                )
                raise AgentParsingError(f"Error while parsing tool call from model output: {e}", self.logger)
        else:
            for tool_call in chat_message.tool_calls:
                tool_call.function.arguments = parse_json_if_needed(tool_call.function.arguments)
        tool_call = chat_message.tool_calls[0]  # type: ignore
        tool_name, tool_call_id = tool_call.function.name, tool_call.id
        tool_arguments = tool_call.function.arguments
        memory_step.model_output = str(f"Called Tool: '{tool_name}' with arguments: {tool_arguments}")
        memory_step.tool_calls = [ToolCall(name=tool_name, arguments=tool_arguments, id=tool_call_id)]
        memory_step.token_usage = chat_message.token_usage

        # Execute
        self.logger.log(
            Panel(Text(f"Calling tool: '{tool_name}' with arguments: {tool_arguments}")),
            level=LogLevel.INFO,
        )
        if tool_name == "final_answer":
            # 检查是否已获取商品详细信息
            if not self._has_detailed_info:
                self.logger.log("⚠️ 尝试结束任务但未获取商品详细信息，继续执行...", level=LogLevel.ERROR)
                # 如果有待处理的商品ID，强制调用详情工具
                if self._pending_item_ids:
                    self.logger.log("🔄 强制调用商品详情工具...", level=LogLevel.INFO)
                    detail_result = self.execute_tool_call("taobao_item_details", {"item_id_list": self.get_pending_item_ids_json()})
                    self.clear_pending_item_ids()
                    memory_step.observations = f"强制获取商品详情: {str(detail_result).strip()}"
                    yield FinalOutput(output=None)
                    return
                elif self._has_searched_products:
                    # 如果已搜索但没有详情，提示需要详情
                    memory_step.observations = "错误：已搜索商品但未获取详细信息，请调用taobao_item_details工具"
                    yield FinalOutput(output=None)
                    return
                else:
                    # 如果连商品都没搜索，提示需要搜索
                    memory_step.observations = "错误：未搜索任何商品，请先使用taobao_main_search搜索商品"
                    yield FinalOutput(output=None)
                    return

            if isinstance(tool_arguments, dict):
                if "answer" in tool_arguments:
                    answer = tool_arguments["answer"]
                else:
                    answer = tool_arguments
            else:
                answer = tool_arguments
            if isinstance(answer, str) and answer in self.state.keys():
                # if the answer is a state variable, return the value
                # State variables are not JSON-serializable (AgentImage, AgentAudio) so can't be passed as arguments to execute_tool_call
                final_answer = self.state[answer]
                self.logger.log(
                    f"[bold {YELLOW_HEX}]Final answer:[/bold {YELLOW_HEX}] Extracting key '{answer}' from state to return value '{final_answer}'.",
                    level=LogLevel.INFO,
                )
            else:
                final_answer = self.execute_tool_call("final_answer", {"answer": answer})
                self.logger.log(
                    Text(f"Final answer: {final_answer}", style=f"bold {YELLOW_HEX}"),
                    level=LogLevel.INFO,
                )

            memory_step.action_output = final_answer
            yield FinalOutput(output=final_answer)
        else:
            if tool_arguments is None:
                tool_arguments = {}
            observation = self.execute_tool_call(tool_name, tool_arguments)
            observation_type = type(observation)
            if observation_type in [AgentImage, AgentAudio]:
                if observation_type == AgentImage:
                    observation_name = "image.png"
                elif observation_type == AgentAudio:
                    observation_name = "audio.mp3"
                # TODO: observation naming could allow for different names of same type

                self.state[observation_name] = observation
                updated_information = f"Stored '{observation_name}' in memory."
            else:
                updated_information = str(observation).strip()
            self.logger.log(
                f"Observations: {updated_information.replace('[', '|')}",  # escape potential rich-tag-like components
                level=LogLevel.INFO,
            )
            memory_step.observations = updated_information
            yield FinalOutput(output=None)

    def _substitute_state_variables(self, arguments: dict[str, str] | str) -> dict[str, Any] | str:
        """Replace string values in arguments with their corresponding state values if they exist."""
        if isinstance(arguments, dict):
            return {
                key: self.state.get(value, value) if isinstance(value, str) else value
                for key, value in arguments.items()
            }
        return arguments

    def execute_tool_call(self, tool_name: str, arguments: dict[str, str] | str) -> Any:
        """
        Execute a tool or managed agent with the provided arguments.

        The arguments are replaced with the actual values from the state if they refer to state variables.

        Args:
            tool_name (`str`): Name of the tool or managed agent to execute.
            arguments (dict[str, str] | str): Arguments passed to the tool call.
        """
        # Check if the tool exists
        available_tools = {**self.tools, **self.managed_agents}
        if tool_name not in available_tools:
            raise AgentToolExecutionError(
                f"Unknown tool {tool_name}, should be one of: {', '.join(available_tools)}.", self.logger
            )

        # Get the tool and substitute state variables in arguments
        tool = available_tools[tool_name]
        arguments = self._substitute_state_variables(arguments)
        is_managed_agent = tool_name in self.managed_agents

        try:
            # Call tool with appropriate arguments
            if isinstance(arguments, dict):
                result = tool(**arguments) if is_managed_agent else tool(**arguments, sanitize_inputs_outputs=True)
            elif isinstance(arguments, str):
                result = tool(arguments) if is_managed_agent else tool(arguments, sanitize_inputs_outputs=True)
            else:
                raise TypeError(f"Unsupported arguments type: {type(arguments)}")

            # 处理不同工具的结果，跟踪状态
            if tool_name == "taobao_main_search":
                self._extract_item_ids_from_search_result(result)
                self._has_searched_products = True
                self.logger.log("✅ 已搜索商品，获取到商品ID", level=LogLevel.INFO)
            elif tool_name == "taobao_item_details":
                self._has_detailed_info = True
                self.logger.log("✅ 已获取商品详细信息", level=LogLevel.INFO)

            return result

        except TypeError as e:
            # Handle invalid arguments
            description = getattr(tool, "description", "No description")
            if is_managed_agent:
                error_msg = (
                    f"Invalid request to team member '{tool_name}' with arguments {json.dumps(arguments)}: {e}\n"
                    "You should call this team member with a valid request.\n"
                    f"Team member description: {description}"
                )
            else:
                error_msg = (
                    f"Invalid call to tool '{tool_name}' with arguments {json.dumps(arguments)}: {e}\n"
                    "You should call this tool with correct input arguments.\n"
                    f"Expected inputs: {json.dumps(tool.inputs)}\n"
                    f"Returns output type: {tool.output_type}\n"
                    f"Tool description: '{description}'"
                )
            raise AgentToolCallError(error_msg, self.logger) from e

        except Exception as e:
            # Handle execution errors
            if is_managed_agent:
                error_msg = (
                    f"Error executing request to team member '{tool_name}' with arguments {json.dumps(arguments)}: {e}\n"
                    "Please try again or request to another team member"
                )
            else:
                error_msg = (
                    f"Error executing tool '{tool_name}' with arguments {json.dumps(arguments)}: {type(e).__name__}: {e}\n"
                    "Please try again or use another tool"
                )
            raise AgentToolExecutionError(error_msg, self.logger) from e

    def _extract_item_ids_from_search_result(self, result):
        """
        从淘宝主搜结果中提取商品ID
        """
        try:
            if isinstance(result, str):
                # 尝试解析JSON字符串
                import json
                try:
                    result_data = json.loads(result)
                except:
                    # 如果不是JSON，尝试从字符串中提取ID
                    import re
                    id_pattern = r'"(?:itemId|item_id|nid|nidlong)"\s*:\s*"?(\d+)"?'
                    ids = re.findall(id_pattern, result)
                    self._pending_item_ids.extend(ids)
                    return
            else:
                result_data = result

            # 处理列表或字典格式的结果
            if isinstance(result_data, list):
                for item in result_data:
                    if isinstance(item, dict):
                        # 尝试多种可能的ID字段名
                        for id_field in ['itemId', 'item_id', 'nid', 'nidlong', 'id']:
                            if id_field in item:
                                item_id = str(item[id_field])
                                if item_id not in self._pending_item_ids:
                                    self._pending_item_ids.append(item_id)
                                break
            elif isinstance(result_data, dict):
                # 如果结果是字典，可能包含items列表
                if 'items' in result_data:
                    self._extract_item_ids_from_search_result(result_data['items'])
                elif 'result' in result_data:
                    self._extract_item_ids_from_search_result(result_data['result'])
                else:
                    # 直接从字典中提取ID
                    for id_field in ['itemId', 'item_id', 'nid', 'nidlong', 'id']:
                        if id_field in result_data:
                            item_id = str(result_data[id_field])
                            if item_id not in self._pending_item_ids:
                                self._pending_item_ids.append(item_id)
                            break

            # 记录提取到的ID
            if self._pending_item_ids:
                self.logger.log(
                    f"📦 提取到商品ID: {self._pending_item_ids}",
                    level=LogLevel.INFO,
                )

        except Exception as e:
            self.logger.log(f"提取商品ID时出错: {e}", level=LogLevel.ERROR)

    def should_call_item_details(self) -> bool:
        """
        判断是否应该调用商品详情工具
        """
        return len(self._pending_item_ids) > 0

    def get_pending_item_ids_json(self) -> str:
        """
        获取待处理的商品ID列表的JSON格式
        """
        return json.dumps(self._pending_item_ids)

    def clear_pending_item_ids(self):
        """
        清空待处理的商品ID列表
        """
        self._pending_item_ids = []

    def _generate_planning_step(
        self, task, is_first_step: bool, step: int
    ) -> Generator[ChatMessageStreamDelta | PlanningStep]:
        start_time = time.time()
        if is_first_step:
            input_messages = [
                {
                    "role": MessageRole.USER,
                    "content": [
                        {
                            "type": "text",
                            "text": populate_template(
                                self.prompt_templates["planning"]["initial_plan"],
                                variables={"task": task, "tools": self.tools, "managed_agents": self.managed_agents},
                            ),
                        }
                    ],
                }
            ]
            if self.stream_outputs and hasattr(self.model, "generate_stream"):
                plan_message_content = ""
                output_stream = self.model.generate_stream(input_messages, stop_sequences=["<end_plan>"])  # type: ignore
                input_tokens, output_tokens = 0, 0
                with Live("", console=self.logger.console, vertical_overflow="visible") as live:
                    for event in output_stream:
                        if event.content is not None:
                            plan_message_content += event.content
                            live.update(Markdown(plan_message_content))
                            if event.token_usage:
                                output_tokens += event.token_usage.output_tokens
                                input_tokens = event.token_usage.input_tokens
                        yield event
            else:
                plan_message = self.model.generate(input_messages, stop_sequences=["<end_plan>"])
                plan_message_content = plan_message.content
                input_tokens, output_tokens = (
                    plan_message.token_usage.input_tokens,
                    plan_message.token_usage.output_tokens,
                )
            plan = textwrap.dedent(
                f"""以下是我了解的事实以及我将遵循的解决任务的计划：\n```\n{plan_message_content}\n```"""
            )
        else:
            # Summary mode removes the system prompt and previous planning messages output by the model.
            # Removing previous planning messages avoids influencing too much the new plan.
            memory_messages = self.write_memory_to_messages(summary_mode=True)
            plan_update_pre = {
                "role": MessageRole.SYSTEM,
                "content": [
                    {
                        "type": "text",
                        "text": populate_template(
                            self.prompt_templates["planning"]["update_plan_pre_messages"], variables={"task": task}
                        ),
                    }
                ],
            }
            plan_update_post = {
                "role": MessageRole.USER,
                "content": [
                    {
                        "type": "text",
                        "text": populate_template(
                            self.prompt_templates["planning"]["update_plan_post_messages"],
                            variables={
                                "task": task,
                                "tools": self.tools,
                                "managed_agents": self.managed_agents,
                                "remaining_steps": (self.max_steps - step),
                            },
                        ),
                    }
                ],
            }
            input_messages = [plan_update_pre] + memory_messages + [plan_update_post]
            if self.stream_outputs and hasattr(self.model, "generate_stream"):
                plan_message_content = ""
                input_tokens, output_tokens = 0, 0
                with Live("", console=self.logger.console, vertical_overflow="visible") as live:
                    for event in self.model.generate_stream(
                        input_messages,
                        stop_sequences=["<end_plan>"],
                    ):  # type: ignore
                        if event.content is not None:
                            plan_message_content += event.content
                            live.update(Markdown(plan_message_content))
                            if event.token_usage:
                                output_tokens += event.token_usage.output_tokens
                                input_tokens = event.token_usage.input_tokens
                        yield event
            else:
                plan_message = self.model.generate(input_messages, stop_sequences=["<end_plan>"])
                plan_message_content = plan_message.content
                input_tokens, output_tokens = (
                    plan_message.token_usage.input_tokens,
                    plan_message.token_usage.output_tokens,
                )
            plan = textwrap.dedent(
                f"""我仍然需要解决分配给我的任务：\n```\n{self.task}\n```\n\nHere are the facts I know and my new/updated plan of action to solve the task:\n```\n{plan_message_content}\n```"""
            )
        log_headline = "Initial plan" if is_first_step else "Updated plan"
        self.logger.log(Rule(f"[bold]{log_headline}", style="orange"), Text(plan), level=LogLevel.INFO)
        yield PlanningStep(
            model_input_messages=input_messages,
            plan=plan,
            model_output_message=ChatMessage(role=MessageRole.ASSISTANT, content=plan_message_content),
            token_usage=TokenUsage(input_tokens=input_tokens, output_tokens=output_tokens),
            timing=Timing(start_time=start_time, end_time=time.time()),
        )
