#!/usr/bin/env python3
"""
独立版本 - 完全避免Gradio兼容性问题
"""

import os
import sys
import traceback

# 设置环境变量避免网络问题
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['PYTHONHTTPSVERIFY'] = '0'

def create_agent_safely():
    """安全地创建agent，避免任何Gradio相关问题"""
    try:
        # 直接导入必要的模块，避免循环导入
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from custom_model import CustomModel
        from custom_tools import TaobaoMainSearchTool, TaobaoItemDetailsTool, SearchSummaryTool
        from custom_agent import SpeechSearchAgent
        
        # 设置API参数
        API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "27db1fc058c1861870be4c21a7f93cdc")
        ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api")
        
        # 创建模型
        custom_role_conversions = {"tool-call": "assistant", "tool-response": "user"}
        model = CustomModel(
            model_id="gpt-4-0409",
            custom_role_conversions=custom_role_conversions,
            api_base=ENDPOINT,
            api_key=API_KEY,
            max_completion_tokens=8192
        )
        
        # 创建工具
        tools = [
            SearchSummaryTool(),
            TaobaoMainSearchTool(),
            TaobaoItemDetailsTool(),
        ]
        
        # 尝试添加GoogleSearchTool
        try:
            from smolagents import GoogleSearchTool
            serper_api_key = os.getenv("SERPER_API_KEY", "4b1915a54600d6c6a2d84742f0332346dbb0a6d6")
            os.environ["SERPER_API_KEY"] = serper_api_key
            google_search_tool = GoogleSearchTool(provider="serper")
            tools.insert(0, google_search_tool)
            print("✓ GoogleSearchTool 已添加")
        except Exception as e:
            print(f"⚠️ 跳过GoogleSearchTool: {e}")
        
        # 创建agent - 使用原始的SpeechSearchAgent保持反问能力
        agent = SpeechSearchAgent(
            model=model,
            tools=tools,
            max_steps=8,
            verbosity_level=1
        )
        
        return agent
        
    except Exception as e:
        print(f"❌ 创建agent失败: {e}")
        traceback.print_exc()
        return None

def generate_html_from_json(items_data):
    """从JSON数据生成HTML展示"""
    if not items_data:
        return "<div style='padding: 20px;'>没有找到商品信息</div>"

    html = """
    <div style="padding: 20px;">
        <h2 style="color: #1890ff; margin-bottom: 25px; text-align: center;">🛍️ 商品推荐结果</h2>
    """

    for i, item in enumerate(items_data, 1):
        item_id = item.get('itemId', '')
        title = item.get('itemTitle', f'商品 {i}')
        price = item.get('itemPrice', '价格未知')
        image_url = item.get('imageUrl', '')
        shop_url = item.get('shopInfo', '')

        # 生成购买链接
        buy_link = f"http://a.m.taobao.com/i{item_id}.htm" if item_id else ""

        html += f"""
        <div style="
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        ">
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <div style="flex-shrink: 0;">
                    {f'''
                    <img src="{image_url}"
                         style="
                             width: 220px;
                             height: 220px;
                             object-fit: cover;
                             border-radius: 10px;
                             border: 2px solid #f0f0f0;
                             box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                         "
                         alt="商品图片"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                    <div style="
                        width: 220px;
                        height: 220px;
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                        border-radius: 10px;
                        display: none;
                        align-items: center;
                        justify-content: center;
                        color: #999;
                        font-size: 14px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 24px; margin-bottom: 8px;">📷</div>
                            暂无图片
                        </div>
                    </div>
                    ''' if image_url else '''
                    <div style="
                        width: 220px;
                        height: 220px;
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                        border-radius: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #999;
                        font-size: 14px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 24px; margin-bottom: 8px;">📷</div>
                            暂无图片
                        </div>
                    </div>
                    '''}
                </div>
                <div style="flex: 1; min-width: 0;">
                    <h3 style="
                        color: #2c3e50;
                        margin: 0 0 15px 0;
                        font-size: 18px;
                        font-weight: 600;
                        line-height: 1.4;
                        word-wrap: break-word;
                    ">{title}</h3>

                    <div style="margin-bottom: 15px;">
                        <span style="
                            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 20px;
                            font-weight: bold;
                            font-size: 16px;
                            box-shadow: 0 2px 4px rgba(238, 90, 36, 0.3);
                        ">💰 ¥{price}</span>
                    </div>

                    <div style="color: #666; margin: 10px 0;">
                        <span style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                            🏪 商品ID: {item_id}
                        </span>
                    </div>

                    <div style="margin-top: 15px;">
                        {f'''
                        <a href="{buy_link}"
                           target="_blank"
                           style="
                               display: inline-block;
                               background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
                               color: white;
                               padding: 10px 20px;
                               text-decoration: none;
                               border-radius: 20px;
                               margin-right: 10px;
                               font-weight: 500;
                               box-shadow: 0 3px 8px rgba(24, 144, 255, 0.3);
                           ">
                            🛒 立即购买
                        </a>
                        ''' if buy_link else ''}

                        {f'''
                        <a href="{shop_url}"
                           target="_blank"
                           style="
                               display: inline-block;
                               background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
                               color: white;
                               padding: 10px 20px;
                               text-decoration: none;
                               border-radius: 20px;
                               font-weight: 500;
                               box-shadow: 0 3px 8px rgba(82, 196, 26, 0.3);
                           ">
                            🏪 进店看看
                        </a>
                        ''' if shop_url else ''}
                    </div>
                </div>
            </div>
        </div>
        """

    html += """
    </div>
    <style>
        @media (max-width: 768px) {
            div[style*="display: flex"] {
                flex-direction: column !important;
            }
            img, div[style*="width: 220px"] {
                width: 100% !important;
                max-width: 300px !important;
                margin: 0 auto !important;
            }
        }
    </style>
    """

    return html

def parse_and_display_products_safe(agent_response: str):
    """安全地解析商品信息，支持JSON格式和Markdown格式"""
    import re
    import json

    # 首先尝试解析JSON格式的响应
    try:
        # 检查是否是JSON格式
        if agent_response.strip().startswith('[') and agent_response.strip().endswith(']'):
            products_data = json.loads(agent_response)
            if isinstance(products_data, list) and len(products_data) > 0:
                category_data = products_data[0]
                if 'items' in category_data:
                    return generate_html_from_json(category_data['items'])
    except:
        pass

    # 如果不是JSON格式，尝试解析Markdown格式
    # 提取图片URL
    image_pattern = r'!\[图片\]\((http[^)]+)\)'
    images = re.findall(image_pattern, agent_response)

    # 提取购买链接
    link_pattern = r'\[([^\]]+)\]\((http[^)]+)\)'
    links = re.findall(link_pattern, agent_response)

    # 提取价格
    price_pattern = r'价格：¥([0-9,]+\.?[0-9]*)'
    prices = re.findall(price_pattern, agent_response)

    # 提取店铺
    shop_pattern = r'店铺：([^\n]+)'
    shops = re.findall(shop_pattern, agent_response)

    # 提取商品标题
    lines = agent_response.split('\n')
    products_info = []

    current_product = {}
    for line in lines:
        line = line.strip()
        if re.match(r'^\d+\.\s+', line):
            if current_product:
                products_info.append(current_product)
            title = re.sub(r'^\d+\.\s+', '', line)
            current_product = {'title': title}
        elif line.startswith('特点：'):
            current_product['features'] = line.replace('特点：', '').strip()

    if current_product:
        products_info.append(current_product)
    
    # 生成HTML展示
    if not products_info and not images:
        return f"""
        <div style="padding: 20px; background: #f8f9fa; border-radius: 10px; border: 1px solid #dee2e6;">
            <h3 style="color: #495057; margin-bottom: 15px;">🤖 AI助手回复：</h3>
            <div style="background: white; padding: 15px; border-radius: 8px; white-space: pre-wrap; line-height: 1.6; font-family: monospace; font-size: 14px;">
                {agent_response}
            </div>
        </div>
        """
    
    html = """
    <div style="padding: 20px;">
        <h2 style="color: #1890ff; margin-bottom: 25px;">🛍️ 商品推荐结果</h2>
    """
    
    for i, product in enumerate(products_info):
        image_url = images[i] if i < len(images) else ""
        price = prices[i] if i < len(prices) else "价格未知"
        shop = shops[i] if i < len(shops) else "店铺未知"
        
        # 查找对应的购买链接
        buy_link = ""
        for link_text, link_url in links:
            if product['title'] in link_text or link_text in product['title']:
                buy_link = link_url
                break
        
        html += f"""
        <div style="border: 1px solid #e1e5e9; border-radius: 12px; padding: 20px; margin-bottom: 25px; background: white;">
            <div style="display: flex; gap: 20px;">
                <div style="flex-shrink: 0;">
                    {f'<img src="{image_url}" style="width: 200px; height: 200px; object-fit: cover; border-radius: 10px;" alt="商品图片" />' if image_url else '<div style="width: 200px; height: 200px; background: #f0f0f0; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #999;">暂无图片</div>'}
                </div>
                <div style="flex: 1;">
                    <h3 style="color: #2c3e50; margin: 0 0 15px 0;">{product['title']}</h3>
                    <div style="margin-bottom: 15px;">
                        <span style="background: #ff4d4f; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold;">💰 ¥{price}</span>
                    </div>
                    {f'<div style="color: #555; margin: 12px 0;"><strong>🔧 {product.get("features", "")}</strong></div>' if product.get("features") else ''}
                    <div style="color: #666; margin: 10px 0;">🏪 {shop}</div>
                    {f'<a href="{buy_link}" target="_blank" style="display: inline-block; background: #1890ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; margin-top: 15px;">🛒 立即购买</a>' if buy_link else ''}
                </div>
            </div>
        </div>
        """
    
    html += "</div>"
    return html

def search_products_safe(query: str):
    """安全地搜索商品"""
    if not query.strip():
        return "请输入搜索关键词", ""
    
    try:
        print(f"🔍 开始搜索: {query}")
        
        # 创建agent
        agent = create_agent_safely()
        if not agent:
            return "❌ Agent创建失败", "<div style='color: red;'>Agent创建失败</div>"
        
        print("✅ Agent创建成功，开始搜索...")
        
        # 执行搜索
        response = agent.run(query)
        print("✅ 搜索完成")
        
        # 生成HTML展示
        html_display = parse_and_display_products_safe(response)
        
        return response, html_display
        
    except Exception as e:
        error_msg = f"搜索过程中出现错误: {str(e)}"
        print(f"❌ {error_msg}")
        traceback.print_exc()
        
        error_html = f"""
        <div style="padding: 20px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 8px; color: #a8071a;">
            <h3 style="color: #cf1322; margin-bottom: 10px;">❌ 搜索失败</h3>
            <p>{error_msg}</p>
            <details>
                <summary>详细错误信息</summary>
                <pre>{traceback.format_exc()}</pre>
            </details>
        </div>
        """
        return error_msg, error_html

# 创建Gradio界面
try:
    import gradio as gr
    
    with gr.Blocks(title="淘宝商品搜索助手 (独立版)", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🛍️ 淘宝商品搜索助手 (独立版)
        
        🔧 **完全独立版本，避免所有兼容性问题**
        
        输入您想要搜索的商品，AI助手将为您推荐相关商品，**包括商品图片展示**！
        """)
        
        with gr.Row():
            with gr.Column(scale=4):
                query_input = gr.Textbox(
                    label="🔍 搜索关键词",
                    placeholder="请输入您想要搜索的商品，如：2000左右的手机、运动鞋推荐等...",
                    lines=2
                )
            with gr.Column(scale=1):
                search_btn = gr.Button("🚀 开始搜索", variant="primary", size="lg")
        
        with gr.Tabs():
            with gr.TabItem("🎨 商品展示 (带图片)"):
                product_display = gr.HTML(label="商品信息展示")
            
            with gr.TabItem("📝 原始回复"):
                raw_output = gr.Textbox(
                    label="AI助手原始回复",
                    lines=15,
                    max_lines=25
                )
        
        # 绑定搜索事件
        search_btn.click(
            fn=search_products_safe,
            inputs=[query_input],
            outputs=[raw_output, product_display]
        )
        
        query_input.submit(
            fn=search_products_safe,
            inputs=[query_input],
            outputs=[raw_output, product_display]
        )
        
        # 示例查询
        gr.Examples(
            examples=[
                ["2000左右的手机"],
                ["运动鞋推荐"],
                ["秋天水果推荐"],
                ["无线蓝牙耳机"],
                ["学生用品推荐"]
            ],
            inputs=[query_input],
            label="💡 试试这些搜索示例"
        )

    def main():
        """主函数"""
        print("🚀 启动独立版淘宝商品搜索助手...")
        print("=" * 60)
        print("🔧 完全独立版本，避免所有兼容性问题")
        
        # 测试agent创建
        print("\n🧪 测试agent创建...")
        test_agent = create_agent_safely()
        if test_agent:
            print("✅ Agent测试成功")
        else:
            print("❌ Agent测试失败")
            return
        
        # 启动Gradio
        try:
            demo.launch(
                server_name="127.0.0.1",
                server_port=7860,
                share=False,
                show_error=True,
                quiet=False,
                inbrowser=True
            )
        except Exception as e:
            print(f"❌ Gradio启动失败: {e}")
            print("🔄 尝试其他端口...")
            try:
                demo.launch(
                    server_name="127.0.0.1",
                    server_port=7861,
                    share=False,
                    show_error=True,
                    quiet=True,
                    inbrowser=True
                )
            except Exception as e2:
                print(f"❌ 所有端口都失败: {e2}")

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ Gradio导入失败: {e}")
    print("请安装Gradio: pip install gradio")
except Exception as e:
    print(f"❌ 创建界面失败: {e}")
    traceback.print_exc()
