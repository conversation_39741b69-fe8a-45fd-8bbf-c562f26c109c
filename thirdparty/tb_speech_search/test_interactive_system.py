#!/usr/bin/env python3
"""
测试交互式搜索系统
"""

def test_imports():
    """测试导入"""
    print("测试系统组件导入...")
    
    try:
        # 测试基础组件
        from custom_agent import SpeechSearchAgent
        print("✓ SpeechSearchAgent 导入成功")
        
        from interactive_agent import InteractiveSearchAgent, InteractionType, UserInteraction
        print("✓ InteractiveSearchAgent 导入成功")
        
        # 测试Web组件
        import fastapi
        import uvicorn
        import jinja2
        print("✓ Web框架组件导入成功")
        
        from web_ui import WebUIManager
        print("✓ WebUIManager 导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_agent_creation():
    """测试Agent创建"""
    print("\n测试Agent创建...")
    
    try:
        from run import create_intelligent_agent
        from interactive_agent import InteractiveSearchAgent
        
        # 创建基础agent
        base_agent = create_intelligent_agent()
        print("✓ 基础Agent创建成功")
        
        # 创建交互式agent
        interactive_agent = InteractiveSearchAgent(
            model=base_agent.model,
            tools=base_agent.tools,
            max_steps=3,
            name="test_agent"
        )
        print("✓ 交互式Agent创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent创建失败: {e}")
        return False

def test_interaction_logic():
    """测试交互逻辑"""
    print("\n测试交互逻辑...")
    
    try:
        from interactive_agent import InteractiveSearchAgent, InteractionType
        from run import create_intelligent_agent
        
        # 创建agent
        base_agent = create_intelligent_agent()
        agent = InteractiveSearchAgent(
            model=base_agent.model,
            tools=base_agent.tools,
            max_steps=3,
            name="test_agent"
        )
        
        # 测试查询分析
        analysis = agent._analyze_query_clarity("春游要带什么")
        print(f"✓ 查询分析成功: {analysis}")
        
        # 测试交互判断
        should_interact = agent._should_interact_with_user({
            'query_clarity_score': 0.3,
            'needs_clarification': True
        })
        print(f"✓ 交互判断成功: {should_interact}")
        
        return True
        
    except Exception as e:
        print(f"✗ 交互逻辑测试失败: {e}")
        return False

def test_web_ui_creation():
    """测试Web UI创建"""
    print("\n测试Web UI创建...")
    
    try:
        from web_ui import WebUIManager
        
        # 创建Web UI管理器
        ui_manager = WebUIManager()
        print("✓ WebUIManager创建成功")
        
        # 检查FastAPI应用
        app = ui_manager.app
        print(f"✓ FastAPI应用创建成功: {app.title}")
        
        return True
        
    except Exception as e:
        print(f"✗ Web UI创建失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    import os
    
    required_files = [
        'interactive_agent.py',
        'web_ui.py',
        'templates/index.html',
        'run_interactive.py',
        'install_deps.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path} 存在")
    
    if missing_files:
        print(f"✗ 缺少文件: {missing_files}")
        return False
    
    return True

def main():
    """运行所有测试"""
    print("🧪 交互式商品搜索系统测试")
    print("=" * 50)
    
    tests = [
        test_file_structure,
        test_imports,
        test_agent_creation,
        test_interaction_logic,
        test_web_ui_creation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("测试结果:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过 ({passed}/{total})")
        print("\n✅ 交互式搜索系统就绪！")
        print("\n🚀 启动系统:")
        print("  python run_interactive.py")
        print("\n🌐 然后访问: http://127.0.0.1:8000")
        
    else:
        print(f"❌ 部分测试失败 ({passed}/{total})")
        print("\n需要检查失败的组件")

if __name__ == "__main__":
    main()
