#!/usr/bin/env python3
"""
直接测试nullable修复
"""

def test_nullable_fix():
    """测试nullable修复是否有效"""
    print("测试nullable修复...")
    
    # 直接检查文件内容
    try:
        with open('custom_tools.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含nullable: True
        if '"nullable": True' in content:
            print("✓ 文件中包含 'nullable': True")
            
            # 检查是否在正确的位置
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'use_web_search' in line and 'type' in line:
                    # 找到use_web_search定义，检查后续几行
                    for j in range(i, min(i+5, len(lines))):
                        if '"nullable": True' in lines[j]:
                            print("✓ nullable设置在正确位置")
                            return True
            
            print("✗ nullable设置位置不正确")
            return False
        else:
            print("✗ 文件中未找到 'nullable': True")
            return False
            
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return False

def test_original_command():
    """测试原始命令是否能启动"""
    print("\n测试原始命令启动...")
    
    import subprocess
    import sys
    
    try:
        # 使用timeout来避免长时间等待
        cmd = [
            sys.executable, 'run.py',
            '--question=春游带什么东西',
            '--model-id=gpt-4o-0806', 
            '--agent-type=intelligent'
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 设置较短的超时时间，只要能启动不报错就算成功
        result = subprocess.run(
            cmd,
            timeout=10,
            capture_output=True,
            text=True
        )
        
        # 如果没有抛出AssertionError，说明nullable问题已解决
        if "AssertionError" not in result.stderr:
            print("✓ 命令启动成功，没有AssertionError")
            return True
        else:
            print("✗ 仍然有AssertionError")
            print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✓ 命令启动成功（超时但没有错误）")
        return True
    except Exception as e:
        print(f"✗ 命令执行失败: {e}")
        return False

def main():
    """运行测试"""
    print("测试nullable修复效果")
    print("=" * 40)
    
    results = []
    results.append(test_nullable_fix())
    results.append(test_original_command())
    
    print("\n" + "=" * 40)
    print("测试结果:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ 所有测试通过 ({passed}/{total})")
        print("\n修复成功！nullable参数问题已解决。")
        print("原始命令现在应该可以正常运行了。")
    else:
        print(f"✗ 部分测试失败 ({passed}/{total})")
        print("可能还需要进一步调试。")

if __name__ == "__main__":
    main()
