# 智能电商搜索Agent

## 概述

智能电商搜索Agent是对原有SpeechSearchAgent的重新设计，实现了更智能的商品搜索策略。该Agent能够：

1. **智能分析**用户查询的搜索意图和可行性
2. **直接搜索**明确的商品查询，避免不必要的步骤
3. **智能反问**模糊查询，帮助用户明确需求
4. **信息补全**通过web search增强查询效果

## 核心功能

### 1. 查询分析 (Query Analysis)
- 自动判断用户查询是否可以直接搜索出合适商品
- 评估搜索意图的明确程度
- 推荐最优的后续行动策略

### 2. 智能路径选择
- **直接搜索路径**: 对于明确查询（如"苹果手机"），直接调用淘宝主搜
- **反问路径**: 对于模糊查询（如"买个东西"），生成针对性问题
- **增强搜索路径**: 对于需要补全信息的查询，先web search再搜索商品

### 3. 用户交互优化
- 减少不必要的中间步骤
- 提供有针对性的反问和建议
- 根据语音识别可能的错误进行智能纠正

## 文件结构

```
smolagents/examples/tb_speech_search/
├── intelligent_search_agent.py          # 智能搜索Agent主类
├── custom_tools.py                      # 增强的工具集合
├── prompts/intelligent_search_agent.yaml # 智能搜索提示模板
├── run.py                              # 运行脚本（已更新）
├── demo_intelligent_agent.py           # 功能演示脚本
└── README_intelligent_agent.md         # 本文档
```

## 新增工具

### QueryAnalysisTool
分析用户查询的商品搜索意图，返回：
- `can_direct_search`: 是否可直接搜索
- `intent_clarity`: 意图明确度
- `suggested_action`: 建议的下一步行动
- `missing_info`: 缺失的关键信息

### UserQuestionTool  
生成智能反问，帮助用户补全商品信息：
- 根据缺失信息生成针对性问题
- 提供常见选项作为参考
- 可选择性调用web search提供提示

### QueryEnhancementTool
基于网络搜索结果增强查询：
- 优化搜索关键词
- 提取关键特征
- 推荐商品类别

## 使用方法

### 1. 运行智能搜索Agent

```bash
# 使用智能搜索模式（推荐）
python run.py --agent-type intelligent --question "适合夏天穿的跑鞋"

# 使用传统ReAct模式（对比）
python run.py --agent-type react --question "适合夏天穿的跑鞋"
```

### 2. 查看功能演示

```bash
python demo_intelligent_agent.py
```

### 3. 在代码中使用

```python
from run import create_intelligent_agent

# 创建智能搜索agent
agent = create_intelligent_agent(model_id="gpt-4-0409")

# 运行查询
result = agent.run("您的查询")
print(result)
```

## 工作流程示例

### 场景1: 明确查询
```
用户: "苹果手机"
1. 查询分析 → 意图明确，可直接搜索
2. 淘宝主搜 → 返回iPhone相关商品
```

### 场景2: 模糊查询  
```
用户: "买个东西"
1. 查询分析 → 意图不明确
2. 智能反问 → "您想要什么类型的商品呢？"
3. 用户回复 → 根据回复进行搜索
```

### 场景3: 需要补全信息
```
用户: "春游需要什么"
1. 查询分析 → 需要补全信息
2. 网络搜索 → 搜索春游必备物品
3. 查询增强 → 生成优化的搜索词
4. 分类搜索 → 搜索帐篷、野餐垫等
```

## 优势对比

| 特性 | 传统ReAct | 智能搜索Agent |
|------|-----------|---------------|
| 明确查询处理 | 4-5步骤 | 2步骤 |
| 模糊查询处理 | 可能给出无关结果 | 智能反问获取需求 |
| 用户体验 | 步骤冗余 | 路径优化 |
| 错误处理 | 试错式 | 预判式 |
| 语音识别适配 | 有限 | 专门优化 |

## 配置说明

### 模型配置
在`run.py`中可以配置不同的模型：
- `gpt-4-0409`: 默认模型，平衡性能和成本
- `o1`: 高推理能力模型
- 其他支持的模型

### 工具配置
可以根据需要调整工具集合：
- 添加或移除特定搜索工具
- 调整工具参数
- 自定义工具行为

## 扩展开发

### 添加新的分析维度
在`QueryAnalysisTool`中可以扩展分析维度：
```python
# 添加价格敏感度分析
"price_sensitivity": "high/medium/low"
# 添加品牌偏好分析  
"brand_preference": ["品牌1", "品牌2"]
```

### 自定义反问策略
在`UserQuestionTool`中可以定制反问逻辑：
```python
# 根据用户历史偏好生成问题
# 结合上下文信息优化问题
```

### 增强搜索策略
在`QueryEnhancementTool`中可以添加：
```python
# 同义词扩展
# 相关商品推荐
# 价格区间建议
```

## 注意事项

1. **API依赖**: 需要配置相应的API密钥和端点
2. **模型选择**: 不同模型的推理能力和成本不同
3. **网络环境**: 某些功能需要稳定的网络连接
4. **错误处理**: 建议在生产环境中增加更完善的错误处理

## 未来改进

- [ ] 添加用户偏好学习
- [ ] 支持多轮对话上下文
- [ ] 集成更多电商平台
- [ ] 优化语音识别错误处理
- [ ] 添加商品比价功能
