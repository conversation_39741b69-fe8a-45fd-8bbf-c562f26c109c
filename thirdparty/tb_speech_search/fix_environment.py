#!/usr/bin/env python3
"""
环境修复脚本 - 解决网络连接问题
"""

import os
import sys
import subprocess
import socket

def set_environment_variables():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 设置NO_PROXY
    no_proxy_entries = ['localhost', '127.0.0.1', '0.0.0.0', '.local']
    no_proxy_value = ','.join(no_proxy_entries)
    
    os.environ['NO_PROXY'] = no_proxy_value
    os.environ['no_proxy'] = no_proxy_value
    
    print(f"✅ 已设置 NO_PROXY: {no_proxy_value}")
    
    # 清除可能冲突的代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    for var in proxy_vars:
        if var in os.environ:
            original_value = os.environ[var]
            print(f"⚠️ 发现代理设置 {var}: {original_value}")
            # 不删除，但记录
    
    # 设置Python相关环境变量
    os.environ['PYTHONHTTPSVERIFY'] = '0'  # 禁用SSL验证
    os.environ['CURL_CA_BUNDLE'] = ''      # 清除CA bundle
    os.environ['REQUESTS_CA_BUNDLE'] = ''  # 清除requests CA bundle
    
    print("✅ 环境变量设置完成")

def check_network_connectivity():
    """检查网络连接"""
    print("\n🔍 检查网络连接...")
    
    # 检查localhost连接
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 80))
        sock.close()
        
        if result == 0:
            print("✅ localhost连接正常")
        else:
            print("⚠️ localhost连接可能有问题")
    except Exception as e:
        print(f"❌ localhost连接测试失败: {e}")
    
    # 检查外网连接
    try:
        import urllib.request
        urllib.request.urlopen('http://httpbin.org/ip', timeout=10)
        print("✅ 外网连接正常")
    except Exception as e:
        print(f"⚠️ 外网连接问题: {e}")

def kill_conflicting_processes():
    """终止可能冲突的进程"""
    print("\n🔧 检查端口占用...")
    
    ports_to_check = [7860, 7861, 8080, 8443]
    
    for port in ports_to_check:
        try:
            # 使用lsof检查端口占用
            result = subprocess.run(['lsof', '-ti', f':{port}'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                print(f"⚠️ 端口{port}被占用，PID: {', '.join(pids)}")
                
                # 询问是否终止进程
                for pid in pids:
                    try:
                        # 获取进程信息
                        ps_result = subprocess.run(['ps', '-p', pid, '-o', 'comm='], 
                                                 capture_output=True, text=True, timeout=5)
                        process_name = ps_result.stdout.strip()
                        print(f"  PID {pid}: {process_name}")
                        
                        # 如果是Python进程，可能是之前的Gradio实例
                        if 'python' in process_name.lower():
                            print(f"  🔄 终止可能的Gradio进程 {pid}...")
                            subprocess.run(['kill', pid], timeout=5)
                            print(f"  ✅ 已终止进程 {pid}")
                    except Exception as e:
                        print(f"  ❌ 处理PID {pid}失败: {e}")
            else:
                print(f"✅ 端口{port}可用")
                
        except FileNotFoundError:
            print("⚠️ lsof命令不可用，跳过端口检查")
            break
        except Exception as e:
            print(f"❌ 检查端口{port}失败: {e}")

def create_minimal_test():
    """创建最小测试"""
    print("\n🧪 创建最小测试...")
    
    test_code = '''
import gradio as gr
import os

# 设置环境变量
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'

def hello(name):
    return f"Hello {name}!"

# 创建最简界面
demo = gr.Interface(
    fn=hello,
    inputs=gr.Textbox(label="输入您的名字"),
    outputs=gr.Textbox(label="输出"),
    title="网络连接测试"
)

if __name__ == "__main__":
    print("🧪 启动最小测试...")
    try:
        demo.launch(
            server_name="127.0.0.1",
            server_port=7862,
            share=False,
            show_error=True,
            quiet=False,
            inbrowser=False
        )
        print("✅ 最小测试成功！")
    except Exception as e:
        print(f"❌ 最小测试失败: {e}")
'''
    
    with open('minimal_test.py', 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print("✅ 已创建 minimal_test.py")

def run_minimal_test():
    """运行最小测试"""
    print("\n🧪 运行最小测试...")
    
    try:
        result = subprocess.run([sys.executable, 'minimal_test.py'], 
                              timeout=30, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 最小测试通过")
            return True
        else:
            print(f"❌ 最小测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 最小测试超时（可能是正常的，说明服务启动了）")
        return True
    except Exception as e:
        print(f"❌ 运行最小测试失败: {e}")
        return False

def create_launch_script():
    """创建启动脚本"""
    print("\n📝 创建启动脚本...")
    
    script_content = f'''#!/bin/bash

# 设置环境变量
export NO_PROXY="localhost,127.0.0.1,0.0.0.0"
export no_proxy="localhost,127.0.0.1,0.0.0.0"
export PYTHONHTTPSVERIFY=0

# 终止可能冲突的进程
echo "🔧 清理环境..."
pkill -f "gradio"
pkill -f "app_"

# 等待进程终止
sleep 2

# 启动应用
echo "🚀 启动应用..."
cd "{os.getcwd()}"
python app_fixed_final.py

echo "✅ 启动完成"
'''
    
    with open('start_app.sh', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod('start_app.sh', 0o755)
    
    print("✅ 已创建 start_app.sh")
    print("💡 使用方法: ./start_app.sh")

def main():
    """主函数"""
    print("🔧 环境修复脚本")
    print("=" * 60)
    
    # 1. 设置环境变量
    set_environment_variables()
    
    # 2. 检查网络连接
    check_network_connectivity()
    
    # 3. 清理端口占用
    kill_conflicting_processes()
    
    # 4. 创建测试文件
    create_minimal_test()
    
    # 5. 运行最小测试
    test_passed = run_minimal_test()
    
    # 6. 创建启动脚本
    create_launch_script()
    
    print("\n" + "=" * 60)
    print("🎯 环境修复完成！")
    
    if test_passed:
        print("✅ 网络环境正常，可以启动应用")
        print("\n🚀 推荐启动方式:")
        print("1. python app_fixed_final.py")
        print("2. ./start_app.sh")
        print("3. python minimal_test.py (测试用)")
    else:
        print("⚠️ 网络环境可能仍有问题")
        print("\n🛠️ 建议:")
        print("1. 检查系统代理设置")
        print("2. 重启终端后重试")
        print("3. 使用 ./start_app.sh 启动")
    
    print(f"\n🌐 预期访问地址:")
    print("- http://127.0.0.1:7860")
    print("- http://127.0.0.1:7861")
    print("- http://127.0.0.1:8080")

if __name__ == "__main__":
    main()
