#!/usr/bin/env python3
"""
测试智能搜索Agent的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run import create_intelligent_agent

def test_queries():
    """测试不同类型的查询"""
    
    # 创建智能搜索agent
    print("正在创建智能搜索Agent...")
    try:
        agent = create_intelligent_agent()
        print("✓ Agent创建成功")
    except Exception as e:
        print(f"✗ Agent创建失败: {e}")
        return
    
    # 测试查询列表
    test_cases = [
        {
            "query": "苹果手机",
            "expected_behavior": "应该直接搜索，因为查询明确"
        },
        {
            "query": "适合夏天穿的跑鞋",
            "expected_behavior": "应该直接搜索，查询相对明确"
        },
        {
            "query": "买个东西",
            "expected_behavior": "应该反问用户，查询太模糊"
        },
        {
            "query": "春游需要什么",
            "expected_behavior": "可能需要web search补全信息"
        }
    ]
    
    print("\n开始测试不同类型的查询...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case["query"]
        expected = test_case["expected_behavior"]
        
        print(f"\n测试 {i}: {query}")
        print(f"预期行为: {expected}")
        print("-" * 40)
        
        try:
            # 运行agent
            result = agent.run(query)
            print(f"结果: {result}")
            print("✓ 测试完成")
        except Exception as e:
            print(f"✗ 测试失败: {e}")
        
        print("-" * 40)

if __name__ == "__main__":
    test_queries()
