#!/usr/bin/env python3
"""
测试修复后的QueryAnalysisTool
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_query_analysis():
    """测试查询分析工具"""
    print("🧪 测试修复后的QueryAnalysisTool...")
    
    try:
        from custom_tools import QueryAnalysisTool
        from custom_model import CustomModel
        
        # 创建模型和工具
        model = CustomModel(
            model_id="gpt-4-0409",
            api_base="https://api.openai.com/v1",
            api_key=os.getenv("OPENAI_API_KEY", "your-api-key")
        )
        
        tool = QueryAnalysisTool(model)
        
        # 测试用例
        test_cases = [
            "2000左右的手机 电子产品",
            "手机推荐", 
            "秋天水果推荐",
            "买个东西",
            "iPhone 15",
            "适合学生的笔记本电脑",
            "运动鞋",
            "便宜的耳机"
        ]
        
        print("\n📋 测试结果:")
        print("=" * 80)
        
        for i, query in enumerate(test_cases, 1):
            print(f"\n{i}. 查询: '{query}'")
            print("-" * 40)
            
            try:
                result = tool.forward(query)
                print(f"结果: {result}")
                
                # 解析结果
                import json
                parsed = json.loads(result)
                intent = parsed.get('intent_clarity', 'unknown')
                action = parsed.get('suggested_action', 'unknown')
                reason = parsed.get('analysis_reason', 'unknown')
                
                # 判断结果是否合理
                if query in ["2000左右的手机 电子产品", "手机推荐", "iPhone 15", "运动鞋"]:
                    expected = "clear"
                    if intent == expected:
                        print("✅ 分析正确")
                    else:
                        print(f"❌ 分析错误，期望: {expected}, 实际: {intent}")
                elif query in ["买个东西"]:
                    expected = "unclear"
                    if intent == expected:
                        print("✅ 分析正确")
                    else:
                        print(f"❌ 分析错误，期望: {expected}, 实际: {intent}")
                else:
                    print(f"📊 意图: {intent}, 行动: {action}")
                    
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 80)
        print("🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_enhanced_agent():
    """测试增强型agent是否能正确处理查询"""
    print("\n🧪 测试增强型Agent...")
    
    try:
        from run import create_enhanced_agent
        
        # 创建增强型agent
        agent = create_enhanced_agent(model_id="gpt-4-0409")
        
        # 测试问题查询
        query = "2000左右的手机"
        print(f"\n🔍 测试查询: {query}")
        
        # 运行agent（限制步数避免无限循环）
        result = agent.run(query, max_steps=6)
        
        print(f"\n✅ Agent执行完成")
        print(f"📄 最终结果: {result}")
        
        # 检查是否包含商品信息
        if isinstance(result, str):
            has_product_info = any(keyword in result.lower() for keyword in [
                '¥', '店铺', '付款', 'price', '商品', '销量', '图片'
            ])
            
            if has_product_info:
                print("✅ 结果包含商品信息")
            else:
                print("⚠️ 结果可能缺少商品信息")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 测试查询分析工具
    test_query_analysis()
    
    # 测试增强型agent
    test_enhanced_agent()
