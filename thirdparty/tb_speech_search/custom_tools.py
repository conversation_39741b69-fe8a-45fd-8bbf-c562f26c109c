# 基于smolagents实现custom tools

from smolagents import Tool
import requests
import json


# TODO: 支持并发
class TaobaoMainSearchTool(Tool):
    """
    主搜接口（替换{query}部分）：https://tppwork.taobao.com/center/recommend?appid=36935&code=aiSrp_itemSearchTool&_input_charset=UTF-8&_output_charset=UTF-8&searchType=recall&from=voiceTest&outputType=recall&userId=&closeItemRelevanceCheck=true&gotoOld=true&q={query}
    """
    name = "taobao_main_search"
    description = "淘宝主搜工具，基于关键词搜索商品，返回商品介绍、价格等信息。"
    inputs = {
        "query": {
            "type": "string",
            "description": "给定关键词，在淘宝上搜索相关商品，返回商品介绍、价格等信息。",
        }
    }
    output_type = "string"
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.url = "https://tppwork.taobao.com/center/recommend?appid=36935&code=aiSrp_itemSearchTool&_input_charset=UTF-8&_output_charset=UTF-8&searchType=recall&from=voiceTest&outputType=recall&userId=&closeItemRelevanceCheck=true&gotoOld=true&q={query}"

    def forward(self, query: str) -> str:
        url = self.url.format(query=query)
        response = requests.get(url)

        return response.json()['result']


class QueryAnalysisTool(Tool):
    """
    查询分析工具，判断用户查询是否可以直接搜索商品，以及搜索意图是否明确
    """
    name = "query_analysis"
    description = "分析用户查询的商品搜索意图，判断是否可以直接搜索以及意图是否明确。"
    inputs = {
        "query": {
            "type": "string",
            "description": "用户的原始查询文本",
        }
    }
    output_type = "string"

    def __init__(self, model, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model = model

    def forward(self, query: str) -> str:
        try:
            # 确保参数是字符串类型
            if not isinstance(query, str):
                query = str(query)

            analysis_prompt = f"""
            请分析以下用户查询的商品搜索意图：

            用户查询："{query}"

            分析规则：
            1. 如果查询中包含明确的商品类型（如：手机、电脑、衣服、鞋子、包包等），即使有其他模糊词汇，也应该认为意图明确
            2. 如果查询中包含价格范围（如：2000左右、100元以内等），这是有用的筛选条件，不是缺失信息
            3. 只有当查询完全没有商品类型指向时，才认为意图不明确
            4. 对于推荐类查询（如：秋天水果推荐），应该先web搜索了解相关信息

            请从以下维度进行分析，并返回JSON格式的结果：

            1. can_direct_search: 是否可以直接在淘宝搜索到合适的商品（true/false）
            2. intent_clarity: 搜索意图是否明确（"clear"/"unclear"/"no_intent"）
            3. analysis_reason: 分析原因的简短说明
            4. suggested_action: 建议的下一步行动（"direct_search"/"ask_user"/"web_search_then_search"）
            5. missing_info: 如果意图不明确，列出缺失的关键信息

            示例：
            - "2000左右的手机" → intent_clarity: "clear", suggested_action: "direct_search"
            - "手机推荐" → intent_clarity: "clear", suggested_action: "direct_search"
            - "秋天水果推荐" → intent_clarity: "clear", suggested_action: "web_search_then_search"
            - "买个东西" → intent_clarity: "unclear", suggested_action: "ask_user"

            返回格式：
            {{
                "can_direct_search": true/false,
                "intent_clarity": "clear/unclear/no_intent",
                "analysis_reason": "分析原因",
                "suggested_action": "direct_search/ask_user/web_search_then_search",
                "missing_info": ["缺失信息1", "缺失信息2"]
            }}
            """

            messages = [{"role": "user", "content": [{"type": "text", "text": analysis_prompt}]}]
            response = self.model.generate(messages)

            try:
                # 尝试解析JSON响应
                result = json.loads(response.content)
                return json.dumps(result, ensure_ascii=False)
            except:
                # 如果解析失败，返回默认的保守策略
                return json.dumps({
                    "can_direct_search": False,
                    "intent_clarity": "unclear",
                    "analysis_reason": "无法准确分析用户意图",
                    "suggested_action": "ask_user",
                    "missing_info": ["商品类型", "具体需求"]
                }, ensure_ascii=False)

        except Exception as e:
            # 捕获所有异常并返回错误信息
            return json.dumps({
                "can_direct_search": False,
                "intent_clarity": "unclear",
                "analysis_reason": f"分析过程出错: {str(e)}",
                "suggested_action": "ask_user",
                "missing_info": ["商品类型", "具体需求"]
            }, ensure_ascii=False)


class UserQuestionTool(Tool):
    """
    用户反问工具，当搜索意图不明确时，生成合适的反问来补全信息
    """
    name = "ask_user_question"
    description = "当用户查询意图不明确时，生成合适的反问来获取更多信息。"
    inputs = {
        "original_query": {
            "type": "string",
            "description": "用户的原始查询",
        },
        "missing_info": {
            "type": "string",
            "description": "缺失的信息列表（JSON格式）",
        },
        "use_web_search": {
            "type": "boolean",
            "description": "是否需要先进行网络搜索来提供选项提示",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, model, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model = model

    def forward(self, original_query: str, missing_info: str, use_web_search: bool = False) -> str:
        # 确保参数类型正确
        if missing_info is None:
            missing_info = '[]'
        if not isinstance(original_query, str):
            original_query = str(original_query)
        if not isinstance(missing_info, str):
            missing_info = str(missing_info)

        question_prompt = f"""
        用户的原始查询："{original_query}"
        缺失的信息：{missing_info}
        是否需要网络搜索提示：{use_web_search}

        请生成一个友好、有帮助的反问，帮助用户补全商品搜索信息。
        反问应该：
        1. 简洁明了
        2. 针对缺失的关键信息
        3. 提供一些常见选项作为参考（如果适用）
        4. 语气友好自然

        直接返回反问内容，不需要其他格式。
        """

        messages = [{"role": "user", "content": [{"type": "text", "text": question_prompt}]}]
        response = self.model.generate(messages)

        return response.content

class WebSearchTool(Tool):
    """
    网络搜索工具，用于搜索相关信息
    """
    name = "custom_web_search"
    description = "搜索网络信息，获取相关内容来帮助理解用户需求。"
    inputs = {
        "query": {
            "type": "string",
            "description": "搜索查询关键词",
        }
    }
    output_type = "string"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def forward(self, query: str) -> str:
        try:
            # 确保参数是字符串类型
            if not isinstance(query, str):
                query = str(query)

            # 尝试使用search_summary工具
            try:
                from search_agent import llm_summary
                summary = llm_summary(query)
                # 检查是否出现错误
                if "pluginModeOutPutString" in str(summary) or "没有找到" in str(summary):
                    # 如果search_agent出现问题，使用SearchSummaryTool作为备选
                    print(f"⚠️ search_agent出现问题，切换到SearchSummaryTool")
                    return self._fallback_search(query)
                return f"网络搜索结果：{summary}"
            except Exception as e:
                print(f"⚠️ search_agent调用失败: {e}，切换到SearchSummaryTool")
                return self._fallback_search(query)

        except Exception as e:
            return f"搜索过程中出现错误: {str(e)}"

    def _fallback_search(self, query: str) -> str:
        """备选搜索方法，使用SearchSummaryTool"""
        try:
            # 创建SearchSummaryTool实例并调用
            search_tool = SearchSummaryTool()
            result = search_tool.forward(query)
            return f"网络搜索结果（备选）：{result}"
        except Exception as e:
            # 如果备选方案也失败，返回基础信息
            return f"关于'{query}'的搜索结果：建议查找相关商品信息和用户评价。由于网络搜索暂时不可用，请直接搜索相关商品。"


class SearchSummaryTool(Tool):
    name = "search_summary"
    description = "搜索结果摘要工具，基于关键词搜索，返回搜索结果的摘要信息。"
    inputs = {
        "query": {
            "type": "string",
            "description": "给定关键词，使用夸克浏览器接口搜索关键词相关信息，并给出搜索总结",
        }
    }
    output_type = "string"
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def forward(self, query: str) -> str:
        # 简化实现，避免复杂的依赖
        try:
            # 尝试导入并使用llm_summary
            from search_agent import llm_summary
            summary = llm_summary(query)
            return summary
        except Exception:
            # 如果导入失败，返回简单的搜索提示
            return f"关于'{query}'的搜索摘要：建议搜索相关商品信息。"


class QueryEnhancementTool(Tool):
    """
    查询增强工具，基于网络搜索结果来增强和优化用户的搜索查询
    """
    name = "query_enhancement"
    description = "基于网络搜索结果来增强和优化用户的商品搜索查询。"
    inputs = {
        "original_query": {
            "type": "string",
            "description": "用户的原始查询",
        },
        "web_search_results": {
            "type": "string",
            "description": "网络搜索的结果内容",
        }
    }
    output_type = "string"

    def __init__(self, model, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model = model

    def forward(self, original_query: str, web_search_results: str) -> str:
        # 确保参数类型正确
        if web_search_results is None:
            web_search_results = ''
        if not isinstance(original_query, str):
            original_query = str(original_query)
        if not isinstance(web_search_results, str):
            web_search_results = str(web_search_results)

        enhancement_prompt = f"""
        用户原始查询："{original_query}"

        网络搜索结果：
        {web_search_results}

        基于网络搜索结果，请优化用户的查询，使其更适合在淘宝等电商平台搜索商品。

        请返回JSON格式的结果：
        {{
            "enhanced_queries": ["优化后的查询1", "优化后的查询2", "优化后的查询3"],
            "key_features": ["关键特征1", "关键特征2"],
            "recommended_categories": ["推荐类别1", "推荐类别2"]
        }}
        """

        messages = [{"role": "user", "content": [{"type": "text", "text": enhancement_prompt}]}]
        response = self.model.generate(messages)

        try:
            result = json.loads(response.content)
            return json.dumps(result, ensure_ascii=False)
        except:
            # 如果解析失败，返回原始查询
            return json.dumps({
                "enhanced_queries": [original_query],
                "key_features": [],
                "recommended_categories": []
            }, ensure_ascii=False)


class TaobaoItemDetailsTool(Tool):
    """
    淘宝商品详情工具
    根据itemId获取商品详细信息
    """
    name = "taobao_item_details"
    description = "根据商品ID列表获取淘宝商品的详细信息，包括标题、价格、图片、描述等"
    inputs = {
        "item_id_list": {
            "type": "string",
            "description": "商品ID列表，格式为JSON字符串，例如：[\"811748752470\", \"932739107635\"]",
        },
        "query": {
            "type": "string",
            "description": "搜索关键词，用于spanQuery和q参数",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.base_url = "https://tppwork.taobao.com/center/recommend"

    def forward(self, item_id_list: str, query: str = "") -> str:
        """
        获取商品详细信息

        Args:
            item_id_list: JSON格式的商品ID列表
            query: 搜索关键词，用于spanQuery和q参数

        Returns:
            str: JSON格式的商品详情
        """
        try:
            # 解析商品ID列表
            if isinstance(item_id_list, str):
                try:
                    item_ids = json.loads(item_id_list)
                except:
                    # 如果解析失败，尝试按逗号分割
                    item_ids = [id.strip() for id in item_id_list.split(',')]
            else:
                item_ids = item_id_list

            print(f"🔍 商品详情工具：准备获取商品详情，ID列表: {item_ids}")
            print(f"🔍 搜索关键词: {query}")

            # 将ID列表转换为正确的格式 - 确保是字符串数组
            item_ids_str = [str(id) for id in item_ids]
            ids_str = json.dumps(item_ids_str)
            print(f"🔍 格式化后的ID参数: {ids_str}")

            # 构建请求参数，参考工作的URL参数

            params = {
                "_sid_": "106323",
                "appid": "36935",
                "_input_charset": "UTF-8",
                "_output_charset": "UTF-8",
                "DEBUG_": "true",
                "logPassThrough": "true",
                "_invoke_ttl_": "-1",
                "spanQuery": query,  # 不需要手动编码，requests会自动处理
                "_timeout_": "5000",
                "_mtop_rl_url_": "true",
                "code": "showItemCardById",
                "chatId": "b3ae5cfa59ac907febbb4741f3f771be",
                "channelSrp": "ai_srp_assistant",
                "mainAgentRn": "45a882d640c266e34e7063f6b029a8ec",
                "sessionId": "8482daf82ebaa92fb63d8e6a1f8e0aa4",
                "ttid": "600000@taobao_android_10.49.10.1002",
                "nick": "大壮",  # 使用原始中文，让requests自动编码
                "q": query,
                "entranceType": "voice",
                "vm": "nw",
                "clientip": "*************",
                "sversion": "24.5",
                "ids": ids_str,
                "itemIdList": ids_str,
                "style": "wf",
                "page": "1",
                "globalMemory": json.dumps([{"role":"user","content":query}])
            }

            print(f"🔍 请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")

            response = requests.get(self.base_url, params=params, timeout=10)
            print(f"🔍 请求URL: {response.url}")
            print(f"🔍 响应状态码: {response.status_code}")
            response.raise_for_status()  # 如果请求不成功，抛出异常

            result = response.json()
            print(f"✅ 商品详情API调用成功，返回数据类型: {type(result)}")
            print(f"🔍 API返回的原始数据结构: {json.dumps(result, ensure_ascii=False, indent=2)[:500]}...")

            # 返回result字段中的商品详情
            details = result.get('result', [])
            print(f"📦 获取到商品详情数量: {len(details) if isinstance(details, list) else 'N/A'}")

            if isinstance(details, list) and len(details) > 0:
                print(f"🔍 第一个详情项示例: {json.dumps(details[0], ensure_ascii=False, indent=2)[:300]}...")

            return json.dumps(details, ensure_ascii=False, indent=2)

        except requests.RequestException as e:
            print(f"❌ 商品详情API请求发生错误: {e}")
            return json.dumps({"error": f"API请求错误: {str(e)}"}, ensure_ascii=False)
        except Exception as e:
            print(f"❌ 获取商品详情时出错: {e}")
            return json.dumps({"error": f"处理错误: {str(e)}"}, ensure_ascii=False)


if __name__ == "__main__":
    import json

    # 测试调试流程：先用主搜获取商品ID，再用详情工具获取详情
    print("=== 调试流程测试 ===")

    # 步骤1：使用主搜工具获取商品ID
    print("步骤1：使用TaobaoMainSearchTool搜索'口香糖'")
    main_search_tool = TaobaoMainSearchTool()
    search_result = main_search_tool.forward("口香糖")
    print(f"主搜结果类型: {type(search_result)}")

    # 提取商品ID
    item_ids = []
    if isinstance(search_result, dict) and 'itemList' in search_result:
        item_list = search_result['itemList']
        item_ids = [item.get('itemId') for item in item_list[:5] if item.get('itemId')]
        print(f"提取到的商品ID: {item_ids}")
    else:
        print("搜索结果格式不符合预期，使用测试ID")
        item_ids = ["600591248086", "890937845085", "637939793623"]

    # 步骤2：使用详情工具获取商品详情
    print("\n步骤2：使用TaobaoItemDetailsTool获取商品详情")
    detail_tool = TaobaoItemDetailsTool()
    detail_result = detail_tool.forward(json.dumps(item_ids), "口香糖")
    print("详情结果:")
    print(detail_result[:1000] + "..." if len(detail_result) > 1000 else detail_result)
