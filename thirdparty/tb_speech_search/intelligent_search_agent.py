import json
import time
import yaml
from typing import Generator, Any, Callable

from smolagents.agents import MultiStepAgent, PromptTemplates, populate_template, FinalOutput
from smolagents.memory import ActionStep, ChatMessage, MessageRole
from smolagents.tools import Tool
from smolagents.monitoring import LogLevel, YELLOW_HEX
from rich.panel import Panel
from rich.text import Text


class IntelligentSearchAgent(MultiStepAgent):
    """
    智能商品搜索代理，能够：
    1. 判断查询是否可以直接搜索商品
    2. 对于可直接搜索的查询，直接调用淘宝主搜
    3. 对于不明确的查询，智能反问用户或使用web search补全信息
    """

    def __init__(
        self,
        tools: list[Tool],
        model: Callable[[list[dict[str, str]]], ChatMessage],
        prompt_templates: PromptTemplates | None = None,
        planning_interval: int | None = None,
        **kwargs,
    ):
        prompt_templates = prompt_templates or yaml.safe_load(
            open('prompts/intelligent_search_agent.yaml').read()
        )
        super().__init__(
            tools=tools,
            model=model,
            prompt_templates=prompt_templates,
            planning_interval=planning_interval,
            **kwargs,
        )
        
        # 状态跟踪
        self.current_phase = "analysis"  # analysis, direct_search, clarification, enhancement, final_search
        self.query_analysis_result = None
        self.enhanced_queries = []
        self.user_clarification = None

    def initialize_system_prompt(self) -> str:
        system_prompt = populate_template(
            self.prompt_templates["system_prompt"],
            variables={"tools": self.tools, "managed_agents": self.managed_agents},
        )
        return system_prompt

    def _determine_next_action(self, task: str) -> dict:
        """
        根据当前状态和任务确定下一步行动
        """
        if self.current_phase == "analysis":
            return {
                "tool_name": "query_analysis",
                "arguments": {"query": task},
                "reason": "分析用户查询的搜索意图"
            }
        
        elif self.current_phase == "direct_search":
            return {
                "tool_name": "taobao_main_search", 
                "arguments": {"query": task},
                "reason": "直接搜索商品"
            }
            
        elif self.current_phase == "clarification":
            # 需要反问用户
            missing_info = self.query_analysis_result.get("missing_info", [])
            return {
                "tool_name": "ask_user_question",
                "arguments": {
                    "original_query": task,
                    "missing_info": json.dumps(missing_info, ensure_ascii=False),
                    "use_web_search": False
                },
                "reason": "向用户询问更多信息"
            }
            
        elif self.current_phase == "enhancement":
            return {
                "tool_name": "web_search",
                "arguments": {"query": task + " 商品推荐"},
                "reason": "搜索相关信息来增强查询"
            }
            
        elif self.current_phase == "final_search":
            # 使用增强后的查询进行搜索
            if self.enhanced_queries:
                query = self.enhanced_queries[0]  # 使用第一个增强查询
            else:
                query = task
            return {
                "tool_name": "taobao_main_search",
                "arguments": {"query": query},
                "reason": "使用增强后的查询搜索商品"
            }
        
        # 默认情况
        return {
            "tool_name": "final_answer",
            "arguments": {"answer": "抱歉，无法处理您的请求。"},
            "reason": "无法确定下一步行动"
        }

    def _update_phase_based_on_result(self, tool_name: str, result: Any):
        """
        根据工具执行结果更新当前阶段
        """
        if tool_name == "query_analysis":
            try:
                analysis = json.loads(result) if isinstance(result, str) else result
                self.query_analysis_result = analysis
                
                suggested_action = analysis.get("suggested_action", "ask_user")
                
                if suggested_action == "direct_search":
                    self.current_phase = "direct_search"
                elif suggested_action == "ask_user":
                    self.current_phase = "clarification"
                elif suggested_action == "web_search_then_search":
                    self.current_phase = "enhancement"
                else:
                    self.current_phase = "clarification"
                    
            except Exception as e:
                self.logger.log(f"解析查询分析结果失败: {e}", level=LogLevel.WARNING)
                self.current_phase = "clarification"
                
        elif tool_name == "taobao_main_search":
            # 搜索完成，准备返回结果
            self.current_phase = "completed"
            
        elif tool_name == "ask_user_question":
            # 已经向用户提问，等待用户回复
            self.current_phase = "waiting_user_response"
            
        elif tool_name == "web_search":
            # 网络搜索完成，准备增强查询
            self.current_phase = "query_enhancement"
            
        elif tool_name == "query_enhancement":
            try:
                enhancement = json.loads(result) if isinstance(result, str) else result
                self.enhanced_queries = enhancement.get("enhanced_queries", [])
                self.current_phase = "final_search"
            except:
                self.current_phase = "final_search"

    def _step_stream(self, memory_step: ActionStep) -> Generator[FinalOutput, None, None]:
        """
        执行一个智能搜索步骤
        """
        memory_messages = self.write_memory_to_messages()
        input_messages = memory_messages.copy()
        memory_step.model_input_messages = input_messages

        # 确定下一步行动
        next_action = self._determine_next_action(self.task)
        tool_name = next_action["tool_name"]
        tool_arguments = next_action["arguments"]
        reason = next_action["reason"]

        self.logger.log(
            Panel(Text(f"执行: {tool_name} - {reason}\n参数: {tool_arguments}")),
            level=LogLevel.INFO,
        )

        # 模拟工具调用消息
        chat_message = ChatMessage(
            role=MessageRole.ASSISTANT,
            content=f"调用工具: {tool_name}",
            tool_calls=[type('ToolCall', (), {
                'function': type('Function', (), {
                    'name': tool_name,
                    'arguments': tool_arguments
                })(),
                'id': f"call_{int(time.time())}"
            })()]
        )

        memory_step.model_output_message = chat_message
        memory_step.model_output = f"Called Tool: '{tool_name}' with arguments: {tool_arguments}"
        memory_step.tool_calls = [type('ToolCall', (), {
            'name': tool_name,
            'arguments': tool_arguments,
            'id': chat_message.tool_calls[0].id
        })()]

        # 执行工具
        if tool_name == "final_answer":
            answer = tool_arguments.get("answer", "")
            self.logger.log(
                Text(f"Final answer: {answer}", style=f"bold {YELLOW_HEX}"),
                level=LogLevel.INFO,
            )
            memory_step.action_output = answer
            yield FinalOutput(output=answer)
        else:
            try:
                observation = self.execute_tool_call(tool_name, tool_arguments)
                self.logger.log(
                    f"Observations: {str(observation)[:200]}...",
                    level=LogLevel.INFO,
                )
                memory_step.observations = str(observation)
                
                # 更新阶段状态
                self._update_phase_based_on_result(tool_name, observation)

                # 检查是否需要结束
                if self.current_phase == "completed":
                    # 格式化商品搜索结果并结束
                    final_answer = self._format_search_results(observation)
                    yield FinalOutput(output=final_answer)
                elif self.current_phase == "waiting_user_response":
                    # 返回用户问题
                    yield FinalOutput(output=observation)
                else:
                    yield FinalOutput(output=None)

            except Exception as e:
                error_msg = f"工具执行失败: {e}"
                self.logger.log(error_msg, level=LogLevel.ERROR)
                memory_step.observations = error_msg
                yield FinalOutput(output=f"抱歉，执行过程中出现错误: {error_msg}")

    def _format_search_results(self, search_results: str) -> str:
        """
        格式化淘宝搜索结果
        """
        try:
            # 尝试解析搜索结果
            if isinstance(search_results, str):
                # 如果是字符串，尝试解析为JSON
                try:
                    results = json.loads(search_results)
                except:
                    results = search_results
            else:
                results = search_results
                
            return f"为您找到以下商品：\n\n{results}"
        except Exception:
            return f"搜索完成，结果：{search_results}"
