
import gradio as gr
import os

# 设置环境变量
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'

def hello(name):
    return f"Hello {name}!"

# 创建最简界面
demo = gr.Interface(
    fn=hello,
    inputs=gr.Textbox(label="输入您的名字"),
    outputs=gr.Textbox(label="输出"),
    title="网络连接测试"
)

if __name__ == "__main__":
    print("🧪 启动最小测试...")
    try:
        demo.launch(
            server_name="127.0.0.1",
            server_port=7862,
            share=False,
            show_error=True,
            quiet=False,
            inbrowser=False
        )
        print("✅ 最小测试成功！")
    except Exception as e:
        print(f"❌ 最小测试失败: {e}")
