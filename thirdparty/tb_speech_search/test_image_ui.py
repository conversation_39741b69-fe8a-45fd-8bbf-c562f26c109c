#!/usr/bin/env python3
"""
测试图片展示功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_parsing():
    """测试图片解析功能"""
    print("🧪 测试图片解析功能...")
    
    # 模拟agent响应
    sample_response = """为您推荐两款价格接近2000元的IQQO手机：

1. iQOO Z10 Turbo
价格：¥1999.00
特点：12GB+512GB, 144Hz屏幕刷新率, 5000万像素相机, 天玑8400 CPU, 7620mAh大电池
购买链接：[iQOO Z10 Turbo](http://a.m.taobao.com/i924724901284.htm)
店铺：优力国货商贸
商品图片：![图片](http://g.search.alicdn.com/img/bao/uploaded/i4/i3/2218639436989/O1CN01oBt6yQ21V19zYgrxE_!!2218639436989.jpg)

2. iQOO Z10 Turbo 16+256G
价格：¥2199.00
特点：16GB+256GB, 144Hz屏幕刷新率, 5000万像素相机, 天玑8400 CPU, 7620mAh大电池
购买链接：[iQOO Z10 Turbo 16+256G](http://a.m.taobao.com/i924184974412.htm)
店铺：优力国货商贸
商品图片：![图片](http://g.search2.alicdn.com/img/bao/uploaded/i4/i4/2218639436989/O1CN01rDYLd521V19yODvyv_!!2218639436989.jpg)

这两款手机享有免费邮递服务，拥有出色的继电器性能和摄像能力，非常适合对性能有较高要求的用户。"""
    
    try:
        from app_with_images import parse_and_display_products
        
        # 解析响应
        html_result = parse_and_display_products(sample_response)
        
        print("✅ 图片解析功能测试成功")
        print(f"📄 生成的HTML长度: {len(html_result)} 字符")
        
        # 检查是否包含图片
        if "img src=" in html_result:
            print("✅ HTML中包含图片标签")
        else:
            print("⚠️ HTML中未找到图片标签")
        
        # 检查是否包含价格
        if "¥" in html_result:
            print("✅ HTML中包含价格信息")
        else:
            print("⚠️ HTML中未找到价格信息")
        
        # 检查是否包含购买链接
        if "立即购买" in html_result:
            print("✅ HTML中包含购买链接")
        else:
            print("⚠️ HTML中未找到购买链接")
        
        # 保存HTML到文件用于查看
        with open("test_output.html", "w", encoding="utf-8") as f:
            f.write(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>商品展示测试</title>
            </head>
            <body>
                {html_result}
            </body>
            </html>
            """)
        print("📁 HTML输出已保存到 test_output.html")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_ui_launch():
    """测试UI启动"""
    print("\n🧪 测试UI启动...")
    
    try:
        # 导入UI模块
        import app_with_images
        print("✅ UI模块导入成功")
        
        # 检查必要的函数
        if hasattr(app_with_images, 'search_products'):
            print("✅ search_products函数存在")
        else:
            print("❌ search_products函数不存在")
        
        if hasattr(app_with_images, 'parse_and_display_products'):
            print("✅ parse_and_display_products函数存在")
        else:
            print("❌ parse_and_display_products函数不存在")
        
        print("✅ UI启动测试通过")
        
    except Exception as e:
        print(f"❌ UI启动测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试图片展示功能...")
    
    # 测试图片解析
    test_image_parsing()
    
    # 测试UI启动
    test_ui_launch()
    
    print("\n🎉 测试完成！")
    print("\n💡 使用说明:")
    print("1. 运行 'python app_with_images.py' 启动带图片的Web UI")
    print("2. 在浏览器中访问 http://localhost:7860")
    print("3. 输入搜索关键词，查看带图片的商品展示")
