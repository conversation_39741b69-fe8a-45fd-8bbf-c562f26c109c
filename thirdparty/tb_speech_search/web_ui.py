#!/usr/bin/env python3
"""
交互式商品搜索Web UI
提供用户与Agent交互的界面
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import uvicorn

from interactive_agent import InteractiveSearchAgent, UserInteraction, InteractionType
from run import create_intelligent_agent


class WebUIManager:
    """Web UI管理器"""
    
    def __init__(self):
        self.app = FastAPI(title="交互式商品搜索")
        self.templates = Jinja2Templates(directory="templates")
        
        # WebSocket连接管理
        self.active_connections: Dict[str, WebSocket] = {}
        self.agent_sessions: Dict[str, InteractiveSearchAgent] = {}
        
        # 设置路由
        self._setup_routes()
        
        # 创建静态文件目录（如果不存在）
        self._setup_static_files()

    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def home(request: Request):
            return self.templates.TemplateResponse("index.html", {"request": request})
        
        @self.app.websocket("/ws/{session_id}")
        async def websocket_endpoint(websocket: WebSocket, session_id: str):
            await self._handle_websocket(websocket, session_id)

    def _setup_static_files(self):
        """设置静态文件"""
        try:
            self.app.mount("/static", StaticFiles(directory="static"), name="static")
        except:
            # 如果static目录不存在，创建一个空的处理
            pass

    async def _handle_websocket(self, websocket: WebSocket, session_id: str):
        """处理WebSocket连接"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        
        try:
            # 创建Agent实例
            agent = self._create_agent_for_session(session_id)
            self.agent_sessions[session_id] = agent
            
            await websocket.send_json({
                "type": "connection_established",
                "session_id": session_id,
                "message": "连接已建立，请输入您的商品搜索需求"
            })
            
            while True:
                # 接收用户消息
                data = await websocket.receive_json()
                await self._handle_user_message(session_id, data)
                
        except WebSocketDisconnect:
            self._cleanup_session(session_id)
        except Exception as e:
            await websocket.send_json({
                "type": "error",
                "message": f"发生错误: {str(e)}"
            })
            self._cleanup_session(session_id)

    def _create_agent_for_session(self, session_id: str) -> InteractiveSearchAgent:
        """为会话创建Agent实例"""
        # 创建基础agent
        base_agent = create_intelligent_agent()

        # 将工具字典转换为工具列表
        tools_list = list(base_agent.tools.values())

        # 转换为交互式agent
        agent = InteractiveSearchAgent(
            model=base_agent.model,
            tools=tools_list,
            max_steps=base_agent.max_steps,
            verbosity_level=1,  # 降低日志级别
            name=f"interactive_agent_{session_id}",
            description="交互式商品搜索助手"
        )
        
        # 设置回调函数
        agent.set_interaction_callbacks(
            on_interaction_needed=lambda interaction: self._handle_agent_interaction(session_id, interaction),
            on_status_update=lambda status: self._send_status_update(session_id, status)
        )
        
        return agent

    async def _handle_user_message(self, session_id: str, data: Dict):
        """处理用户消息"""
        message_type = data.get("type")
        
        if message_type == "start_search":
            # 开始搜索
            query = data.get("query", "")
            await self._start_search(session_id, query)
            
        elif message_type == "user_response":
            # 用户响应Agent的问题
            interaction_id = data.get("interaction_id")
            response_text = data.get("response_text", "")
            selected_option = data.get("selected_option")
            
            await self._handle_user_response(session_id, interaction_id, response_text, selected_option)
            
        elif message_type == "cancel_search":
            # 取消搜索
            await self._cancel_search(session_id)

    async def _start_search(self, session_id: str, query: str):
        """开始搜索"""
        agent = self.agent_sessions.get(session_id)
        if not agent:
            return
        
        websocket = self.active_connections.get(session_id)
        if not websocket:
            return
        
        await websocket.send_json({
            "type": "search_started",
            "query": query,
            "message": f"开始搜索「{query}」..."
        })
        
        try:
            # 在后台运行搜索
            asyncio.create_task(self._run_search_task(session_id, query))
            
        except Exception as e:
            await websocket.send_json({
                "type": "error",
                "message": f"搜索启动失败: {str(e)}"
            })

    async def _run_search_task(self, session_id: str, query: str):
        """运行搜索任务"""
        agent = self.agent_sessions.get(session_id)
        websocket = self.active_connections.get(session_id)

        if not agent or not websocket:
            return

        try:
            # 运行交互式搜索（异步版本）
            result = await agent.run_interactive_async(query)

            await websocket.send_json({
                "type": "search_completed",
                "result": result,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            await websocket.send_json({
                "type": "search_error",
                "message": f"搜索过程中出现错误: {str(e)}"
            })

    async def _handle_agent_interaction(self, session_id: str, interaction: UserInteraction):
        """处理Agent的交互请求"""
        websocket = self.active_connections.get(session_id)
        if not websocket:
            return
        
        await websocket.send_json({
            "type": "agent_interaction",
            "interaction_id": interaction.interaction_id,
            "interaction_type": interaction.interaction_type.value,
            "message": interaction.message,
            "options": interaction.options,
            "timestamp": datetime.fromtimestamp(interaction.timestamp).isoformat()
        })

    async def _handle_user_response(self, session_id: str, interaction_id: str, response_text: str, selected_option: str = None):
        """处理用户对Agent问题的响应"""
        agent = self.agent_sessions.get(session_id)
        if not agent:
            return
        
        # 将响应传递给Agent
        agent.handle_user_response(interaction_id, response_text, selected_option)
        
        websocket = self.active_connections.get(session_id)
        if websocket:
            await websocket.send_json({
                "type": "response_received",
                "interaction_id": interaction_id,
                "message": "已收到您的回复，继续搜索..."
            })

    async def _send_status_update(self, session_id: str, status: Dict):
        """发送状态更新"""
        websocket = self.active_connections.get(session_id)
        if not websocket:
            return
        
        await websocket.send_json({
            "type": "status_update",
            "status": status,
            "timestamp": datetime.now().isoformat()
        })

    async def _cancel_search(self, session_id: str):
        """取消搜索"""
        websocket = self.active_connections.get(session_id)
        if websocket:
            await websocket.send_json({
                "type": "search_cancelled",
                "message": "搜索已取消"
            })

    def _cleanup_session(self, session_id: str):
        """清理会话"""
        self.active_connections.pop(session_id, None)
        self.agent_sessions.pop(session_id, None)

    def run(self, host: str = "127.0.0.1", port: int = 8000, ssl_certfile: str = None, ssl_keyfile: str = None):
        """运行Web UI"""

        # 根据SSL配置确定协议和显示信息
        if ssl_certfile and ssl_keyfile:
            protocol = "https"
            print(f"启动交互式商品搜索Web UI...")
            print(f"🔒 启用SSL加密")
            print(f"📍 访问地址: {protocol}://{host}:{port}")

            uvicorn.run(
                self.app,
                host=host,
                port=port,
                log_level="info",
                ssl_certfile=ssl_certfile,
                ssl_keyfile=ssl_keyfile
            )
        else:
            protocol = "http"
            print(f"启动交互式商品搜索Web UI...")
            print(f"⚠️  使用HTTP（未加密）")
            print(f"📍 访问地址: {protocol}://{host}:{port}")

            uvicorn.run(
                self.app,
                host=host,
                port=port,
                log_level="info"
            )


def main():
    """主函数"""
    ui_manager = WebUIManager()
    ui_manager.run()


if __name__ == "__main__":
    main()
