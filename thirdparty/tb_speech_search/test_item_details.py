#!/usr/bin/env python3
"""
测试新的TaobaoItemDetailsTool工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from custom_tools import TaobaoItemDetailsTool
import json

def test_item_details_tool():
    """测试商品详情工具"""
    print("🧪 测试TaobaoItemDetailsTool...")
    
    # 创建工具实例
    tool = TaobaoItemDetailsTool()
    
    # 测试用例1：使用JSON格式的商品ID列表
    print("\n📋 测试用例1：JSON格式商品ID列表")
    test_ids_json = '["811748752470", "932739107635"]'
    result1 = tool.forward(test_ids_json)
    print(f"输入: {test_ids_json}")
    print(f"输出: {result1[:200]}..." if len(result1) > 200 else f"输出: {result1}")
    
    # 测试用例2：使用逗号分隔的商品ID列表
    print("\n📋 测试用例2：逗号分隔商品ID列表")
    test_ids_csv = "811748752470, 932739107635"
    result2 = tool.forward(test_ids_csv)
    print(f"输入: {test_ids_csv}")
    print(f"输出: {result2[:200]}..." if len(result2) > 200 else f"输出: {result2}")
    
    # 测试用例3：单个商品ID
    print("\n📋 测试用例3：单个商品ID")
    test_single_id = '["811748752470"]'
    result3 = tool.forward(test_single_id)
    print(f"输入: {test_single_id}")
    print(f"输出: {result3[:200]}..." if len(result3) > 200 else f"输出: {result3}")
    
    # 尝试解析结果
    try:
        parsed_result = json.loads(result1)
        if isinstance(parsed_result, list) and len(parsed_result) > 0:
            print(f"\n✅ 成功获取到 {len(parsed_result)} 个商品的详细信息")
            
            # 显示第一个商品的关键信息
            first_item = parsed_result[0]
            print(f"📦 第一个商品信息预览:")
            for key, value in first_item.items():
                if isinstance(value, str) and len(value) > 50:
                    print(f"  {key}: {value[:50]}...")
                else:
                    print(f"  {key}: {value}")
        else:
            print("⚠️ 返回结果为空或格式不正确")
    except json.JSONDecodeError:
        print("⚠️ 无法解析返回的JSON结果")
    except Exception as e:
        print(f"⚠️ 处理结果时出错: {e}")

if __name__ == "__main__":
    test_item_details_tool()
