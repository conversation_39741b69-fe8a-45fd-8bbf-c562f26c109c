#!/usr/bin/env python3
"""
调试运行脚本
"""

import sys
import traceback

def debug_run():
    """调试运行"""
    try:
        print("开始调试运行...")
        
        # 模拟命令行参数
        sys.argv = ['run.py', '--question=春游带什么东西', '--model-id=gpt-4o-0806', '--agent-type=intelligent']
        
        # 导入并运行main函数
        from run import main
        print("✓ 成功导入main函数")
        
        main()
        print("✓ main函数执行完成")
        
    except Exception as e:
        print(f"✗ 执行失败: {e}")
        print("\n完整错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    debug_run()
