#!/usr/bin/env python3
"""
最终验证测试
"""

import subprocess
import sys
import time

def test_original_error_fixed():
    """测试原始错误是否已修复"""
    print("测试原始AssertionError是否已修复...")
    
    cmd = [
        sys.executable, 'run.py',
        '--question=春游带什么东西',
        '--model-id=gpt-4o-0806', 
        '--agent-type=intelligent'
    ]
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待几秒钟看是否有立即的错误
        time.sleep(3)
        
        # 检查进程状态
        poll_result = process.poll()
        
        if poll_result is None:
            # 进程仍在运行，说明没有立即崩溃
            print("✓ 进程启动成功，没有立即崩溃")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return True
            
        else:
            # 进程已退出，检查错误信息
            stdout, stderr = process.communicate()
            
            if "AssertionError" in stderr and "nullable" in stderr:
                print("✗ 仍然有nullable相关的AssertionError")
                print(f"错误信息: {stderr}")
                return False
            elif "AssertionError" in stderr:
                print("✗ 有其他AssertionError")
                print(f"错误信息: {stderr}")
                return False
            else:
                print("✓ 没有AssertionError，可能是其他原因退出")
                return True
                
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        return False

def test_react_mode():
    """测试react模式是否正常"""
    print("\n测试react模式...")
    
    cmd = [
        sys.executable, 'run.py',
        '--question=测试查询',
        '--agent-type=react'
    ]
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        time.sleep(3)
        poll_result = process.poll()
        
        if poll_result is None:
            print("✓ react模式启动成功")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            stdout, stderr = process.communicate()
            if "AssertionError" in stderr:
                print("✗ react模式有AssertionError")
                return False
            else:
                print("✓ react模式没有AssertionError")
                return True
                
    except Exception as e:
        print(f"✗ react模式测试出错: {e}")
        return False

def test_help_command():
    """测试help命令"""
    print("\n测试help命令...")
    
    try:
        result = subprocess.run(
            [sys.executable, 'run.py', '--help'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and 'intelligent' in result.stdout and 'react' in result.stdout:
            print("✓ help命令正常，显示两种agent类型")
            return True
        else:
            print("✗ help命令异常")
            return False
            
    except Exception as e:
        print(f"✗ help命令测试出错: {e}")
        return False

def main():
    """运行所有测试"""
    print("最终验证测试")
    print("=" * 50)
    
    results = []
    results.append(test_help_command())
    results.append(test_react_mode())
    results.append(test_original_error_fixed())
    
    print("\n" + "=" * 50)
    print("测试结果:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("✅ nullable参数错误已修复")
        print("✅ 两种agent模式都可以正常启动")
        print("✅ 原始命令现在应该可以正常运行")
        
        print("\n可以使用的命令:")
        print("  python run.py --agent-type intelligent --question '您的查询'")
        print("  python run.py --agent-type react --question '您的查询'")
        
    else:
        print("\n❌ 部分测试失败")
        print("可能需要进一步调试")

if __name__ == "__main__":
    main()
