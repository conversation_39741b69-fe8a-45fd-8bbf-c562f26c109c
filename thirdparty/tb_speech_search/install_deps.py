#!/usr/bin/env python3
"""
安装交互式商品搜索系统的依赖
"""

import subprocess
import sys
import importlib

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("🔧 安装交互式商品搜索系统依赖")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        ("fastapi", "fastapi"),
        ("uvicorn[standard]", "uvicorn"),
        ("jinja2", "jinja2"),
        ("python-multipart", "multipart"),
        ("websockets", "websockets")
    ]
    
    installed_count = 0
    failed_packages = []
    
    for package_name, import_name in packages:
        print(f"检查 {package_name}...", end=" ")
        
        if check_package(import_name):
            print("✅ 已安装")
            installed_count += 1
        else:
            print("❌ 未安装，正在安装...")
            if install_package(package_name):
                print(f"✅ {package_name} 安装成功")
                installed_count += 1
            else:
                print(f"❌ {package_name} 安装失败")
                failed_packages.append(package_name)
    
    print("\n" + "=" * 50)
    print(f"安装结果: {installed_count}/{len(packages)} 个包已就绪")
    
    if failed_packages:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("\n请手动安装:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    else:
        print("✅ 所有依赖已安装完成")
        print("\n🚀 现在可以运行交互式系统:")
        print("  python run_interactive.py")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
