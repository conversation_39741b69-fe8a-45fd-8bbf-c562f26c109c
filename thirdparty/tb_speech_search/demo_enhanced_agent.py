#!/usr/bin/env python3
"""
演示增强型agent的使用，确保获取商品ID后自动调用详情工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_enhanced_agent():
    """演示增强型agent"""
    print("🚀 增强型电商搜索Agent演示")
    print("=" * 50)
    
    try:
        from run import create_enhanced_agent
        
        # 创建增强型agent
        print("📋 正在创建增强型agent...")
        agent = create_enhanced_agent(model_id="gpt-4-0409")
        print("✅ Agent创建成功")
        
        # 测试用例
        test_cases = [
            "运动鞋",
            "适合夏天穿的跑鞋", 
            "无线蓝牙耳机"
        ]
        
        for i, query in enumerate(test_cases, 1):
            print(f"\n🔍 测试用例 {i}: {query}")
            print("-" * 30)
            
            try:
                # 运行agent
                result = agent.run(query)
                
                print(f"✅ 搜索完成")
                print(f"📄 结果预览: {result[:200]}..." if len(result) > 200 else f"📄 完整结果: {result}")
                
                # 分析结果质量
                analyze_result_quality(result)
                
            except Exception as e:
                print(f"❌ 查询 '{query}' 失败: {e}")
            
            print()
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

def analyze_result_quality(result):
    """分析结果质量"""
    if not isinstance(result, str):
        print("⚠️ 结果不是字符串格式")
        return
    
    result_lower = result.lower()
    
    # 检查是否包含详细信息
    detail_indicators = {
        '价格信息': ['¥', '元', '价格', 'price'],
        '图片信息': ['图片', 'image', 'jpg', 'png'],
        '店铺信息': ['店铺', 'shop', '旗舰店'],
        '销量信息': ['销量', '付款', '人付款', '销售'],
        '商品详情': ['详情', '描述', '规格', '参数']
    }
    
    found_details = []
    for category, keywords in detail_indicators.items():
        if any(keyword in result_lower for keyword in keywords):
            found_details.append(category)
    
    if found_details:
        print(f"✅ 包含详细信息: {', '.join(found_details)}")
    else:
        print("⚠️ 可能缺少详细信息")
    
    # 检查结果长度
    if len(result) > 100:
        print(f"✅ 结果详细 (长度: {len(result)} 字符)")
    else:
        print(f"⚠️ 结果较简单 (长度: {len(result)} 字符)")

def compare_agents():
    """比较不同类型的agent"""
    print("\n🔄 Agent类型比较")
    print("=" * 50)
    
    try:
        from run import create_agent, create_intelligent_agent, create_enhanced_agent
        
        query = "运动鞋"
        agents = [
            ("传统ReAct Agent", create_agent),
            ("智能搜索Agent", create_intelligent_agent), 
            ("增强型Agent", create_enhanced_agent)
        ]
        
        for name, create_func in agents:
            print(f"\n🧪 测试 {name}...")
            try:
                agent = create_func(model_id="gpt-4-0409")
                result = agent.run(query)
                
                print(f"✅ {name} 完成")
                print(f"📊 结果长度: {len(result)} 字符")
                analyze_result_quality(result)
                
            except Exception as e:
                print(f"❌ {name} 失败: {e}")
        
    except Exception as e:
        print(f"❌ 比较过程中出错: {e}")

if __name__ == "__main__":
    # 演示增强型agent
    demo_enhanced_agent()
    
    # 比较不同agent
    # compare_agents()  # 取消注释以进行比较测试
