#!/usr/bin/env python3
"""
带图片展示的淘宝商品搜索Web UI
"""

import gradio as gr
import re
import json
from run import create_enhanced_agent

def parse_and_display_products(agent_response: str):
    """解析agent响应并生成带图片的HTML展示"""
    
    # 提取图片URL
    image_pattern = r'!\[图片\]\((http[^)]+)\)'
    images = re.findall(image_pattern, agent_response)
    
    # 提取购买链接
    link_pattern = r'\[([^\]]+)\]\((http[^)]+)\)'
    links = re.findall(link_pattern, agent_response)
    
    # 提取价格
    price_pattern = r'价格：¥([0-9,]+\.?[0-9]*)'
    prices = re.findall(price_pattern, agent_response)
    
    # 提取店铺
    shop_pattern = r'店铺：([^\n]+)'
    shops = re.findall(shop_pattern, agent_response)
    
    # 提取商品标题和特点
    lines = agent_response.split('\n')
    products_info = []
    
    current_product = {}
    for line in lines:
        line = line.strip()
        if re.match(r'^\d+\.\s+', line):
            # 保存上一个商品
            if current_product:
                products_info.append(current_product)
            # 开始新商品
            title = re.sub(r'^\d+\.\s+', '', line)
            current_product = {'title': title}
        elif line.startswith('价格：'):
            current_product['price_line'] = line
        elif line.startswith('特点：'):
            current_product['features'] = line
        elif line.startswith('店铺：'):
            current_product['shop_line'] = line
        elif line.startswith('购买链接：'):
            current_product['link_line'] = line
    
    # 添加最后一个商品
    if current_product:
        products_info.append(current_product)
    
    # 生成HTML
    if not products_info and not images:
        # 如果没有解析到商品信息，显示原始响应
        return f"""
        <div style="padding: 20px; background: #f8f9fa; border-radius: 10px; border: 1px solid #dee2e6;">
            <h3 style="color: #495057; margin-bottom: 15px;">🤖 AI助手回复：</h3>
            <div style="background: white; padding: 15px; border-radius: 8px; white-space: pre-wrap; line-height: 1.6;">
                {agent_response}
            </div>
        </div>
        """
    
    html = """
    <div style="padding: 20px;">
        <h2 style="color: #1890ff; margin-bottom: 25px; text-align: center;">🛍️ 商品推荐结果</h2>
    """
    
    # 为每个商品生成卡片
    for i, product in enumerate(products_info):
        image_url = images[i] if i < len(images) else ""
        price = prices[i] if i < len(prices) else "价格未知"
        shop = shops[i] if i < len(shops) else "店铺未知"
        
        # 查找对应的购买链接
        buy_link = ""
        for link_text, link_url in links:
            if product['title'] in link_text or link_text in product['title']:
                buy_link = link_url
                break
        
        html += f"""
        <div style="
            border: 1px solid #e1e5e9; 
            border-radius: 12px; 
            padding: 20px; 
            margin-bottom: 25px; 
            background: white; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <div style="flex-shrink: 0;">
                    {f'''
                    <img src="{image_url}" 
                         style="
                             width: 220px; 
                             height: 220px; 
                             object-fit: cover; 
                             border-radius: 10px; 
                             border: 2px solid #f0f0f0;
                             box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                         " 
                         alt="商品图片"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                    <div style="
                        width: 220px; 
                        height: 220px; 
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); 
                        border-radius: 10px; 
                        display: none; 
                        align-items: center; 
                        justify-content: center; 
                        color: #999;
                        font-size: 14px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 24px; margin-bottom: 8px;">📷</div>
                            暂无图片
                        </div>
                    </div>
                    ''' if image_url else '''
                    <div style="
                        width: 220px; 
                        height: 220px; 
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); 
                        border-radius: 10px; 
                        display: flex; 
                        align-items: center; 
                        justify-content: center; 
                        color: #999;
                        font-size: 14px;
                        text-align: center;
                    ">
                        <div>
                            <div style="font-size: 24px; margin-bottom: 8px;">📷</div>
                            暂无图片
                        </div>
                    </div>
                    '''}
                </div>
                <div style="flex: 1; min-width: 0;">
                    <h3 style="
                        color: #2c3e50; 
                        margin: 0 0 15px 0; 
                        font-size: 20px; 
                        font-weight: 600;
                        line-height: 1.4;
                        word-wrap: break-word;
                    ">{product['title']}</h3>
                    
                    <div style="margin-bottom: 15px;">
                        <span style="
                            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); 
                            color: white; 
                            padding: 8px 16px; 
                            border-radius: 20px; 
                            font-weight: bold; 
                            font-size: 18px;
                            box-shadow: 0 2px 4px rgba(238, 90, 36, 0.3);
                        ">💰 ¥{price}</span>
                    </div>
                    
                    {f'<div style="color: #555; margin: 12px 0; line-height: 1.5;"><strong>🔧 {product.get("features", "").replace("特点：", "")}</strong></div>' if product.get("features") else ''}
                    
                    <div style="color: #666; margin: 10px 0;">
                        <span style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                            🏪 {shop}
                        </span>
                    </div>
                    
                    {f'''
                    <a href="{buy_link}" 
                       target="_blank" 
                       style="
                           display: inline-block; 
                           background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); 
                           color: white; 
                           padding: 12px 24px; 
                           text-decoration: none; 
                           border-radius: 25px; 
                           margin-top: 15px;
                           font-weight: 500;
                           box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
                           transition: all 0.3s ease;
                       "
                       onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 6px 16px rgba(24, 144, 255, 0.4)'"
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(24, 144, 255, 0.3)'">
                        🛒 立即购买
                    </a>
                    ''' if buy_link else ''}
                </div>
            </div>
        </div>
        """
    
    html += """
    </div>
    <style>
        @media (max-width: 768px) {
            div[style*="display: flex"] {
                flex-direction: column !important;
            }
            img, div[style*="width: 220px"] {
                width: 100% !important;
                max-width: 300px !important;
                margin: 0 auto !important;
            }
        }
    </style>
    """
    
    return html

def search_products(query: str):
    """搜索商品"""
    if not query.strip():
        return "请输入搜索关键词", ""
    
    try:
        # 创建agent并搜索
        agent = create_enhanced_agent()
        response = agent.run(query)
        
        # 生成带图片的HTML展示
        html_display = parse_and_display_products(response)
        
        return response, html_display
        
    except Exception as e:
        error_msg = f"搜索过程中出现错误: {str(e)}"
        error_html = f"""
        <div style="padding: 20px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 8px; color: #a8071a;">
            <h3 style="color: #cf1322; margin-bottom: 10px;">❌ 搜索失败</h3>
            <p>{error_msg}</p>
        </div>
        """
        return error_msg, error_html

# 创建Gradio界面
with gr.Blocks(title="淘宝商品搜索助手", theme=gr.themes.Soft()) as demo:
    gr.Markdown("""
    # 🛍️ 淘宝商品搜索助手 (图片版)
    
    输入您想要搜索的商品，AI助手将为您推荐相关商品，**包括商品图片展示**！
    
    **✨ 特色功能：**
    - 🖼️ **商品图片展示** - 直观查看商品外观
    - 💰 **价格信息** - 清晰显示商品价格
    - 🏪 **店铺信息** - 了解商品来源
    - 🛒 **一键购买** - 直达商品页面
    """)
    
    with gr.Row():
        with gr.Column(scale=4):
            query_input = gr.Textbox(
                label="🔍 搜索关键词",
                placeholder="请输入您想要搜索的商品，如：2000左右的手机、运动鞋推荐等...",
                lines=2
            )
        with gr.Column(scale=1):
            search_btn = gr.Button("🚀 开始搜索", variant="primary", size="lg")
    
    with gr.Tabs():
        with gr.TabItem("🎨 商品展示 (带图片)"):
            product_display = gr.HTML(label="商品信息展示")
        
        with gr.TabItem("📝 原始回复"):
            raw_output = gr.Textbox(
                label="AI助手原始回复",
                lines=15,
                max_lines=25
            )
    
    # 绑定搜索事件
    search_btn.click(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    query_input.submit(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    # 示例查询
    gr.Examples(
        examples=[
            ["2000左右的手机"],
            ["运动鞋推荐"],
            ["秋天水果推荐"],
            ["无线蓝牙耳机"],
            ["学生用品推荐"],
            ["适合冬天的保暖用品"]
        ],
        inputs=[query_input],
        label="💡 试试这些搜索示例"
    )

if __name__ == "__main__":
    print("🚀 正在启动带图片展示的淘宝商品搜索助手...")

    import os

    # SSL证书路径
    cert_file = "/Users/<USER>/Documents/data/cert.pem"
    key_file = "/Users/<USER>/Documents/data/key.pem"

    # 检查证书文件是否存在
    if os.path.exists(cert_file) and os.path.exists(key_file):
        print("🔒 使用SSL证书启动...")
        print(f"📜 证书文件: {cert_file}")
        print(f"🔑 密钥文件: {key_file}")

        try:
            demo.launch(
                server_name="0.0.0.0",
                server_port=7860,
                ssl_keyfile=key_file,
                ssl_certfile=cert_file,
                ssl_verify=False,
                share=False,
                show_error=True,
                quiet=False,
                inbrowser=True
            )
            print("✅ HTTPS服务启动成功！")
            print("🌐 访问地址: https://localhost:7860")

        except Exception as e:
            print(f"❌ SSL启动失败: {e}")
            print("🔄 尝试HTTPS标准端口443...")

            try:
                demo.launch(
                    server_name="0.0.0.0",
                    server_port=443,
                    ssl_keyfile=key_file,
                    ssl_certfile=cert_file,
                    ssl_verify=False,
                    share=False,
                    show_error=True,
                    quiet=True,
                    inbrowser=True
                )
                print("✅ HTTPS服务在端口443启动成功！")
                print("🌐 访问地址: https://localhost")

            except Exception as e2:
                print(f"❌ 端口443也失败: {e2}")
                print("🔄 尝试高端口8443...")

                try:
                    demo.launch(
                        server_name="0.0.0.0",
                        server_port=8443,
                        ssl_keyfile=key_file,
                        ssl_certfile=cert_file,
                        ssl_verify=False,
                        share=False,
                        show_error=True,
                        quiet=True,
                        inbrowser=True
                    )
                    print("✅ HTTPS服务在端口8443启动成功！")
                    print("🌐 访问地址: https://localhost:8443")

                except Exception as e3:
                    print(f"❌ 所有SSL端口都失败: {e3}")
                    print("🔄 回退到HTTP模式...")
                    demo.launch(
                        server_name="127.0.0.1",
                        server_port=7860,
                        share=False,
                        show_error=True
                    )
    else:
        print("⚠️ SSL证书文件未找到，使用HTTP启动...")
        print(f"❌ 证书文件不存在: {cert_file}")
        print(f"❌ 密钥文件不存在: {key_file}")

        demo.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            show_error=True
        )
