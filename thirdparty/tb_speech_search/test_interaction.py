#!/usr/bin/env python3
"""
测试交互式代理的交互逻辑
"""

from interactive_agent import InteractionType


def test_clarification_generation():
    """测试澄清问题生成逻辑"""

    # 模拟一个简化的交互生成器
    class MockInteractionGenerator:
        def _generate_clarification_question(self, query: str, analysis: dict):
            """生成澄清问题"""
            missing_info = analysis.get('missing_info', [])

            if '商品类型' in missing_info:
                message = f"您想要搜索什么类型的商品呢？我可以帮您搜索："
                options = ["电子产品", "服装鞋包", "家居用品", "食品饮料", "运动户外", "美妆个护"]
            elif '价格范围' in missing_info:
                message = "您希望的价格范围是多少？"
                options = ["50元以下", "50-200元", "200-500元", "500-1000元", "1000元以上", "没有特别要求"]
            elif '品牌偏好' in missing_info:
                message = "您有特别偏好的品牌吗？"
                options = ["知名品牌", "性价比品牌", "没有特别要求"]
            else:
                # 根据查询内容智能生成选项
                if any(keyword in query for keyword in ['春游', '旅游', '出游', '旅行']):
                    message = f"关于「{query}」，您想要搜索哪类商品呢？"
                    options = ["户外装备", "旅行用品", "服装鞋帽", "食品零食", "电子设备", "其他用品"]
                elif any(keyword in query for keyword in ['夏天', '夏季', '热天']):
                    message = f"关于「{query}」，您想要搜索哪类商品呢？"
                    options = ["服装鞋帽", "防晒用品", "清凉用品", "运动装备", "家居用品", "其他商品"]
                elif any(keyword in query for keyword in ['冬天', '冬季', '寒冷']):
                    message = f"关于「{query}」，您想要搜索哪类商品呢？"
                    options = ["保暖服装", "取暖用品", "冬季装备", "护肤用品", "家居用品", "其他商品"]
                elif any(keyword in query for keyword in ['运动', '健身', '锻炼']):
                    message = f"关于「{query}」，您想要搜索哪类商品呢？"
                    options = ["运动服装", "运动鞋", "健身器材", "运动配件", "营养补剂", "其他用品"]
                elif any(keyword in query for keyword in ['学习', '办公', '工作']):
                    message = f"关于「{query}」，您想要搜索哪类商品呢？"
                    options = ["文具用品", "电子设备", "办公用品", "学习资料", "家具用品", "其他商品"]
                else:
                    message = f"关于「{query}」，您能提供更多具体信息吗？比如用途、场景、预算等。"
                    options = ["电子产品", "服装鞋包", "家居用品", "食品饮料", "运动户外", "美妆个护", "其他类别"]

            return {
                'message': message,
                'options': options,
                'interaction_type': InteractionType.CLARIFICATION
            }

    generator = MockInteractionGenerator()

    # 测试不同的查询
    test_cases = [
        {
            'query': '春游要带什么',
            'analysis': {'missing_info': []}
        },
        {
            'query': '夏天跑步穿什么',
            'analysis': {'missing_info': []}
        },
        {
            'query': '办公用品',
            'analysis': {'missing_info': ['商品类型']}
        }
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n=== 测试案例 {i} ===")
        print(f"查询: {test_case['query']}")
        print(f"分析: {test_case['analysis']}")

        result = generator._generate_clarification_question(test_case['query'], test_case['analysis'])

        print(f"生成的消息: {result['message']}")
        print(f"选项数量: {len(result['options']) if result['options'] else 0}")

        if result['options']:
            print("选项:")
            for j, option in enumerate(result['options'], 1):
                print(f"  {j}. {option}")
            print("✅ 成功生成选项")
        else:
            print("❌ 没有生成选项")


if __name__ == "__main__":
    test_clarification_generation()
