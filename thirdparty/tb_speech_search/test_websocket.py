#!/usr/bin/env python3
"""
测试WebSocket交互
"""

import asyncio
import json
import websockets


async def test_websocket_interaction():
    """测试WebSocket交互"""
    uri = "ws://127.0.0.1:8000/ws/test_session"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 接收连接确认消息
            response = await websocket.recv()
            data = json.loads(response)
            print(f"收到连接消息: {data}")
            
            # 发送搜索请求
            search_request = {
                "type": "start_search",
                "query": "春游要带什么"
            }
            
            await websocket.send(json.dumps(search_request))
            print(f"发送搜索请求: {search_request}")
            
            # 接收响应
            timeout_count = 0
            max_timeout = 10
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(response)
                    print(f"\n收到响应: {data['type']}")
                    
                    if data['type'] == 'agent_interaction':
                        print(f"交互消息: {data['message']}")
                        if data.get('options'):
                            print(f"选项数量: {len(data['options'])}")
                            print("选项:")
                            for i, option in enumerate(data['options'], 1):
                                print(f"  {i}. {option}")
                            print("✅ 成功接收到选项！")
                            
                            # 模拟用户选择第一个选项
                            user_response = {
                                "type": "user_response",
                                "interaction_id": data['interaction_id'],
                                "response_text": data['options'][0],
                                "selected_option": data['options'][0]
                            }
                            await websocket.send(json.dumps(user_response))
                            print(f"发送用户响应: {user_response}")
                        else:
                            print("❌ 没有收到选项")
                            break
                    
                    elif data['type'] == 'search_completed':
                        print("搜索完成")
                        break
                    
                    elif data['type'] == 'search_error':
                        print(f"搜索错误: {data['message']}")
                        break
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    print(f"等待响应超时 ({timeout_count}/{max_timeout})")
                    
            if timeout_count >= max_timeout:
                print("❌ 等待响应超时")
                
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_websocket_interaction())
