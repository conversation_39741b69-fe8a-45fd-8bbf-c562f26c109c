<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能商品搜索助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 800px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 16px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            text-align: right;
        }

        .message.agent {
            text-align: left;
        }

        .message.system {
            text-align: center;
        }

        .message-content {
            display: inline-block;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
        }

        .message.agent .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
        }

        .message.system .message-content {
            background: #f8f9fa;
            color: #6c757d;
            font-style: italic;
            border: 1px solid #dee2e6;
        }

        .options {
            margin-top: 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: flex-start;
        }

        .message.user .options {
            justify-content: flex-end;
        }

        .option-btn {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 20px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .option-btn:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.2s;
            resize: none;
            min-height: 50px;
            max-height: 120px;
        }

        .input-field:focus {
            border-color: #007bff;
        }

        .send-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover:not(:disabled) {
            background: #0056b3;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .status {
            padding: 8px 16px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #856404;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ 智能商品搜索助手</h1>
            <p>告诉我您想要什么，我会智能地为您搜索并在需要时与您交互</p>
        </div>
        
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message system">
                    <div class="message-content">
                        欢迎使用智能商品搜索助手！请输入您想要搜索的商品，我会根据需要与您交互以提供最佳的搜索结果。
                    </div>
                </div>
            </div>
            
            <div class="input-area">
                <div class="input-group">
                    <textarea 
                        class="input-field" 
                        id="messageInput" 
                        placeholder="请输入您想要搜索的商品，例如：春游要带什么、适合夏天的跑鞋..."
                        rows="1"></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        <span id="sendIcon">➤</span>
                        <span id="loadingIcon" class="loading hidden"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ChatUI {
            constructor() {
                this.ws = null;
                this.sessionId = this.generateSessionId();
                this.currentInteractionId = null;
                this.isSearching = false;
                
                this.messagesContainer = document.getElementById('messages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.sendIcon = document.getElementById('sendIcon');
                this.loadingIcon = document.getElementById('loadingIcon');
                
                this.setupEventListeners();
                this.connectWebSocket();
            }
            
            generateSessionId() {
                return 'session_' + Math.random().toString(36).substr(2, 9);
            }
            
            setupEventListeners() {
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                
                this.messageInput.addEventListener('input', () => {
                    this.autoResize();
                });
            }
            
            autoResize() {
                this.messageInput.style.height = 'auto';
                this.messageInput.style.height = this.messageInput.scrollHeight + 'px';
            }
            
            connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/${this.sessionId}`;
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                };
                
                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                };
                
                this.ws.onclose = () => {
                    console.log('WebSocket连接已关闭');
                    this.addSystemMessage('连接已断开，请刷新页面重新连接');
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    this.addSystemMessage('连接出现错误，请检查网络连接');
                };
            }
            
            handleMessage(data) {
                switch (data.type) {
                    case 'connection_established':
                        this.addSystemMessage(data.message);
                        break;
                        
                    case 'search_started':
                        this.addSystemMessage(`开始搜索「${data.query}」...`);
                        this.setSearching(true);
                        break;
                        
                    case 'agent_interaction':
                        this.handleAgentInteraction(data);
                        break;
                        
                    case 'status_update':
                        this.handleStatusUpdate(data.status);
                        break;
                        
                    case 'search_completed':
                        this.handleSearchCompleted(data.result);
                        this.setSearching(false);
                        break;
                        
                    case 'search_error':
                        this.addSystemMessage(`❌ ${data.message}`);
                        this.setSearching(false);
                        break;
                        
                    case 'response_received':
                        this.addSystemMessage('✅ 已收到您的回复，继续搜索...');
                        break;
                        
                    case 'error':
                        this.addSystemMessage(`❌ ${data.message}`);
                        break;
                }
            }
            
            handleAgentInteraction(data) {
                this.currentInteractionId = data.interaction_id;
                this.addAgentMessage(data.message, data.options);
                // 根据是否有选项显示不同的提示信息
                if (data.options && data.options.length > 0) {
                    this.addSystemMessage('💡 您可以点击上方选项，或在下方输入框中输入自定义回复');
                } else {
                    this.addSystemMessage('💡 请在下方输入框中输入您的回复');
                }
            }
            
            handleStatusUpdate(status) {
                if (status.step) {
                    this.addSystemMessage(`🔄 ${status.step}: ${status.query || ''}`);
                }
            }
            
            handleSearchCompleted(result) {
                try {
                    const results = JSON.parse(result);
                    this.displaySearchResults(results);
                } catch (e) {
                    this.addAgentMessage(result);
                }
            }
            
            displaySearchResults(results) {
                if (Array.isArray(results)) {
                    results.forEach(category => {
                        if (category.items && category.items.length > 0) {
                            let resultText = `📦 ${category.category}:\n\n`;
                            category.items.slice(0, 5).forEach((item, index) => {
                                resultText += `${index + 1}. ${item.itemTitle}\n`;
                                resultText += `   💰 价格: ¥${item.itemPrice}\n`;
                                resultText += `   🆔 商品ID: ${item.itemId}\n\n`;
                            });
                            this.addAgentMessage(resultText);
                        }
                    });
                } else {
                    this.addAgentMessage('搜索完成，但结果格式异常');
                }
            }
            
            sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isSearching) return;

                this.addUserMessage(message);
                this.messageInput.value = '';
                this.autoResize();

                if (this.currentInteractionId) {
                    // 响应Agent的问题
                    this.ws.send(JSON.stringify({
                        type: 'user_response',
                        interaction_id: this.currentInteractionId,
                        response_text: message,
                        is_custom_input: true
                    }));
                    this.currentInteractionId = null;
                    this.disableOptionButtons();
                } else {
                    // 开始新的搜索
                    this.ws.send(JSON.stringify({
                        type: 'start_search',
                        query: message
                    }));
                }
            }

            disableOptionButtons() {
                // 禁用所有当前可见的选项按钮
                const optionButtons = document.querySelectorAll('.option-btn:not(.disabled)');
                optionButtons.forEach(btn => {
                    btn.disabled = true;
                    btn.classList.add('disabled');
                    btn.style.opacity = '0.5';
                    btn.style.cursor = 'not-allowed';
                });
            }
            
            selectOption(option, interactionId) {
                this.addUserMessage(option);

                this.ws.send(JSON.stringify({
                    type: 'user_response',
                    interaction_id: interactionId,
                    response_text: option,
                    selected_option: option
                }));

                // 清除 currentInteractionId，完成当前交互
                this.currentInteractionId = null;

                // 禁用已点击的选项按钮
                this.disableOptionButtons();
            }
            
            addUserMessage(text) {
                this.addMessage('user', text);
            }
            
            addAgentMessage(text, options = null) {
                this.addMessage('agent', text, options);
            }
            
            addSystemMessage(text) {
                this.addMessage('system', text);
            }
            
            addMessage(type, text, options = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = text;
                
                messageDiv.appendChild(contentDiv);
                
                if (options && options.length > 0) {
                    const optionsDiv = document.createElement('div');
                    optionsDiv.className = 'options';
                    
                    options.forEach(option => {
                        const optionBtn = document.createElement('button');
                        optionBtn.className = 'option-btn';
                        optionBtn.textContent = option;
                        optionBtn.onclick = () => this.selectOption(option, this.currentInteractionId);
                        optionsDiv.appendChild(optionBtn);
                    });
                    
                    messageDiv.appendChild(optionsDiv);
                }
                
                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }
            
            setSearching(searching) {
                this.isSearching = searching;
                this.sendBtn.disabled = searching;
                
                if (searching) {
                    this.sendIcon.classList.add('hidden');
                    this.loadingIcon.classList.remove('hidden');
                } else {
                    this.sendIcon.classList.remove('hidden');
                    this.loadingIcon.classList.add('hidden');
                }
            }
            
            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
        }
        
        // 全局函数
        function sendMessage() {
            window.chatUI.sendMessage();
        }
        
        // 初始化
        window.addEventListener('load', () => {
            window.chatUI = new ChatUI();
        });
    </script>
</body>
</html>
