# 模仿smolagents/examples/open_deep_research/app.py，实现淘宝语音搜索商品功能
"""
主搜接口（替换{query}部分）：https://tppwork.taobao.com/center/recommend?appid=36935&code=aiSrp_itemSearchTool&_input_charset=UTF-8&_output_charset=UTF-8&searchType=recall&from=voiceTest&outputType=recall&userId=&closeItemRelevanceCheck=true&gotoOld=true&q={query}
"""

from smolagents import Agent, ToolCallingAgent, InferenceClientModel, PythonInterpreterTool, GoogleSearchTool, VisitWebpageTool
from smolagents.gradio_ui import GradioUI
import os

from run import create_enhanced_agent

agent = create_enhanced_agent()

demo = GradioUI(agent)

if __name__ == "__main__":
    # SSL证书路径
    cert_file = "/Users/<USER>/Documents/data/cert.pem"
    key_file = "/Users/<USER>/Documents/data/key.pem"

    # 检查证书文件是否存在
    if os.path.exists(cert_file) and os.path.exists(key_file):
        print("🔒 使用SSL证书启动...")
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            ssl_keyfile=key_file,
            ssl_certfile=cert_file,
            ssl_verify=False,
            share=False
        )
    else:
        print("⚠️ SSL证书文件未找到，使用标准启动...")
        demo.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False
        )




