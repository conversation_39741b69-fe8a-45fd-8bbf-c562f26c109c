#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

from custom_tools import TaobaoMainSearchTool, TaobaoItemDetailsTool
import json

def extract_huawei_phone_images():
    """提取华为手机商品的主图URL"""
    print("=== 提取华为手机商品主图URL ===")
    
    query = "华为手机"
    
    # 步骤1：获取商品ID
    main_search_tool = TaobaoMainSearchTool()
    search_result = main_search_tool.forward(query)
    
    item_ids = []
    if isinstance(search_result, list):
        for item in search_result[:3]:
            if 'itemId' in item:
                item_ids.append(item['itemId'])
    
    if not item_ids:
        print("❌ 没有获取到商品ID")
        return
    
    print(f"获取到商品ID: {item_ids}")
    
    # 步骤2：获取详情并提取图片URL
    detail_tool = TaobaoItemDetailsTool()
    detail_result = detail_tool.forward(json.dumps(item_ids), query)
    
    try:
        parsed_details = json.loads(detail_result)
        
        print(f"\n=== 前3个华为手机商品的主图URL ===")
        for i, item in enumerate(parsed_details[:3]):
            print(f"\n商品{i+1}:")
            print(f"  - 标题: {item.get('title', 'N/A')}")
            print(f"  - 价格: {item.get('price', 'N/A')}")
            
            # 检查 pic_path 字段
            pic_path = item.get('pic_path')
            if pic_path:
                # 构建完整的图片URL
                if pic_path.startswith('//'):
                    pic_url = 'https:' + pic_path
                elif not pic_path.startswith('http'):
                    pic_url = 'https://' + pic_path
                else:
                    pic_url = pic_path
                print(f"  - 主图URL (pic_path): {pic_url}")
            
            # 检查 imageInfo 字段
            image_info = item.get('imageInfo')
            if image_info:
                print(f"  - imageInfo: {image_info}")
                # 如果imageInfo是字典，尝试提取URL
                if isinstance(image_info, dict):
                    for key, value in image_info.items():
                        if 'url' in key.lower() or 'pic' in key.lower():
                            print(f"    - {key}: {value}")
            
            # 检查其他可能的图片字段
            potential_image_fields = ['pic_url', 'picUrl', 'image', 'mainPic', 'picture', 'imgUrl']
            for field in potential_image_fields:
                if field in item and item[field]:
                    value = item[field]
                    if isinstance(value, str) and ('http' in value or '//' in value):
                        if value.startswith('//'):
                            value = 'https:' + value
                        print(f"  - {field}: {value}")
            
            # 如果都没找到，显示一些可能包含图片信息的字段
            if not pic_path and not image_info:
                print("  - 未找到明确的图片URL字段")
                # 查看是否有其他包含图片信息的字段
                for key, value in item.items():
                    if isinstance(value, str) and ('jpg' in value.lower() or 'png' in value.lower() or 'jpeg' in value.lower()):
                        print(f"  - 可能的图片字段 {key}: {value}")
                    elif 'pic' in key.lower() or 'img' in key.lower() or 'image' in key.lower():
                        print(f"  - 图片相关字段 {key}: {str(value)[:100]}...")
        
        # 额外：显示第一个商品的完整字段结构以便调试
        if len(parsed_details) > 0:
            print(f"\n=== 第一个商品的完整字段结构（调试用） ===")
            first_item = parsed_details[0]
            for key, value in first_item.items():
                if 'pic' in key.lower() or 'img' in key.lower() or 'image' in key.lower():
                    print(f"{key}: {value}")
                    
    except Exception as e:
        print(f"❌ 解析详情结果失败: {e}")

if __name__ == "__main__":
    extract_huawei_phone_images()
