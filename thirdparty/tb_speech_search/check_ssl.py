#!/usr/bin/env python3
"""
SSL证书检查工具
"""

import os
import subprocess
import sys
from datetime import datetime

# SSL证书路径
CERT_FILE = "/Users/<USER>/Documents/data/cert.pem"
KEY_FILE = "/Users/<USER>/Documents/data/key.pem"

def check_file_exists(filepath, file_type):
    """检查文件是否存在"""
    print(f"\n🔍 检查{file_type}文件...")
    print(f"📁 路径: {filepath}")
    
    if os.path.exists(filepath):
        print(f"✅ {file_type}文件存在")
        
        # 获取文件信息
        stat = os.stat(filepath)
        print(f"📊 文件大小: {stat.st_size} bytes")
        print(f"🔐 文件权限: {oct(stat.st_mode)[-3:]}")
        print(f"📅 修改时间: {datetime.fromtimestamp(stat.st_mtime)}")
        
        # 检查文件是否可读
        if os.access(filepath, os.R_OK):
            print(f"✅ {file_type}文件可读")
        else:
            print(f"❌ {file_type}文件不可读")
            return False
        
        return True
    else:
        print(f"❌ {file_type}文件不存在")
        return False

def check_cert_content(cert_file):
    """检查证书内容"""
    print(f"\n🔍 检查证书内容...")
    
    try:
        with open(cert_file, 'r') as f:
            content = f.read()
        
        if "-----BEGIN CERTIFICATE-----" in content:
            print("✅ 证书格式正确 (PEM格式)")
        else:
            print("❌ 证书格式可能有问题")
            return False
        
        if "-----END CERTIFICATE-----" in content:
            print("✅ 证书结束标记正确")
        else:
            print("❌ 证书结束标记缺失")
            return False
        
        # 计算证书行数
        lines = content.strip().split('\n')
        print(f"📄 证书总行数: {len(lines)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取证书文件失败: {e}")
        return False

def check_key_content(key_file):
    """检查密钥内容"""
    print(f"\n🔍 检查密钥内容...")
    
    try:
        with open(key_file, 'r') as f:
            content = f.read()
        
        # 检查不同类型的密钥格式
        key_types = [
            ("RSA PRIVATE KEY", "RSA私钥"),
            ("PRIVATE KEY", "PKCS#8私钥"),
            ("EC PRIVATE KEY", "EC私钥")
        ]
        
        found_key_type = None
        for key_type, description in key_types:
            if f"-----BEGIN {key_type}-----" in content:
                print(f"✅ 密钥格式正确 ({description})")
                found_key_type = key_type
                break
        
        if not found_key_type:
            print("❌ 密钥格式可能有问题")
            return False
        
        if f"-----END {found_key_type}-----" in content:
            print("✅ 密钥结束标记正确")
        else:
            print("❌ 密钥结束标记缺失")
            return False
        
        # 计算密钥行数
        lines = content.strip().split('\n')
        print(f"📄 密钥总行数: {len(lines)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取密钥文件失败: {e}")
        return False

def check_openssl():
    """检查OpenSSL是否可用"""
    print(f"\n🔍 检查OpenSSL...")
    
    try:
        result = subprocess.run(['openssl', 'version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ OpenSSL可用: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ OpenSSL命令失败: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ OpenSSL未安装或不在PATH中")
        return False
    except Exception as e:
        print(f"❌ OpenSSL检查失败: {e}")
        return False

def verify_cert_with_openssl(cert_file):
    """使用OpenSSL验证证书"""
    print(f"\n🔍 使用OpenSSL验证证书...")
    
    try:
        # 验证证书
        result = subprocess.run(['openssl', 'x509', '-in', cert_file, '-text', '-noout'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 证书验证成功")
            
            # 提取关键信息
            output = result.stdout
            
            # 查找主题
            for line in output.split('\n'):
                if 'Subject:' in line:
                    print(f"📋 证书主题: {line.strip()}")
                elif 'Issuer:' in line:
                    print(f"🏢 证书颁发者: {line.strip()}")
                elif 'Not Before:' in line:
                    print(f"📅 有效期开始: {line.strip()}")
                elif 'Not After:' in line:
                    print(f"📅 有效期结束: {line.strip()}")
            
            return True
        else:
            print(f"❌ 证书验证失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ OpenSSL验证失败: {e}")
        return False

def verify_key_with_openssl(key_file):
    """使用OpenSSL验证密钥"""
    print(f"\n🔍 使用OpenSSL验证密钥...")
    
    try:
        # 验证密钥
        result = subprocess.run(['openssl', 'rsa', '-in', key_file, '-check', '-noout'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ RSA密钥验证成功")
            return True
        else:
            # 尝试其他密钥类型
            result = subprocess.run(['openssl', 'pkey', '-in', key_file, '-check', '-noout'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ 密钥验证成功")
                return True
            else:
                print(f"❌ 密钥验证失败: {result.stderr}")
                return False
            
    except Exception as e:
        print(f"❌ OpenSSL密钥验证失败: {e}")
        return False

def check_cert_key_match(cert_file, key_file):
    """检查证书和密钥是否匹配"""
    print(f"\n🔍 检查证书和密钥是否匹配...")
    
    try:
        # 获取证书的公钥
        cert_result = subprocess.run(['openssl', 'x509', '-in', cert_file, '-pubkey', '-noout'], 
                                   capture_output=True, text=True, timeout=10)
        
        # 获取私钥的公钥
        key_result = subprocess.run(['openssl', 'rsa', '-in', key_file, '-pubout'], 
                                  capture_output=True, text=True, timeout=10)
        
        if cert_result.returncode == 0 and key_result.returncode == 0:
            if cert_result.stdout.strip() == key_result.stdout.strip():
                print("✅ 证书和密钥匹配")
                return True
            else:
                print("❌ 证书和密钥不匹配")
                return False
        else:
            print("❌ 无法提取公钥进行比较")
            return False
            
    except Exception as e:
        print(f"❌ 证书密钥匹配检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔒 SSL证书检查工具")
    print("=" * 60)
    
    # 检查文件存在性
    cert_exists = check_file_exists(CERT_FILE, "证书")
    key_exists = check_file_exists(KEY_FILE, "密钥")
    
    if not cert_exists or not key_exists:
        print("\n❌ SSL证书文件缺失，无法继续检查")
        print("\n💡 解决方案:")
        print("1. 确保证书文件路径正确")
        print("2. 检查文件权限")
        print("3. 重新生成或获取SSL证书")
        return False
    
    # 检查文件内容
    cert_content_ok = check_cert_content(CERT_FILE)
    key_content_ok = check_key_content(KEY_FILE)
    
    if not cert_content_ok or not key_content_ok:
        print("\n❌ SSL证书文件内容有问题")
        return False
    
    # 检查OpenSSL
    openssl_ok = check_openssl()
    
    if openssl_ok:
        # 使用OpenSSL进行详细验证
        cert_valid = verify_cert_with_openssl(CERT_FILE)
        key_valid = verify_key_with_openssl(KEY_FILE)
        
        if cert_valid and key_valid:
            # 检查证书和密钥是否匹配
            match_ok = check_cert_key_match(CERT_FILE, KEY_FILE)
            
            if match_ok:
                print("\n" + "=" * 60)
                print("🎉 SSL证书检查完全通过！")
                print("✅ 证书文件存在且格式正确")
                print("✅ 密钥文件存在且格式正确")
                print("✅ 证书和密钥匹配")
                print("\n🚀 可以使用以下命令启动SSL版本:")
                print("   python app_ssl.py")
                print("   python app_with_images.py")
                return True
    
    print("\n" + "=" * 60)
    print("⚠️ SSL证书检查完成，但可能存在问题")
    print("🔧 建议检查证书文件的完整性和有效性")
    
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
