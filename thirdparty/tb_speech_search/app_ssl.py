#!/usr/bin/env python3
"""
SSL版本的淘宝商品搜索Web UI
使用SSL证书解决网络连接问题
"""

import os
import gradio as gr

# SSL证书路径
CERT_FILE = "/Users/<USER>/Documents/data/cert.pem"
KEY_FILE = "/Users/<USER>/Documents/data/key.pem"

def search_products(query: str):
    """搜索商品"""
    if not query.strip():
        return "请输入搜索关键词", ""
    
    try:
        from run import create_enhanced_agent
        from app_with_images import parse_and_display_products
        
        # 创建agent并搜索
        agent = create_enhanced_agent()
        response = agent.run(query)
        
        # 生成带图片的HTML展示
        html_display = parse_and_display_products(response)
        
        return response, html_display
        
    except Exception as e:
        error_msg = f"搜索过程中出现错误: {str(e)}"
        error_html = f"""
        <div style="padding: 20px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 8px; color: #a8071a;">
            <h3 style="color: #cf1322; margin-bottom: 10px;">❌ 搜索失败</h3>
            <p>{error_msg}</p>
        </div>
        """
        return error_msg, error_html

# 创建界面
with gr.Blocks(title="淘宝商品搜索助手 (SSL版)", theme=gr.themes.Soft()) as demo:
    gr.Markdown("""
    # 🛍️ 淘宝商品搜索助手 (SSL版)
    
    🔒 **使用SSL证书启动，解决网络连接问题**
    
    输入您想要搜索的商品，AI助手将为您推荐相关商品，**包括商品图片展示**！
    """)
    
    with gr.Row():
        with gr.Column(scale=4):
            query_input = gr.Textbox(
                label="🔍 搜索关键词",
                placeholder="请输入您想要搜索的商品，如：2000左右的手机、运动鞋推荐等...",
                lines=2
            )
        with gr.Column(scale=1):
            search_btn = gr.Button("🚀 开始搜索", variant="primary", size="lg")
    
    with gr.Tabs():
        with gr.TabItem("🎨 商品展示 (带图片)"):
            product_display = gr.HTML(label="商品信息展示")
        
        with gr.TabItem("📝 原始回复"):
            raw_output = gr.Textbox(
                label="AI助手原始回复",
                lines=15,
                max_lines=25
            )
    
    # 绑定搜索事件
    search_btn.click(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    query_input.submit(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    # 示例查询
    gr.Examples(
        examples=[
            ["2000左右的手机"],
            ["运动鞋推荐"],
            ["秋天水果推荐"],
            ["无线蓝牙耳机"],
            ["学生用品推荐"]
        ],
        inputs=[query_input],
        label="💡 试试这些搜索示例"
    )

def check_ssl_files():
    """检查SSL证书文件"""
    print("🔍 检查SSL证书文件...")
    
    if os.path.exists(CERT_FILE):
        print(f"✅ 证书文件存在: {CERT_FILE}")
        # 检查文件权限
        stat = os.stat(CERT_FILE)
        print(f"   文件大小: {stat.st_size} bytes")
        print(f"   文件权限: {oct(stat.st_mode)[-3:]}")
    else:
        print(f"❌ 证书文件不存在: {CERT_FILE}")
        return False
    
    if os.path.exists(KEY_FILE):
        print(f"✅ 密钥文件存在: {KEY_FILE}")
        # 检查文件权限
        stat = os.stat(KEY_FILE)
        print(f"   文件大小: {stat.st_size} bytes")
        print(f"   文件权限: {oct(stat.st_mode)[-3:]}")
    else:
        print(f"❌ 密钥文件不存在: {KEY_FILE}")
        return False
    
    return True

def launch_with_ssl():
    """使用SSL启动"""
    print("🔒 尝试SSL启动...")
    
    # 尝试不同的端口配置
    ssl_configs = [
        {"port": 7860, "name": "标准端口7860"},
        {"port": 8443, "name": "HTTPS高端口8443"},
        {"port": 443, "name": "HTTPS标准端口443"},
        {"port": 9443, "name": "备用端口9443"}
    ]
    
    for config in ssl_configs:
        try:
            print(f"🔄 尝试{config['name']}...")
            
            demo.launch(
                server_name="0.0.0.0",
                server_port=config["port"],
                ssl_keyfile=KEY_FILE,
                ssl_certfile=CERT_FILE,
                ssl_verify=False,
                share=False,
                show_error=True,
                quiet=False,
                enable_queue=False,
                show_tips=False,
                inbrowser=True,
                prevent_thread_lock=False
            )
            
            print(f"✅ SSL启动成功！")
            print(f"🌐 HTTPS访问地址: https://localhost:{config['port']}")
            return True
            
        except PermissionError:
            print(f"❌ 权限错误：端口{config['port']}需要管理员权限")
            continue
        except OSError as e:
            if "Address already in use" in str(e):
                print(f"❌ 端口{config['port']}已被占用")
                continue
            elif "Permission denied" in str(e):
                print(f"❌ 端口{config['port']}权限被拒绝")
                continue
            else:
                print(f"❌ 端口{config['port']}启动失败: {e}")
                continue
        except Exception as e:
            print(f"❌ 端口{config['port']}启动失败: {e}")
            continue
    
    print("❌ 所有SSL端口都启动失败")
    return False

def launch_fallback():
    """回退到HTTP启动"""
    print("🔄 回退到HTTP模式...")
    
    try:
        demo.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            show_error=True,
            quiet=False,
            enable_queue=False,
            show_tips=False,
            inbrowser=True
        )
        print("✅ HTTP启动成功！")
        print("🌐 HTTP访问地址: http://127.0.0.1:7860")
        return True
    except Exception as e:
        print(f"❌ HTTP启动也失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动SSL版淘宝商品搜索助手...")
    print("=" * 60)
    
    # 检查SSL证书文件
    if not check_ssl_files():
        print("\n⚠️ SSL证书文件检查失败，无法使用SSL模式")
        print("💡 请确保以下文件存在:")
        print(f"   📜 {CERT_FILE}")
        print(f"   🔑 {KEY_FILE}")
        print("\n🔄 尝试HTTP模式...")
        
        if not launch_fallback():
            print("\n❌ 启动完全失败")
            print("🛠️ 请检查:")
            print("   1. Python环境和依赖包")
            print("   2. 端口占用情况")
            print("   3. 网络配置")
        return
    
    print("\n🔒 SSL证书文件检查通过")
    
    # 尝试SSL启动
    if not launch_with_ssl():
        print("\n🔄 SSL启动失败，尝试HTTP模式...")
        if not launch_fallback():
            print("\n❌ 所有启动方式都失败")
            print("🛠️ 请检查系统配置和网络设置")

if __name__ == "__main__":
    main()
