# 🛍️ 交互式商品搜索系统

一个智能的商品搜索助手，能够在搜索过程中与用户进行有意义的交互，避免不必要的打扰，提供个性化的搜索体验。

## ✨ 核心特性

### 🧠 智能交互策略
- **自主判断**: Agent自动判断何时需要与用户交互
- **避免打扰**: 只在真正需要时才询问用户
- **上下文感知**: 基于查询明确度和搜索结果质量决定交互策略

### 💬 多种交互类型
- **澄清查询**: 当用户意图不明确时，智能询问具体需求
- **确认选择**: 当有多个搜索方向时，让用户确认偏好
- **获取反馈**: 根据搜索结果质量调整策略
- **提供建议**: 主动提供有价值的搜索建议

### 🌐 现代化Web界面
- **实时通信**: 基于WebSocket的实时交互
- **响应式设计**: 适配各种设备屏幕
- **直观操作**: 简洁美观的聊天界面
- **选项按钮**: 快速选择预设选项

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │ Interactive     │    │ Search Tools    │
│   (Frontend)    │◄──►│ Agent           │◄──►│ (Backend)       │
│                 │    │ (Core Logic)    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        │                       │                       │
   WebSocket              Smart Interaction        Tool Execution
   Communication          Decision Engine          (Taobao, Web Search)
```

### 核心组件

1. **InteractiveSearchAgent**: 智能交互搜索引擎
2. **WebUIManager**: Web界面管理器
3. **交互策略引擎**: 决定何时及如何与用户交互
4. **实时通信层**: WebSocket双向通信

## 🚀 快速开始

### 1. 安装依赖

```bash
# 自动安装所有依赖
python install_deps.py

# 或手动安装
pip install fastapi uvicorn jinja2 python-multipart websockets
```

### 2. 启动系统

```bash
# 启动交互式搜索系统
python run_interactive.py

# 自定义端口和地址
python run_interactive.py --host 0.0.0.0 --port 8080

# 仅检查环境
python run_interactive.py --check-only
```

### 3. 访问界面

打开浏览器访问: `http://127.0.0.1:8000`

## 📖 使用指南

### 基本使用流程

1. **输入查询**: 在输入框中描述您想要搜索的商品
2. **智能交互**: Agent会根据需要询问澄清问题
3. **选择偏好**: 通过选项按钮或文本回复您的偏好
4. **获得结果**: 查看个性化的搜索结果

### 查询示例

#### 明确查询（直接搜索）
```
苹果手机
适合夏天的跑鞋
无线蓝牙耳机
```

#### 模糊查询（会触发交互）
```
春游要带什么？
买个礼物
推荐一些好用的东西
```

#### 复杂查询（智能引导）
```
想要一个性价比高的笔记本电脑
适合小朋友的玩具
健身需要什么装备？
```

## 🔧 配置说明

### 交互策略配置

在 `InteractiveSearchAgent` 中可以调整交互阈值：

```python
self.interaction_threshold = {
    'query_clarity_score': 0.6,        # 查询明确度阈值
    'result_confidence_score': 0.7,    # 结果置信度阈值
    'max_interactions_per_session': 3, # 每次会话最大交互次数
}
```

### Web UI配置

在 `run_interactive.py` 中可以配置：

```bash
--host 127.0.0.1    # 服务器地址
--port 8000         # 服务器端口
```

## 🎯 交互策略详解

### 何时触发交互

Agent会在以下情况下与用户交互：

1. **查询明确度低**: 用户查询包含模糊词汇
2. **多个搜索方向**: 存在多种可能的搜索策略
3. **结果置信度低**: 搜索结果质量不够理想
4. **用户明确期望**: 查询中包含问号或"推荐"等词

### 交互类型选择

- **澄清查询**: 当缺少关键信息时
- **确认选择**: 当有多个选项时
- **获取反馈**: 当结果可能不满意时
- **提供建议**: 当可以给出有价值建议时

### 避免过度交互

- **交互次数限制**: 每次会话最多3次交互
- **智能阈值**: 基于置信度决定是否交互
- **上下文记忆**: 避免重复询问相同信息

## 🛠️ 开发指南

### 扩展交互类型

```python
class CustomInteractionType(Enum):
    PRICE_RANGE = "price_range"
    BRAND_PREFERENCE = "brand_preference"
    # 添加新的交互类型
```

### 自定义交互策略

```python
def _should_interact_with_user(self, context: Dict) -> bool:
    # 实现自定义的交互判断逻辑
    pass
```

### 添加新的工具

```python
# 在 InteractiveSearchAgent 中添加新工具
TOOLS.append(YourCustomTool())
```

## 📊 性能优化

### 减少交互延迟
- 使用异步处理
- 预加载常用选项
- 缓存分析结果

### 提升用户体验
- 智能预测用户需求
- 提供相关建议
- 记住用户偏好

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置

2. **依赖错误**
   - 运行 `python install_deps.py`
   - 检查Python版本（需要3.8+）

3. **API配置**
   - 确认API密钥设置
   - 检查网络连接

### 调试模式

```bash
# 启用详细日志
python run_interactive.py --debug

# 检查环境
python run_interactive.py --check-only
```

## 🚧 未来改进

- [ ] 用户偏好学习和记忆
- [ ] 多语言支持
- [ ] 语音交互功能
- [ ] 移动端适配
- [ ] 搜索结果可视化
- [ ] 用户行为分析

## 📄 许可证

本项目基于现有的SpeechSearchAgent扩展开发，遵循相同的许可证条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个交互式搜索系统！
