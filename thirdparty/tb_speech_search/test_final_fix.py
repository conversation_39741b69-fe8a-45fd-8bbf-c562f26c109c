#!/usr/bin/env python3
"""
最终测试修复后的agent
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_agent_final():
    """最终测试增强型agent"""
    print("🧪 最终测试增强型Agent...")
    
    try:
        from run import create_enhanced_agent
        
        # 创建增强型agent
        print("📋 创建增强型agent...")
        agent = create_enhanced_agent(model_id="gpt-4-0409")
        print("✅ Agent创建成功")
        
        # 测试查询
        query = "手机推荐"
        print(f"\n🔍 测试查询: {query}")
        
        # 运行agent
        result = agent.run(query)
        
        print(f"\n✅ 查询完成")
        print(f"📄 最终结果:")
        print(result)
        
        # 检查结果质量
        if isinstance(result, str):
            has_product_info = any(keyword in result.lower() for keyword in [
                '¥', '店铺', '付款', 'price', '商品', '销量', '图片'
            ])
            
            if has_product_info:
                print("✅ 结果包含详细商品信息")
            else:
                print("⚠️ 结果可能缺少详细商品信息")
                
            print(f"📊 结果长度: {len(result)} 字符")
        
        # 检查agent状态
        print(f"\n📊 Agent状态:")
        print(f"  已搜索商品: {getattr(agent, '_has_searched_products', False)}")
        print(f"  已获取详情: {getattr(agent, '_has_detailed_info', False)}")
        print(f"  待处理ID数量: {len(getattr(agent, '_pending_item_ids', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始最终测试...")
    success = test_enhanced_agent_final()
    
    if success:
        print("\n🎉 测试成功！增强型Agent工作正常！")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
