#!/usr/bin/env python3
"""
简单的WebSocket测试
"""

import asyncio
import json
import websockets


async def test_connection():
    """测试连接"""
    uri = "ws://127.0.0.1:8000/ws/test_session_456"
    
    try:
        print(f"连接到: {uri}")
        async with websockets.connect(uri) as websocket:
            print("✅ 连接成功")
            
            # 接收连接消息
            response = await websocket.recv()
            data = json.loads(response)
            print(f"连接消息: {data}")
            
            # 发送搜索请求
            request = {
                "type": "start_search",
                "query": "春游要买什么"
            }
            
            print(f"发送请求: {request}")
            await websocket.send(json.dumps(request))
            
            # 等待响应
            for i in range(10):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(response)
                    print(f"响应 {i+1}: {data.get('type')} - {data.get('message', '')[:100]}")
                    
                    if data.get('type') == 'agent_interaction':
                        options = data.get('options', [])
                        print(f"选项数量: {len(options)}")
                        if options:
                            print("选项:", options)
                            print("✅ 成功！")
                            return True
                        else:
                            print("❌ 没有选项")
                            return False
                            
                    elif data.get('type') == 'search_completed':
                        print("搜索完成")
                        break
                        
                except asyncio.TimeoutError:
                    print(f"超时 {i+1}")
                    
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


if __name__ == "__main__":
    result = asyncio.run(test_connection())
    print(f"测试结果: {'成功' if result else '失败'}")
