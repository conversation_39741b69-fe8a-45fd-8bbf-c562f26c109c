#!/usr/bin/env python3
"""
自定义Web UI，支持商品图片展示
"""

import gradio as gr
import re
import json
from typing import List, Dict, Any
from run import create_enhanced_agent

class ProductSearchUI:
    def __init__(self):
        self.agent = create_enhanced_agent()
        
    def parse_product_info(self, agent_response: str) -> List[Dict[str, Any]]:
        """解析agent响应，提取商品信息"""
        products = []
        
        # 尝试从响应中提取商品信息
        # 查找图片URL
        image_pattern = r'!\[图片\]\((http[^)]+)\)'
        images = re.findall(image_pattern, agent_response)
        
        # 查找购买链接
        link_pattern = r'\[([^\]]+)\]\((http[^)]+)\)'
        links = re.findall(link_pattern, agent_response)
        
        # 查找价格信息
        price_pattern = r'价格：¥([0-9,]+\.?[0-9]*)'
        prices = re.findall(price_pattern, agent_response)
        
        # 查找店铺信息
        shop_pattern = r'店铺：([^\n]+)'
        shops = re.findall(shop_pattern, agent_response)
        
        # 按行分割响应，查找商品标题
        lines = agent_response.split('\n')
        titles = []
        features = []
        
        for i, line in enumerate(lines):
            # 查找商品标题（通常是数字开头的行）
            if re.match(r'^\d+\.\s+(.+)', line):
                title_match = re.match(r'^\d+\.\s+(.+)', line)
                if title_match:
                    titles.append(title_match.group(1))
                    
                    # 查找特点信息（通常在标题后面）
                    feature_text = ""
                    for j in range(i+1, min(i+5, len(lines))):
                        if lines[j].startswith('特点：'):
                            feature_text = lines[j].replace('特点：', '').strip()
                            break
                    features.append(feature_text)
        
        # 组合商品信息
        max_items = max(len(titles), len(prices), len(images), len(shops))
        
        for i in range(max_items):
            product = {
                'title': titles[i] if i < len(titles) else f"商品 {i+1}",
                'price': prices[i] if i < len(prices) else "价格未知",
                'image_url': images[i] if i < len(images) else "",
                'shop': shops[i] if i < len(shops) else "店铺未知",
                'features': features[i] if i < len(features) else "",
                'link': links[i][1] if i < len(links) else ""
            }
            products.append(product)
        
        return products
    
    def search_products(self, query: str) -> tuple:
        """搜索商品并返回格式化结果"""
        if not query.strip():
            return "请输入搜索关键词", "", []
        
        try:
            # 调用agent进行搜索
            response = self.agent.run(query)
            
            # 解析商品信息
            products = self.parse_product_info(response)
            
            # 生成HTML展示
            html_content = self.generate_product_html(products, response)
            
            # 生成图片列表用于gallery展示
            image_list = [product['image_url'] for product in products if product['image_url']]
            
            return response, html_content, image_list
            
        except Exception as e:
            error_msg = f"搜索过程中出现错误: {str(e)}"
            return error_msg, f"<div style='color: red;'>{error_msg}</div>", []
    
    def generate_product_html(self, products: List[Dict[str, Any]], original_response: str) -> str:
        """生成商品展示的HTML"""
        if not products:
            return f"""
            <div style="padding: 20px; background: #f5f5f5; border-radius: 10px;">
                <h3>原始响应：</h3>
                <div style="background: white; padding: 15px; border-radius: 5px; white-space: pre-wrap;">
                    {original_response}
                </div>
            </div>
            """
        
        html = """
        <div style="padding: 20px;">
            <h2 style="color: #1890ff; margin-bottom: 20px;">🛍️ 商品推荐结果</h2>
        """
        
        for i, product in enumerate(products, 1):
            html += f"""
            <div style="border: 1px solid #e8e8e8; border-radius: 10px; padding: 20px; margin-bottom: 20px; background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <div style="display: flex; gap: 20px;">
                    <div style="flex-shrink: 0;">
                        {f'<img src="{product["image_url"]}" style="width: 200px; height: 200px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;" alt="商品图片" />' if product["image_url"] else '<div style="width: 200px; height: 200px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #999;">暂无图片</div>'}
                    </div>
                    <div style="flex: 1;">
                        <h3 style="color: #333; margin: 0 0 10px 0; font-size: 18px;">{product['title']}</h3>
                        <div style="margin-bottom: 10px;">
                            <span style="background: #ff4d4f; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold; font-size: 16px;">¥{product['price']}</span>
                        </div>
                        {f'<p style="color: #666; margin: 8px 0;"><strong>特点：</strong>{product["features"]}</p>' if product["features"] else ''}
                        <p style="color: #666; margin: 8px 0;"><strong>店铺：</strong>{product['shop']}</p>
                        {f'<a href="{product["link"]}" target="_blank" style="display: inline-block; background: #1890ff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-top: 10px;">🛒 立即购买</a>' if product["link"] else ''}
                    </div>
                </div>
            </div>
            """
        
        html += """
        </div>
        """
        
        return html
    
    def create_interface(self):
        """创建Gradio界面"""
        with gr.Blocks(title="淘宝商品搜索助手", theme=gr.themes.Soft()) as demo:
            gr.Markdown("""
            # 🛍️ 淘宝商品搜索助手
            
            输入您想要搜索的商品，AI助手将为您推荐相关商品，包括详细信息和图片展示。
            
            **支持的查询类型：**
            - 具体商品：如"iPhone 15"、"运动鞋"
            - 价格范围：如"2000左右的手机"
            - 推荐类：如"秋天水果推荐"、"学生用品推荐"
            """)
            
            with gr.Row():
                with gr.Column(scale=3):
                    query_input = gr.Textbox(
                        label="搜索关键词",
                        placeholder="请输入您想要搜索的商品...",
                        lines=2
                    )
                    search_btn = gr.Button("🔍 搜索商品", variant="primary", size="lg")
                
                with gr.Column(scale=1):
                    gr.Markdown("""
                    **使用提示：**
                    - 可以输入具体商品名称
                    - 可以包含价格范围
                    - 支持推荐类查询
                    - AI会自动获取详细信息
                    """)
            
            with gr.Tabs():
                with gr.TabItem("🎨 商品展示"):
                    product_html = gr.HTML(label="商品信息")
                
                with gr.TabItem("🖼️ 商品图片"):
                    product_gallery = gr.Gallery(
                        label="商品图片集",
                        show_label=True,
                        elem_id="gallery",
                        columns=3,
                        rows=2,
                        height="auto"
                    )
                
                with gr.TabItem("📝 原始响应"):
                    raw_response = gr.Textbox(
                        label="AI助手原始回复",
                        lines=15,
                        max_lines=20
                    )
            
            # 绑定搜索事件
            search_btn.click(
                fn=self.search_products,
                inputs=[query_input],
                outputs=[raw_response, product_html, product_gallery]
            )
            
            # 回车键搜索
            query_input.submit(
                fn=self.search_products,
                inputs=[query_input],
                outputs=[raw_response, product_html, product_gallery]
            )
            
            # 示例查询
            gr.Examples(
                examples=[
                    ["2000左右的手机"],
                    ["运动鞋推荐"],
                    ["秋天水果推荐"],
                    ["学生用品"],
                    ["无线蓝牙耳机"],
                    ["适合冬天的保暖用品"]
                ],
                inputs=[query_input]
            )
        
        return demo

def main():
    """启动应用"""
    print("🚀 正在启动淘宝商品搜索助手...")
    
    ui = ProductSearchUI()
    demo = ui.create_interface()
    
    print("✅ 应用启动成功！")
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

if __name__ == "__main__":
    main()
