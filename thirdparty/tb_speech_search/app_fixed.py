#!/usr/bin/env python3
"""
修复网络问题的启动脚本
"""

import os
import gradio as gr

# 清除localhost代理设置
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'

def search_products(query: str):
    """搜索商品"""
    if not query.strip():
        return "请输入搜索关键词", ""
    
    try:
        from run import create_enhanced_agent
        from app_with_images import parse_and_display_products
        
        # 创建agent并搜索
        agent = create_enhanced_agent()
        response = agent.run(query)
        
        # 生成带图片的HTML展示
        html_display = parse_and_display_products(response)
        
        return response, html_display
        
    except Exception as e:
        error_msg = f"搜索过程中出现错误: {str(e)}"
        error_html = f"""
        <div style="padding: 20px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 8px; color: #a8071a;">
            <h3 style="color: #cf1322; margin-bottom: 10px;">❌ 搜索失败</h3>
            <p>{error_msg}</p>
        </div>
        """
        return error_msg, error_html

# 创建简化的界面
with gr.Blocks(title="淘宝商品搜索助手", theme=gr.themes.Soft()) as demo:
    gr.Markdown("# 🛍️ 淘宝商品搜索助手 (网络修复版)")
    
    with gr.Row():
        query_input = gr.Textbox(
            label="🔍 搜索关键词",
            placeholder="请输入您想要搜索的商品...",
            lines=2
        )
        search_btn = gr.Button("🚀 开始搜索", variant="primary")
    
    with gr.Tabs():
        with gr.TabItem("🎨 商品展示"):
            product_display = gr.HTML(label="商品信息展示")
        with gr.TabItem("📝 原始回复"):
            raw_output = gr.Textbox(label="AI助手原始回复", lines=15)
    
    search_btn.click(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )
    
    query_input.submit(
        fn=search_products,
        inputs=[query_input],
        outputs=[raw_output, product_display]
    )

if __name__ == "__main__":
    print("🚀 启动网络修复版...")
    print(f"📡 使用端口: 7860")
    print("🔧 已应用网络修复")
    
    try:
        demo.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            show_error=True,
            quiet=False,
            enable_queue=False,
            show_tips=False,
            inbrowser=True
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请尝试手动运行: python -m http.server 8000")
