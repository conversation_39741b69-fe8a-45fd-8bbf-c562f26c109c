#!/usr/bin/env python3
"""
交互式搜索系统演示
简化版本，用于测试核心功能
"""

import asyncio
from interactive_agent import InteractiveSearchAgent, InteractionType, UserInteraction
from run import create_intelligent_agent

def demo_interaction_logic():
    """演示交互逻辑"""
    print("🧪 演示交互逻辑")
    print("=" * 40)
    
    try:
        # 创建基础agent
        base_agent = create_intelligent_agent()
        
        # 创建交互式agent
        agent = InteractiveSearchAgent(
            model=base_agent.model,
            tools=list(base_agent.tools.values()),
            max_steps=3,
            name="demo_agent"
        )
        
        print("✓ 交互式Agent创建成功")
        
        # 测试查询分析
        test_queries = [
            "苹果手机",  # 明确查询
            "春游要带什么？",  # 模糊查询
            "推荐一些好用的东西",  # 非常模糊
        ]
        
        for query in test_queries:
            print(f"\n📝 分析查询: '{query}'")
            analysis = agent._analyze_query_clarity(query)
            print(f"   明确度分数: {analysis.get('query_clarity_score', 0):.2f}")
            print(f"   需要澄清: {analysis.get('needs_clarification', False)}")
            
            # 测试是否需要交互
            should_interact = agent._should_interact_with_user({
                **analysis,
                'multiple_search_directions': False,
                'user_expects_interaction': '?' in query
            })
            print(f"   需要交互: {should_interact}")
            
            if should_interact and analysis.get('needs_clarification'):
                # 生成澄清问题
                interaction = agent._generate_clarification_question(
                    query, 
                    analysis.get('analysis', {})
                )
                print(f"   澄清问题: {interaction.message}")
                if interaction.options:
                    print(f"   选项: {interaction.options}")
        
        return True
        
    except Exception as e:
        print(f"✗ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_simple_search():
    """演示简单搜索"""
    print("\n🔍 演示简单搜索")
    print("=" * 40)
    
    try:
        # 创建基础agent
        base_agent = create_intelligent_agent()
        
        # 创建交互式agent
        agent = InteractiveSearchAgent(
            model=base_agent.model,
            tools=list(base_agent.tools.values()),
            max_steps=3,
            name="demo_agent"
        )
        
        # 运行简单搜索
        query = "苹果手机"
        print(f"搜索查询: {query}")
        
        result = agent.run_interactive(query)
        print(f"搜索结果: {result[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 搜索演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def demo_async_interaction():
    """演示异步交互"""
    print("\n⚡ 演示异步交互")
    print("=" * 40)
    
    try:
        # 创建基础agent
        base_agent = create_intelligent_agent()
        
        # 创建交互式agent
        agent = InteractiveSearchAgent(
            model=base_agent.model,
            tools=list(base_agent.tools.values()),
            max_steps=3,
            name="demo_agent"
        )
        
        # 设置模拟回调
        interactions_received = []
        status_updates = []
        
        async def mock_interaction_callback(interaction):
            interactions_received.append(interaction)
            print(f"   收到交互请求: {interaction.message}")
            
            # 模拟用户响应
            if interaction.options:
                selected = interaction.options[0]
                print(f"   模拟用户选择: {selected}")
                agent.handle_user_response(interaction.interaction_id, selected, selected)
        
        async def mock_status_callback(status):
            status_updates.append(status)
            print(f"   状态更新: {status}")
        
        agent.set_interaction_callbacks(
            on_interaction_needed=mock_interaction_callback,
            on_status_update=mock_status_callback
        )
        
        # 运行异步搜索
        query = "春游要带什么？"
        print(f"异步搜索查询: {query}")
        
        result = await agent.run_interactive_async(query)
        print(f"异步搜索结果: {result[:200]}...")
        
        print(f"收到 {len(interactions_received)} 个交互请求")
        print(f"收到 {len(status_updates)} 个状态更新")
        
        return True
        
    except Exception as e:
        print(f"✗ 异步演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主演示函数"""
    print("🎭 交互式商品搜索系统演示")
    print("=" * 50)
    
    demos = [
        ("交互逻辑", demo_interaction_logic),
        ("简单搜索", demo_simple_search),
        ("异步交互", lambda: asyncio.run(demo_async_interaction())),
    ]
    
    results = []
    for name, demo_func in demos:
        print(f"\n🎯 运行演示: {name}")
        try:
            result = demo_func()
            results.append(result)
            if result:
                print(f"✅ {name} 演示成功")
            else:
                print(f"❌ {name} 演示失败")
        except Exception as e:
            print(f"❌ {name} 演示异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("演示结果:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有演示成功 ({passed}/{total})")
        print("\n✅ 交互式搜索系统核心功能正常！")
        print("\n🚀 可以启动完整系统:")
        print("  python run_interactive.py")
        
    else:
        print(f"⚠️  部分演示失败 ({passed}/{total})")
        print("核心功能可能需要调整")

if __name__ == "__main__":
    main()
