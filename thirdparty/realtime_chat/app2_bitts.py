import asyncio
import base64
import json
from pathlib import Path
import os
import aiohttp  # pip install aiohttp
import gradio as gr
import numpy as np
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.responses import HTMLResponse, StreamingResponse
from fastrtc import (
    AdditionalOutputs,
    AsyncStreamHandler,
    Stream,
    get_twilio_turn_credentials,
    wait_for_item,
)
import uuid
from gradio.utils import get_space
from tts8 import ByteDanceTTSStreaming, StreamConfig, TriggerStrategy
import struct 
import string

class InterruptEvent:
    pass


load_dotenv()
cur_dir = Path(__file__).parent
load_dotenv("key.env")
# sd.default.device = (3, 3)  # (Input-Gerät, Output-Gerät)

# print(f"Used Mic: {sd.query_devices(3)['name']}")
# print(f"Used Speaker: {sd.query_devices(3)['name']}")
SAMPLE_RATE = 48000

instruction = """
<Role>
You a helpful assistant.
"""
from openai import AsyncAzureOpenAI
import json
from typing import AsyncGenerator
from chat import InterruptibleAzureOpenAI
API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "********************************")
ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api")
MODEL_NAME = os.getenv("AZURE_OPENAI_MODEL_NAME", "gpt-4-0409")

async def clear_queue(queue):
    while not queue.empty():
        try:
            item = await queue.get()
            queue.task_done()  # 可选，用于与 join() 配合使用
        except asyncio.QueueEmpty:
            break
class ChatEvent:
    pass  
class AzureAudioHandler(AsyncStreamHandler):
    def __init__(self) -> None:
        super().__init__(
            expected_layout="mono",
            output_sample_rate=SAMPLE_RATE,
            input_sample_rate=24000,
        )
        self.ws = None
        self.session = None
        self.output_queue = asyncio.Queue()
        self.llm_input_queue = asyncio.Queue()
        self.llm_output_queue = asyncio.Queue()
        self.voice_queue = asyncio.Queue()
        self.voice_inqueue = asyncio.Queue()
        # 创建TTS客户端
        # self.tts_client = AlibabaStreamingTTSFixed(API_KEY)
        self.chat = InterruptibleAzureOpenAI(api_key=API_KEY, endpoint=ENDPOINT, api_version='')
        # self.tts_client = OpenAITTSClient(api_key="********************************")
        # self.tts_player = RealTimeStreamingPlayer()
        self.tts_server = ByteDanceTTSStreaming(
            app_id = "4301754327" ,
            token = "iTyYQaoJGz1dNspdaXdBx0LltSwIHjF5",
            speaker='zh_female_shuangkuaisisi_moon_bigtts',
            audio_format='pcm',
            # audio_sample_rate=24000,
            # # 文本缓冲参数
            # min_chars=15,       # 最小15字符
            # max_chars=100,      # 最大100字符
            # sentence_timeout=2.0,   # 2秒句子超时
            # paragraph_timeout=4.0   # 4秒段落超时
        )

        
        # This internal buffer is not used directly in receive_messages.
        # Instead, multiple audio chunks are collected in the emit() method.
        # If needed, a continuous buffer can also be implemented here.
        # self.audio_buffer = bytearray()

    def copy(self):
        return AzureAudioHandler()

    async def start_up(self):
        """Connects to the Azure Real-time Audio API via WebSocket using aiohttp."""
        # Replace the following placeholders with your actual Azure values:
        azure_api_key = "********************************"  # e.g., "your-api-key"
        azure_resource_name = "idealab.alibaba-inc.com"  # e.g., "aigdopenai"
        deployment_id = "your-deployment-id"  # e.g., "gpt-4o-realtime-preview"
        api_version = "2024-10-01-preview"
        # azure_endpoint = (
        #     f"wss://{azure_resource_name}.openai.azure.com/openai/realtime"
        #     f"?model=gpt-4o-realtime-preview-1001"
        # )
        azure_endpoint = "wss://idealab.alibaba-inc.com/api/openai/realtime?model=gpt-4o-realtime-preview-1001"
        headers = {"api-key": azure_api_key}

        self.session = aiohttp.ClientSession()
        self.ws = await self.session.ws_connect(azure_endpoint, headers=headers)
        # Send initial session parameters
        session_update_message = {
            "type": "session.update",
            # "session": {
            #     "turn_detection": {"type": "server_vad"},
            #     "instructions": instruction,
            #     "voice": "ballad",  # Possible voices see  https://platform.openai.com/docs/guides/realtime-model-capabilities#voice-options
            # },
            "session":{
                "modalities": ["text", "audio"],
                # "instructions": "Your knowledge cutoff is 2023-10. You are a helpful assistant.",
                # "voice": "alloy",
                "input_audio_format": "pcm16",
                # "output_audio_format": "pcm16",
                "input_audio_transcription": {
                    "model": "whisper-1",
                    "language": "zh"
                },

                "turn_detection": {
                    "type": "server_vad",
                    "threshold": 0.7,
                    "prefix_padding_ms": 100,
                    "silence_duration_ms": 500
                },
                "input_audio_noise_reduction": {
                    "type": "near_field"
                },
                }
        }
        # session_update_message = {
        #     "type": "session.update",
        #     "session": {
        #         "turn_detection": {"type": "server_vad"},
        #         "instructions": instruction,
        #         "voice": "ballad",  # Possible voices see  https://platform.openai.com/docs/guides/realtime-model-capabilities#voice-options
        #     },
        # }
        await self.ws.send_str(json.dumps(session_update_message))
            # 配置流式处理参数
        self.stream_config = StreamConfig(
            trigger_strategy=TriggerStrategy.HYBRID,
            max_words_per_chunk=30,
            timeout_seconds=3.0,
            min_words_for_punctuation=5
        )
        # Start receiving messages asynchronously
        asyncio.create_task(self.receive_messages())
        # asyncio.create_task(self.tts_server.run_tts_worker(self.voice_inqueue, self.voice_queue))
        self.processer_task = asyncio.create_task(self.tts_server.run_stream_tts_processor(
            word_stream=self.voice_inqueue,
            voice_queue=self.voice_queue,
            config=self.stream_config,
            max_workers=1,
            # on_audio_callback=audio_callback
        ))

    async def interrupt(self, conv_id):
        async def interrupt_llm(conv_id):
            if await self.chat.can_interrupt():
                self.clear_queue()
                success = await self.chat.interrupt()
        async def interrupt_tts(conv_id):
            interrupt_event = InterruptEvent()
            interrupt_event.type = "[interruption]"
            interrupt_event.id = conv_id
            interrupt_event.content = "[Conversation interrupted]"
            if self.tts_server.can_interrupt():
                print("tts runing打断")
                # Send interruption event through RTC connection
                await clear_queue(self.voice_queue)
                await self.voice_queue.put(AdditionalOutputs(interrupt_event))
                await self.tts_server.interrupt()
                self.clear_queue()
                await self.voice_queue.put(AdditionalOutputs(interrupt_event))
                
        tasks = [interrupt_llm(conv_id), interrupt_tts(conv_id)]
        await asyncio.gather(*tasks)
        # asyncio.create_task(self.tts_server.run_stream_tts_processor(
        #     word_stream=self.voice_inqueue,
        #     voice_queue=self.voice_queue,
        #     config=self.stream_config,
        #     max_workers=1,
        #     # on_audio_callback=audio_callback
        # ))
        processer_task = await self.tts_server.resume(self.voice_inqueue, self.voice_queue)
        
        if processer_task != None:
            self.processer_task.cancel()
            self.processer_task = processer_task
        

    async def receive_messages(self):
        """Handles incoming WebSocket messages and processes them accordingly."""
        async for msg in self.ws:
            if msg.type == aiohttp.WSMsgType.TEXT:
                print("Received event:", msg.data)  # Debug output
                event = json.loads(msg.data)
                event_type = event.get("type")
                if event_type in ["final", "response.audio_transcript.done"]:
                    transcript = event.get("transcript", "")

                    # Wrap the transcript in an object with a .transcript attribute
                    class TranscriptEvent:
                        pass

                    te = TranscriptEvent()
                    te.transcript = transcript
                    await self.output_queue.put(AdditionalOutputs(te))
                elif event_type == "partial":
                    print("Partial transcript:", event.get("transcript", ""))
                elif event_type == "response.audio.delta":
                    audio_message = event.get("delta")
                    if audio_message:
                        try:
                            audio_bytes = base64.b64decode(audio_message)
                            # Assuming 16-bit PCM (int16)
                            audio_array = np.frombuffer(audio_bytes, dtype=np.int16)
                            # Interpret as mono audio:
                            audio_array = audio_array.reshape(1, -1)
                            # Instead of playing the audio, add the chunk to the output queue
                            await self.output_queue.put(
                                (self.output_sample_rate, audio_array)
                            )
                        except Exception as e:
                            print("Error processing audio data:", e)
                elif event_type == "conversation.item.input_audio_transcription.completed":
                    transcript = event.get("transcript", "")
                    # Wrap the transcript in an object with a .transcript attribute
                    class TranscriptEvent:
                        pass
                    
                    if is_punctuation_only(transcript) or transcript.strip() == '':
                        break

                    conv_id = str(uuid.uuid4())
                    await self.interrupt(conv_id=conv_id)
                    # if self.chat.interrupt():
                    #     await clear_queue(self.output_queue)


                    
                        # if success:
                        #     print("clean llm queue")
                            # await clear_queue(self.output_queue)
                    # async for chunk in self.chat.stream_chat_with_interrupt(transcript, model=MODEL_NAME):
                    #     # print(chunk, end="", flush=True)
                    #     te = ChatEvent()
                    #     te.content = chunk
                    #     te.id = conv_id
                    #     await self.output_queue.put(AdditionalOutputs(te))


                    interrupt_event = InterruptEvent()
                    interrupt_event.type = "[start]"
                    interrupt_event.id = conv_id
                    interrupt_event.content = "[Conversation start]"
                    await self.voice_queue.put(AdditionalOutputs(interrupt_event))
                    te = ChatEvent()
                    te.content = "[user]:"+transcript #.replace("[用户已经打断]", "")
                    te.id = conv_id+"1"
                    te.type='msg'
                    await self.voice_queue.put(AdditionalOutputs(te))
                    asyncio.create_task(self.process_llm_response(transcript, conv_id))

                else:
                    print("Unknown event:", event)
            elif msg.type == aiohttp.WSMsgType.ERROR:
                break
    async def process_llm_response(self, transcript: str, conv_id: str):
        """独立处理 LLM 响应的方法"""
        # content = ''
        try:
            async for chunk in self.chat.stream_chat_with_interrupt(transcript, model=MODEL_NAME):
                te = ChatEvent()
                te.content = chunk #.replace("[用户已经打断]", "")
                # content += chunk.replace("[用户已经打断]", "")
                te.id = conv_id
                te.type='msg'
                await self.voice_queue.put(AdditionalOutputs(te))
                await self.voice_inqueue.put(chunk)
                # await self.tts_player.voice_queue.put(AdditionalOutputs(te))
                # await self.tts_client.play_streaming_audio("始生成并播放语音，文本长度")
        except Exception as e:
            print(f"Error in LLM processing: {e}")
            
        # te = ChatEvent()
        # te.content = '这是一个流式的代码，text_queue可能是逐个词输入的'
        # te.id = '123'
        # # print("content:", content)
        # await self.voice_inqueue.put("这是一个流式的代码，text_queue可能是逐个词输入的")

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Sends received audio frames to the WebSocket."""
        if not self.ws or self.ws.close_code:
            return
        try:
            _, array = frame
            array = array.squeeze()
            audio_message = base64.b64encode(array.tobytes()).decode("utf-8")
            message = {"type": "input_audio_buffer.append", "audio": audio_message}
            await self.ws.send_str(json.dumps(message))
        except aiohttp.ClientConnectionError as e:
            print("Connection closed while sending:", e)
            return

    async def emit(self) -> tuple[int, np.ndarray] | AdditionalOutputs | None:
        """
        Collects multiple audio chunks from the queue before returning them as a single contiguous audio array.
        This helps smooth playback.
        """
        item = await wait_for_item(self.voice_queue)
        # If it's a transcript event, return it immediately.
        if not isinstance(item, tuple):
            return item
        # Otherwise, it is an audio chunk (sample_rate, audio_array)
        sample_rate, first_chunk = item
        if type(first_chunk) == type(None):
            return
        
        audio_chunks = [first_chunk]
        print("first_chunk:", first_chunk.shape)
        # Define a minimum length (e.g., 0.1 seconds)
        min_samples = int(SAMPLE_RATE * 0.1)  # 0.1 sec
        # Collect more audio chunks until we have enough samples
        while audio_chunks and audio_chunks[0].shape[1] < min_samples:
            try:
                extra = self.voice_queue.get_nowait()
                if isinstance(extra, tuple):
                    _, chunk = extra
            
                    audio_chunks.append(chunk)
                else:
                    # If it's not an audio chunk, put it back
                    await self.voice_queue.put(extra)
                    break
            except asyncio.QueueEmpty:
                break
        # Concatenate collected chunks along the time axis (axis=1)
        full_audio = np.concatenate(audio_chunks, axis=1)
        # with open("full_audio.pcm", "ab") as f:
        #     f.write(full_audio.tobytes())

        return (sample_rate, full_audio)

    async def shutdown(self) -> None:
        """Closes the WebSocket and session properly."""
        if self.ws:
            await self.ws.close()
            self.ws = None
        if self.session:
            await self.session.close()
            self.session = None

async def clear_queue(queue: asyncio.Queue):
    while not queue.empty():
        try:
            item = await queue.get()
            # 如果使用了 `put()` + `join()`，建议调用 `task_done()`
            queue.task_done()
        except asyncio.QueueEmpty:
            break



def is_punctuation_only(transcript):
    return all(c in string.punctuation for c in transcript) and len(transcript) > 0

def update_chatbot(chatbot: list[dict], response) -> list[dict]:
    """Appends the AI assistant's transcript response to the chatbot messages."""
    chatbot.append({"role": "assistant", "content": response.transcript})
    return chatbot


chatbot = gr.Chatbot(type="messages")
latest_message = gr.Textbox(type="text", visible=False)
stream = Stream(
    AzureAudioHandler(),
    mode="send-receive",
    modality="audio",
    additional_inputs=[chatbot],
    additional_outputs=[chatbot],
    additional_outputs_handler=update_chatbot,
    rtc_configuration=get_twilio_turn_credentials() if get_space() else None,
    concurrency_limit=5 if get_space() else None,
    time_limit=90 if get_space() else None,
)

app = FastAPI()
stream.mount(app)


@app.get("/")
async def _():
    rtc_config = get_twilio_turn_credentials() if get_space() else None
    print('cur_dir:',cur_dir)
    html_content = (cur_dir / "index.html").read_text()
    html_content = html_content.replace("__RTC_CONFIGURATION__", json.dumps(rtc_config))
    return HTMLResponse(content=html_content)


@app.get("/outputs")
def _(webrtc_id: str):
    async def output_stream():
        import json

        async for output in stream.output_stream(webrtc_id):
            s = json.dumps({"role": "assistant", "content": output.args[0].content, "id":output.args[0].id, "type":output.args[0].type})
            # print(s)
            yield f"event: output\ndata: {s}\n\n"

    return StreamingResponse(output_stream(), media_type="text/event-stream")


if __name__ == "__main__":
    import os

    if (mode := os.getenv("MODE")) == "UI":
        stream.ui.launch(server_port=7878, ssl_certfile='./cert.pem', ssl_keyfile='./key.pem')
    elif mode == "PHONE":
        stream.fastphone(host="0.0.0.0", port=7878, ssl_certfile='./cert.pem', ssl_keyfile='./key.pem')
    else:
        import uvicorn

        uvicorn.run(app, host="0.0.0.0", port=7878, ssl_certfile='./cert.pem', ssl_keyfile='./key.pem')
