<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Chat</title>
    <style>
        body {
            font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #0a0a0a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            height: 100vh;
            box-sizing: border-box;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            height: calc(100% - 100px);
            display: flex;
            gap: 20px;
        }
        .logo {
            text-align: center;
            margin-bottom: 40px;
            grid-column: 1 / -1;
        }
        .main-content {
            display: flex;
            gap: 20px;
            width: 100%;
            height: 100%;
        }
        .chat-section {
            flex: 1;
            min-width: 400px;
            display: flex;
            flex-direction: column;
        }
        .chat-container {
            border: 1px solid #333;
            padding: 20px;
            height: 90%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }
        .product-section {
            flex: 1;
            min-width: 400px;
            display: flex;
            flex-direction: column;
        }
        .product-container {
            border: 1px solid #333;
            padding: 20px;
            height: 90%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            background-color: #111;
        }
        .product-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333;
        }
        .product-header h3 {
            margin: 0;
            color: #1890ff;
            font-size: 18px;
        }
        .product-carousel {
            flex: 1;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .product-display {
            width: 100%;
            max-width: 350px;
            position: relative;
            background: #1a1a1a;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }
        .product-display:hover {
            transform: translateY(-2px);
        }
        .product-image-container {
            width: 100%;
            height: 200px;
            margin-bottom: 16px;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #333;
        }
        .product-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background-color: #f9f9f9;
        }
        .product-details {
            text-align: center;
        }
        .product-title {
            color: #fff;
            font-size: 16px;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .product-price {
            color: #ff6700;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .product-price::before {
            content: "¥";
            font-size: 18px;
        }
        .product-meta {
            color: #999;
            font-size: 12px;
            line-height: 1.3;
            margin-bottom: 16px;
        }
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 10;
            color: #fff;
            font-size: 18px;
            transition: all 0.2s ease;
        }
        .nav-button:hover {
            background-color: #ff6700;
            border-color: #ff6700;
            transform: translateY(-50%) scale(1.1);
        }
        .nav-button:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }
        .nav-button:disabled:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-50%) scale(1);
        }
        .prev-button {
            left: -60px;
        }
        .next-button {
            right: -60px;
        }
        .product-indicator {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 8px;
        }
        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #444;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .indicator-dot.active {
            background-color: #ff6700;
            width: 16px;
            border-radius: 4px;
        }
        .indicator-dot:hover {
            background-color: #666;
        }
        .indicator-dot.active:hover {
            background-color: #ff8533;
        }
        .select-button {
            background-color: #ff6700;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 12px;
        }
        .select-button:hover {
            background-color: #ff4e00;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 103, 0, 0.3);
        }
        .no-products {
            text-align: center;
            color: #666;
            padding: 40px 20px;
            font-style: italic;
        }
        .voice-control-hint {
            margin-top: 10px;
            color: #666;
            font-size: 11px;
            text-align: center;
            opacity: 0.7;
            padding: 5px 10px;
        }
        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
        }
        .message {
            margin-bottom: 20px;
            padding: 12px;
            border-radius: 4px;
            font-size: 16px;
            line-height: 1.5;
        }
        .message.user {
            background-color: #1a1a1a;
            margin-left: 20%;
        }
        .message.assistant {
            background-color: #262626;
            margin-right: 20%;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            background-color: transparent;
            color: #ffffff;
            border: 1px solid #ffffff;
            padding: 12px 24px;
            font-family: inherit;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        button:hover {
            border-width: 2px;
            transform: scale(1.02);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }
        #audio-output {
            display: none;
        }
        .icon-with-spinner {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            min-width: 180px;
        }
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            flex-shrink: 0;
        }
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        .pulse-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            min-width: 180px;
        }
        .pulse-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #ffffff;
            opacity: 0.2;
            flex-shrink: 0;
            transform: translateX(-0%) scale(var(--audio-level, 1));
            transition: transform 0.1s ease;
        }
        /* Add styles for toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 16px 24px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        .toast.error {
            background-color: #f44336;
            color: white;
        }
        .toast.warning {
            background-color: #ffd700;
            color: black;
        }
        .toast.info {
            background-color: #2196f3;
            color: white;
        }
        /* Audio monitoring status */
        .audio-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        .audio-status.good {
            background-color: #4caf50;
            color: white;
        }
        .audio-status.poor {
            background-color: #ff9800;
            color: white;
        }
        .audio-status.bad {
            background-color: #f44336;
            color: white;
        }
    </style>
</head>

<body>
    <!-- Add toast element after body opening tag -->
    <div id="error-toast" class="toast"></div>
    <div id="audio-status" class="audio-status"></div>
    <div class="container">
        <div class="logo">
            <h1>实时语音商品搜索</h1>
        </div>
        <div class="main-content">
            <div class="chat-section">
                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages"></div>
                </div>
                <div class="controls">
                    <button id="start-button">Start Conversation</button>
                </div>
            </div>
            <div class="product-section">
                <div class="product-container">
                    <div class="product-header">
                        <h3>🛍️ 商品搜索结果</h3>
                    </div>
                    <div class="product-carousel" id="product-carousel">
                        <div class="no-products" id="no-products">
                            暂无搜索结果<br>
                            请开始语音对话并说出您想要搜索的商品
                        </div>
                        <div class="product-display" id="product-display" style="display: none;">
                            <button class="nav-button prev-button" id="prev-button" onclick="showPreviousProduct()">❮</button>
                            <button class="nav-button next-button" id="next-button" onclick="showNextProduct()">❯</button>
                            <div class="product-image-container">
                                <img class="product-image" id="product-image" src="" alt="">
                            </div>
                            <div class="product-details">
                                <div class="product-title" id="product-title"></div>
                                <div class="product-price" id="product-price"></div>
                                <div class="product-meta" id="product-meta"></div>
                                <button class="select-button" id="select-button" onclick="selectCurrentProduct()">选择此商品</button>
                            </div>
                            <div class="voice-control-hint">
                                💬 语音控制：说"左滑"、"右滑"、"选择商品"
                            </div>
                        </div>
                        <div class="product-indicator" id="product-indicator"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <audio id="audio-output"></audio>

    <script>
        let peerConnection;
        let webrtc_id;
        const audioOutput = document.getElementById('audio-output');
        const startButton = document.getElementById('start-button');
        const chatMessages = document.getElementById('chat-messages');
        let audioLevel = 0;
        let animationFrame;
        let audioContext, analyser, audioSource;

        // 音量控制相关变量
        let volumeControl = {
            originalVolume: 1.0,  // 原始音量
            currentVolume: 1.0,   // 当前音量
            isReduced: false      // 是否已降低音量
        };
        
        // Audio playback monitoring variables
        let audioPlaybackMonitor = {
            isMonitoring: false,
            audioElement: null,
            audioContext: null,
            sourceNode: null,
            analyserNode: null,
            lastPlayTime: 0,
            lastBufferTime: 0,
            bufferUnderrunCount: 0,
            totalBufferChecks: 0,
            stallCount: 0,
            lastCurrentTime: 0,
            playbackGaps: [],
            expectedPlaybackRate: 1.0,
            statusUpdateInterval: null,
            bufferCheckInterval: null,
            silenceThreshold: 0.001,
            maxSilenceDuration: 2000, // 2 seconds for received audio
            lastAudioActivity: 0,
            audioDropThreshold: 0.05 // 5% drop threshold
        };
        
        function updateButtonState() {
            const button = document.getElementById('start-button');
            if (peerConnection && (peerConnection.connectionState === 'connecting' || peerConnection.connectionState === 'new')) {
                button.innerHTML = `
                    <div class="icon-with-spinner">
                        <div class="spinner"></div>
                        <span>Connecting...</span>
                    </div>
                `;
            } else if (peerConnection && peerConnection.connectionState === 'connected') {
                button.innerHTML = `
                    <div class="pulse-container">
                        <div class="pulse-circle"></div>
                        <span>Stop Conversation</span>
                    </div>
                `;
            } else {
                button.innerHTML = 'Start Conversation';
            }
        }
        
        function startAudioPlaybackMonitoring() {
            const audioElement = document.getElementById('audio-output');
            audioPlaybackMonitor.audioElement = audioElement;
            audioPlaybackMonitor.isMonitoring = true;
            audioPlaybackMonitor.lastPlayTime = performance.now();
            audioPlaybackMonitor.lastAudioActivity = Date.now();
            
            // Setup audio context for playback analysis
            if (!audioPlaybackMonitor.audioContext && audioElement.srcObject) {
                try {
                    audioPlaybackMonitor.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    audioPlaybackMonitor.sourceNode = audioPlaybackMonitor.audioContext.createMediaStreamSource(audioElement.srcObject);
                    audioPlaybackMonitor.analyserNode = audioPlaybackMonitor.audioContext.createAnalyser();
                    audioPlaybackMonitor.analyserNode.fftSize = 256;
                    audioPlaybackMonitor.sourceNode.connect(audioPlaybackMonitor.analyserNode);
                } catch (e) {
                    console.warn('Could not setup playback audio context:', e);
                }
            }
            
            // Monitor audio element events
            audioElement.addEventListener('stalled', handleAudioStall);
            audioElement.addEventListener('waiting', handleAudioWaiting);
            audioElement.addEventListener('canplay', handleAudioCanPlay);
            audioElement.addEventListener('timeupdate', handleTimeUpdate);
            
            // Start monitoring intervals
            audioPlaybackMonitor.statusUpdateInterval = setInterval(updatePlaybackStatus, 1000);
            audioPlaybackMonitor.bufferCheckInterval = setInterval(checkAudioBuffer, 100);
            
            document.getElementById('audio-status').style.display = 'block';
            console.log('Started audio playback monitoring');
        }
        
        function stopAudioPlaybackMonitoring() {
            audioPlaybackMonitor.isMonitoring = false;
            
            if (audioPlaybackMonitor.statusUpdateInterval) {
                clearInterval(audioPlaybackMonitor.statusUpdateInterval);
                audioPlaybackMonitor.statusUpdateInterval = null;
            }
            
            if (audioPlaybackMonitor.bufferCheckInterval) {
                clearInterval(audioPlaybackMonitor.bufferCheckInterval);
                audioPlaybackMonitor.bufferCheckInterval = null;
            }
            
            const audioElement = audioPlaybackMonitor.audioElement;
            if (audioElement) {
                audioElement.removeEventListener('stalled', handleAudioStall);
                audioElement.removeEventListener('waiting', handleAudioWaiting);
                audioElement.removeEventListener('canplay', handleAudioCanPlay);
                audioElement.removeEventListener('timeupdate', handleTimeUpdate);
            }
            
            if (audioPlaybackMonitor.audioContext) {
                audioPlaybackMonitor.audioContext.close();
                audioPlaybackMonitor.audioContext = null;
            }
            
            document.getElementById('audio-status').style.display = 'none';
            
            // Reset counters
            audioPlaybackMonitor.bufferUnderrunCount = 0;
            audioPlaybackMonitor.totalBufferChecks = 0;
            audioPlaybackMonitor.stallCount = 0;
            audioPlaybackMonitor.playbackGaps = [];
            
            console.log('Stopped audio playback monitoring');
        }
        
        function handleAudioStall(event) {
            audioPlaybackMonitor.stallCount++;
            console.warn('Audio stalled:', event);
            showToast('Audio playback stalled', 'warning');
        }
        
        function handleAudioWaiting(event) {
            console.warn('Audio waiting for data:', event);
            showToast('Waiting for audio data', 'info');
        }
        
        function handleAudioCanPlay(event) {
            console.log('Audio can play again');
        }
        
        function handleTimeUpdate(event) {
            const currentTime = event.target.currentTime;
            const now = performance.now();
            
            // Detect playback gaps
            if (audioPlaybackMonitor.lastCurrentTime > 0) {
                const expectedTimeDiff = (now - audioPlaybackMonitor.lastPlayTime) / 1000;
                const actualTimeDiff = currentTime - audioPlaybackMonitor.lastCurrentTime;
                const gap = Math.abs(expectedTimeDiff - actualTimeDiff);
                
                if (gap > 0.1) { // 100ms gap threshold
                    audioPlaybackMonitor.playbackGaps.push({
                        time: now,
                        gap: gap
                    });
                    
                    // Keep only recent gaps (last 30 seconds)
                    audioPlaybackMonitor.playbackGaps = audioPlaybackMonitor.playbackGaps.filter(
                        g => now - g.time < 30000
                    );
                }
            }
            
            audioPlaybackMonitor.lastCurrentTime = currentTime;
            audioPlaybackMonitor.lastPlayTime = now;
        }
        
        function checkAudioBuffer() {
            if (!audioPlaybackMonitor.isMonitoring) return;
            
            const audioElement = audioPlaybackMonitor.audioElement;
            if (!audioElement) return;
            
            audioPlaybackMonitor.totalBufferChecks++;
            
            // Check buffer health
            if (audioElement.buffered && audioElement.buffered.length > 0) {
                const bufferedEnd = audioElement.buffered.end(audioElement.buffered.length - 1);
                const currentTime = audioElement.currentTime;
                const bufferAhead = bufferedEnd - currentTime;
                
                // Buffer underrun detection
                if (bufferAhead < 0.1) { // Less than 100ms buffered ahead
                    audioPlaybackMonitor.bufferUnderrunCount++;
                }
            }
            
            // Check received audio activity
            if (audioPlaybackMonitor.analyserNode) {
                const dataArray = new Uint8Array(audioPlaybackMonitor.analyserNode.frequencyBinCount);
                audioPlaybackMonitor.analyserNode.getByteFrequencyData(dataArray);
                const average = Array.from(dataArray).reduce((a, b) => a + b, 0) / dataArray.length;
                const audioLevel = average / 255;
                
                if (audioLevel > audioPlaybackMonitor.silenceThreshold) {
                    audioPlaybackMonitor.lastAudioActivity = Date.now();
                }
            }
        }
        
        function updatePlaybackStatus() {
            if (!audioPlaybackMonitor.isMonitoring) return;
            
            const bufferUnderrunRate = audioPlaybackMonitor.totalBufferChecks > 0 ? 
                (audioPlaybackMonitor.bufferUnderrunCount / audioPlaybackMonitor.totalBufferChecks) : 0;
            
            const recentGaps = audioPlaybackMonitor.playbackGaps.filter(
                g => performance.now() - g.time < 10000 // Last 10 seconds
            );
            
            const avgGap = recentGaps.length > 0 ? 
                recentGaps.reduce((sum, g) => sum + g.gap, 0) / recentGaps.length : 0;
            
            const statusElement = document.getElementById('audio-status');
            let statusText = '';
            let statusClass = '';
            
            // Determine status based on multiple factors
            if (bufferUnderrunRate > audioPlaybackMonitor.audioDropThreshold || 
                audioPlaybackMonitor.stallCount > 0 || 
                avgGap > 0.2) {
                statusText = `Playback: Poor (${(bufferUnderrunRate * 100).toFixed(1)}% drops, ${recentGaps.length} gaps)`;
                statusClass = 'bad';
                if (bufferUnderrunRate > audioPlaybackMonitor.audioDropThreshold) {
                    showToast(`High playback drop rate: ${(bufferUnderrunRate * 100).toFixed(1)}%`, 'warning');
                }
            } else if (bufferUnderrunRate > audioPlaybackMonitor.audioDropThreshold / 2 || avgGap > 0.1) {
                statusText = `Playback: Fair (${(bufferUnderrunRate * 100).toFixed(1)}% drops, ${recentGaps.length} gaps)`;
                statusClass = 'poor';
            } else {
                statusText = `Playback: Good (${(bufferUnderrunRate * 100).toFixed(1)}% drops)`;
                statusClass = 'good';
            }
            
            // Check for silence from server
            const currentTime = Date.now();
            const silenceDuration = currentTime - audioPlaybackMonitor.lastAudioActivity;
            if (silenceDuration > audioPlaybackMonitor.maxSilenceDuration) {
                statusText += ' - No server audio';
                statusClass = 'poor';
            }
            
            statusElement.textContent = statusText;
            statusElement.className = `audio-status ${statusClass}`;
        }
        
        function setupAudioVisualization(stream) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            audioSource = audioContext.createMediaStreamSource(stream);
            audioSource.connect(analyser);
            analyser.fftSize = 64;
            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            
            function updateAudioLevel() {
                analyser.getByteFrequencyData(dataArray);
                const average = Array.from(dataArray).reduce((a, b) => a + b, 0) / dataArray.length;
                audioLevel = average / 255;
                
                // Update CSS variable instead of rebuilding the button
                const pulseCircle = document.querySelector('.pulse-circle');
                if (pulseCircle) {
                    pulseCircle.style.setProperty('--audio-level', 1 + audioLevel);
                }
                animationFrame = requestAnimationFrame(updateAudioLevel);
            }
            updateAudioLevel();
        }
        
        function showError(message) {
            showToast(message, 'error');
        }
        
        function showToast(message, type = 'info') {
            const toast = document.getElementById('error-toast');
            toast.textContent = message;
            toast.className = `toast ${type}`;
            toast.style.display = 'block';

            // Hide toast after 5 seconds
            setTimeout(() => {
                toast.style.display = 'none';
            }, 5000);
        }

        // 音量控制函数
        function reduceVolume(targetVolume = 0.3) {
            const audioElement = document.getElementById('audio-output');
            if (audioElement) {
                // 保存当前音量作为原始音量（如果还没有保存的话）
                if (!volumeControl.isReduced) {
                    volumeControl.originalVolume = audioElement.volume;
                }

                // 设置新音量
                audioElement.volume = targetVolume;
                volumeControl.currentVolume = targetVolume;
                volumeControl.isReduced = true;

                console.log(`🔉 音量已降低到 ${Math.round(targetVolume * 100)}%`);
                showToast(`音量已降低到 ${Math.round(targetVolume * 100)}%`, 'info');
            }
        }

        function restoreVolume() {
            const audioElement = document.getElementById('audio-output');
            if (audioElement && volumeControl.isReduced) {
                // 恢复到原始音量
                audioElement.volume = volumeControl.originalVolume;
                volumeControl.currentVolume = volumeControl.originalVolume;
                volumeControl.isReduced = false;

                console.log(`🔊 音量已恢复到 ${Math.round(volumeControl.originalVolume * 100)}%`);
                showToast(`音量已恢复到 ${Math.round(volumeControl.originalVolume * 100)}%`, 'info');
            }
        }

        function handleVolumeControl(eventData) {
            console.log('处理音量控制事件:', eventData);

            if (!eventData.action) {
                console.warn('音量控制事件缺少action字段');
                return;
            }

            switch (eventData.action) {
                case 'reduce':
                    const targetVolume = eventData.volume || 0.3;
                    reduceVolume(targetVolume);
                    break;

                case 'restore':
                    restoreVolume();
                    break;

                default:
                    console.warn('未知的音量控制动作:', eventData.action);
            }
        }
        
        async function setupWebRTC() {
            // 优化音频约束
            const audioConstraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 24000,
                    channelCount: 1,
                    sampleSize: 16
                }
            };
            
            isConnecting = true;
            const config = __RTC_CONFIGURATION__;
            peerConnection = new RTCPeerConnection(config);
            
            const timeoutId = setTimeout(() => {
                showToast("Connection is taking longer than usual. Are you on a VPN?", 'warning');
            }, 5000);
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
                setupAudioVisualization(stream);
                
                stream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, stream);
                });
                
                peerConnection.addEventListener('track', (evt) => {
                    if (audioOutput.srcObject !== evt.streams[0]) {
                        audioOutput.srcObject = evt.streams[0];
                        audioOutput.play();
                        
                        // Start monitoring received audio playback
                        setTimeout(() => {
                            startAudioPlaybackMonitoring();
                        }, 1000); // Wait a bit for audio to start
                    }
                });
                
                // Monitor RTC connection stats
                const statsInterval = setInterval(async () => {
                    if (peerConnection && peerConnection.connectionState === 'connected') {
                        try {
                            const stats = await peerConnection.getStats();
                            stats.forEach((report) => {
                                if (report.type === 'inbound-rtp' && report.mediaType === 'audio') {
                                    // Monitor packet loss and jitter
                                    if (report.packetsLost > 0) {
                                        console.warn('Audio packets lost:', report.packetsLost);
                                    }
                                    if (report.jitter > 0.03) { // 30ms jitter threshold
                                        console.warn('High audio jitter detected:', report.jitter);
                                    }
                                }
                            });
                        } catch (e) {
                            console.error('Error getting RTC stats:', e);
                        }
                    } else {
                        clearInterval(statsInterval);
                    }
                }, 5000);
                
                const dataChannel = peerConnection.createDataChannel('text');
                dataChannel.onmessage = (event) => {
                    const eventJson = JSON.parse(event.data);
                    if (eventJson.type === "error") {
                        showError(eventJson.message);
                    }
                };
                
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                await new Promise((resolve) => {
                    if (peerConnection.iceGatheringState === "complete") {
                        resolve();
                    } else {
                        const checkState = () => {
                            if (peerConnection.iceGatheringState === "complete") {
                                peerConnection.removeEventListener("icegatheringstatechange", checkState);
                                resolve();
                            }
                        };
                        peerConnection.addEventListener("icegatheringstatechange", checkState);
                    }
                });
                
                peerConnection.addEventListener('connectionstatechange', () => {
                    console.log('connectionstatechange', peerConnection.connectionState);
                    if (peerConnection.connectionState === 'connected') {
                        clearTimeout(timeoutId);
                        const toast = document.getElementById('error-toast');
                        toast.style.display = 'none';
                        showToast('Connected - monitoring playback quality', 'info');
                    } else if (peerConnection.connectionState === 'disconnected' || 
                              peerConnection.connectionState === 'failed') {
                        showToast('Connection lost - playback monitoring stopped', 'error');
                        stopAudioPlaybackMonitoring();
                    }
                    updateButtonState();
                });
                
                webrtc_id = Math.random().toString(36).substring(7);
                const response = await fetch('/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: peerConnection.localDescription.sdp,
                        type: peerConnection.localDescription.type,
                        webrtc_id: webrtc_id
                    })
                });
                
                const serverResponse = await response.json();
                if (serverResponse.status === 'failed') {
                    showError(serverResponse.meta.error === 'concurrency_limit_reached'
                        ? `Too many connections. Maximum limit is ${serverResponse.meta.limit}`
                        : serverResponse.meta.error);
                    stop();
                    return;
                }
                
                await peerConnection.setRemoteDescription(serverResponse);
                
                const eventSource = new EventSource('/outputs?webrtc_id=' + webrtc_id);
                eventSource.addEventListener("output", (event) => {
                    const eventJson = JSON.parse(event.data);
                    console.log("eventJson", eventJson)

                    // Handle interruption event
                    if (eventJson.type === "[interruption]") {
                        console.log("Interruption received");
                        // Stop audio playback
                        if (audioOutput) {
                            audioOutput.pause();
                            audioOutput.currentTime = 0;
                        }
                        // Show interruption message
                        showToast(eventJson.content, "warning");
                        return;
                    } else if (eventJson.type === "[start]") {
                        console.log("start received");
                        audioOutput.play();
                        // Show interruption message
                        showToast(eventJson.content, "warning");
                        return;
                    } else if (eventJson.type === "search_results") {
                        console.log("Search results received", eventJson);
                        // 处理搜索结果显示 - 只在右边商品区域显示，不在聊天框显示
                        if (eventJson.raw_data && Array.isArray(eventJson.raw_data)) {
                            // 从消息内容中提取查询词
                            const queryMatch = eventJson.content.match(/「(.+?)」/);
                            const query = queryMatch ? queryMatch[1] : '搜索';
                            displayProducts(eventJson.raw_data, query);
                        }
                        // 不在对话框中显示搜索结果，只在右边显示
                        return;
                    } else if (eventJson.type === "product_control") {
                        console.log("Product control received", eventJson);
                        // 处理商品控制事件
                        handleProductControl(eventJson);
                        return;
                    } else if (eventJson.type === "volume_control") {
                        console.log("Volume control received", eventJson);
                        // 处理音量控制事件
                        handleVolumeControl(eventJson);
                        return;
                    }

                    var element = document.getElementById(eventJson.id);
                    if (element) {
                        return element.textContent += eventJson.content
                    } else {
                        addMessage("assistant", eventJson.content, eventJson.id);
                    }
                });

                // Add interruption event listener
                eventSource.addEventListener("interruption", (event) => {
                    console.log("Interruption received");
                    // Stop audio playback
                    if (audioOutput) {
                        audioOutput.pause();
                        audioOutput.currentTime = 0;
                    }
                    // Show interruption message
                    showToast("Conversation interrupted", "warning");
                });
                
            } catch (err) {
                clearTimeout(timeoutId);
                console.error('Error setting up WebRTC:', err);
                showError('Failed to establish connection. Please try again.');
                stop();
            }
        }
        
        function addMessage(role, content, id) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message', role);

            // 检查内容是否包含HTML标签
            if (content.includes('<div') || content.includes('<h3')) {
                messageDiv.innerHTML = content;
            } else {
                messageDiv.textContent = content;
            }

            messageDiv.id = id
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 商品轮播相关变量
        let currentProducts = [];
        let currentProductIndex = 0;

        function displayProducts(products, query) {
            if (!products || products.length === 0) {
                showNoProducts(query);
                return;
            }

            // 保存商品数据
            currentProducts = products;
            currentProductIndex = 0;

            // 更新标题
            const productHeader = document.querySelector('.product-header h3');
            productHeader.textContent = `🛍️ 「${query || '搜索'}」结果 (${products.length}个商品)`;

            // 隐藏无商品提示，显示商品轮播
            document.getElementById('no-products').style.display = 'none';
            document.getElementById('product-display').style.display = 'block';

            // 渲染当前商品和指示器
            renderCurrentProduct();
            renderIndicators();
        }

        function showNoProducts(query) {
            document.getElementById('no-products').style.display = 'block';
            document.getElementById('product-display').style.display = 'none';
            document.getElementById('no-products').innerHTML = `
                未找到「${query || ''}」相关商品<br>
                请尝试其他关键词
            `;
        }

        function renderCurrentProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            const product = currentProducts[currentProductIndex];

            // 适配不同的字段名
            const title = product.itemTitle || product.title || '商品标题';
            const price = product.itemPrice || product.price || '价格待询';
            const image = product.pic_url || product.image || '';
            const itemId = product.itemId || product.nid || product.id || '';
            const shopName = product.seller_name || product.shop_name || product.shop || '';
            const sales = product.transNum30d || product.sales || '';
            const bcType = product.bcType || '';

            // 清理价格格式
            let formattedPrice = price;
            if (typeof price === 'number') {
                formattedPrice = price.toFixed(2);
            } else if (typeof price === 'string') {
                formattedPrice = price.replace('¥', '').trim();
            }

            // 更新商品显示
            document.getElementById('product-image').src = image || '';
            document.getElementById('product-image').alt = title;
            document.getElementById('product-title').textContent = title;
            document.getElementById('product-price').textContent = formattedPrice;

            // 构建商品元信息
            let metaInfo = [];
            if (shopName) metaInfo.push(`🏪 ${shopName}`);
            if (sales) metaInfo.push(`📈 月销${sales}`);
            if (bcType) metaInfo.push(`🏷️ ${bcType}`);
            metaInfo.push(`ID: ${itemId}`);

            document.getElementById('product-meta').innerHTML = metaInfo.join('<br>');

            // 设置选择按钮的商品ID
            document.getElementById('select-button').setAttribute('data-item-id', itemId);

            // 更新导航按钮状态
            document.getElementById('prev-button').disabled = currentProductIndex === 0;
            document.getElementById('next-button').disabled = currentProductIndex === currentProducts.length - 1;

            console.log(`显示商品 ${currentProductIndex + 1}/${currentProducts.length}: ${title}`);
        }

        function renderIndicators() {
            const indicatorContainer = document.getElementById('product-indicator');
            indicatorContainer.innerHTML = '';

            if (!currentProducts || currentProducts.length <= 1) {
                return; // 只有一个商品时不显示指示器
            }

            for (let i = 0; i < currentProducts.length; i++) {
                const dot = document.createElement('div');
                dot.className = `indicator-dot ${i === currentProductIndex ? 'active' : ''}`;
                dot.onclick = () => goToProduct(i);
                indicatorContainer.appendChild(dot);
            }
        }

        function showNextProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            if (currentProductIndex < currentProducts.length - 1) {
                currentProductIndex++;
                renderCurrentProduct();
                renderIndicators();
                console.log('右滑到下一个商品');
            }
        }

        function showPreviousProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            if (currentProductIndex > 0) {
                currentProductIndex--;
                renderCurrentProduct();
                renderIndicators();
                console.log('左滑到上一个商品');
            }
        }

        function goToProduct(index) {
            if (!currentProducts || index < 0 || index >= currentProducts.length) return;

            currentProductIndex = index;
            renderCurrentProduct();
            renderIndicators();
            console.log(`跳转到商品 ${index + 1}`);
        }

        function selectCurrentProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            const product = currentProducts[currentProductIndex];
            const itemId = product.itemId || product.nid || product.id || '';
            const title = product.itemTitle || product.title || '商品标题';

            console.log(`选择商品: ${title} (ID: ${itemId})`);

            // 这里可以添加选择商品的逻辑，比如发送到后端
            showToast(`已选择商品: ${title}`, 'info');
        }

        function handleProductControl(eventData) {
            console.log('处理商品控制事件:', eventData);

            if (!eventData.action) {
                console.warn('商品控制事件缺少action字段');
                return;
            }

            // 如果后端提供了product_index，直接同步到前端
            if (typeof eventData.product_index === 'number' && currentProducts && currentProducts.length > 0) {
                if (eventData.product_index >= 0 && eventData.product_index < currentProducts.length) {
                    currentProductIndex = eventData.product_index;
                    console.log(`同步商品索引到: ${currentProductIndex}`);
                }
            }

            switch (eventData.action) {
                case 'prev':
                    // 语音控制左滑 - 直接更新显示，不再调用showPreviousProduct避免重复操作
                    if (currentProducts && currentProducts.length > 0) {
                        renderCurrentProduct();
                        renderIndicators();
                        showToast('已切换到上一个商品', 'info');
                        console.log(`语音左滑到商品 ${currentProductIndex + 1}/${currentProducts.length}`);
                    }
                    break;

                case 'next':
                    // 语音控制右滑 - 直接更新显示，不再调用showNextProduct避免重复操作
                    if (currentProducts && currentProducts.length > 0) {
                        renderCurrentProduct();
                        renderIndicators();
                        showToast('已切换到下一个商品', 'info');
                        console.log(`语音右滑到商品 ${currentProductIndex + 1}/${currentProducts.length}`);
                    }
                    break;

                case 'select':
                    // 语音控制选择商品
                    if (currentProducts && currentProducts.length > 0) {
                        const product = currentProducts[currentProductIndex];
                        const title = product.itemTitle || product.title || '商品标题';
                        showToast(`已选择商品: ${title}`, 'info');

                        // 可以在这里添加选择商品后的处理逻辑
                        console.log('语音选择商品:', product);
                    }
                    break;

                default:
                    console.warn('未知的商品控制动作:', eventData.action);
            }
        }
        
        function stop() {
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
            
            // Stop audio playback monitoring
            stopAudioPlaybackMonitoring();
            
            if (audioContext) {
                audioContext.close();
                audioContext = null;
                analyser = null;
                audioSource = null;
            }
            if (peerConnection) {
                if (peerConnection.getTransceivers) {
                    peerConnection.getTransceivers().forEach(transceiver => {
                        if (transceiver.stop) {
                            transceiver.stop();
                        }
                    });
                }
                if (peerConnection.getSenders) {
                    peerConnection.getSenders().forEach(sender => {
                        if (sender.track && sender.track.stop) sender.track.stop();
                    });
                }
                console.log('closing');
                peerConnection.close();
            }
            updateButtonState();
            audioLevel = 0;
        }
        
        startButton.addEventListener('click', () => {
            console.log('clicked');
            console.log(peerConnection, peerConnection?.connectionState);
            if (!peerConnection || peerConnection.connectionState !== 'connected') {
                setupWebRTC();
            } else {
                console.log('stopping');
                stop();
            }
        });
    </script>
</body>

</html>