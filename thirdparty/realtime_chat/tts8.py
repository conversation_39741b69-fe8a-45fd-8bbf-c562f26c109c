import asyncio
import json
import uuid
import aiofiles
import websockets
import time
import fastrand
from websockets.asyncio.client import ClientConnection
import ssl
import os
import numpy as np
from typing import Optional as TypingOptional, Tuple, Callable, List
import logging
import re
from dataclasses import dataclass
from enum import Enum
from threading import Lock


class TriggerStrategy(Enum):
    """触发策略枚举"""
    PUNCTUATION = "punctuation"  # 遇到标点符号时触发
    WORD_COUNT = "word_count"    # 达到指定词数时触发
    TIME_BASED = "time_based"    # 基于时间间隔触发
    HYBRID = "hybrid"            # 混合策略


@dataclass
class StreamConfig:
    """流式处理配置"""
    trigger_strategy: TriggerStrategy = TriggerStrategy.HYBRID
    max_words_per_chunk: int = 20  # 每个块的最大词数
    timeout_seconds: float = 2.0   # 超时时间（秒）
    punctuation_marks: str = "。！？；，、"  # 中文标点符号
    min_words_for_punctuation: int = 3  # 遇到标点符号时的最小词数


class ByteDanceTTSStreaming:
    """字节跳动TTS流式客户端"""
    
    # 协议常量
    PROTOCOL_VERSION = 0b0001
    DEFAULT_HEADER_SIZE = 0b0001

    # Message Type
    FULL_CLIENT_REQUEST = 0b0001
    AUDIO_ONLY_RESPONSE = 0b1011
    FULL_SERVER_RESPONSE = 0b1001
    ERROR_INFORMATION = 0b1111

    # Message Type Specific Flags
    MSG_TYPE_FLAG_NO_SEQ = 0b0000
    MSG_TYPE_FLAG_POSITIVE_SEQ = 0b1
    MSG_TYPE_FLAG_LAST_NO_SEQ = 0b10
    MSG_TYPE_FLAG_NEGATIVE_SEQ = 0b11
    MSG_TYPE_FLAG_WITH_EVENT = 0b100

    # Message Serialization
    NO_SERIALIZATION = 0b0000
    JSON = 0b0001

    # Message Compression
    COMPRESSION_NO = 0b0000
    COMPRESSION_GZIP = 0b0001

    # 事件常量
    EVENT_NONE = 0
    EVENT_START_CONNECTION = 1
    EVENT_FINISH_CONNECTION = 2
    EVENT_CONNECTION_STARTED = 50
    EVENT_CONNECTION_FAILED = 51
    EVENT_CONNECTION_FINISHED = 52
    EVENT_START_SESSION = 100
    EVENT_FINISH_SESSION = 102
    EVENT_SESSION_STARTED = 150
    EVENT_SESSION_FINISHED = 152
    EVENT_SESSION_FAILED = 153
    EVENT_TASK_REQUEST = 200
    EVENT_TTS_SENTENCE_START = 350
    EVENT_TTS_SENTENCE_END = 351
    EVENT_TTS_RESPONSE = 352

    class Header:
        """消息头部类"""
        def __init__(self,
                     protocol_version=None,
                     header_size=None,
                     message_type: int = 0,
                     message_type_specific_flags: int = 0,
                     serial_method: int = None,
                     compression_type: int = None,
                     reserved_data=0):
            self.header_size = header_size or ByteDanceTTSStreaming.DEFAULT_HEADER_SIZE
            self.protocol_version = protocol_version or ByteDanceTTSStreaming.PROTOCOL_VERSION
            self.message_type = message_type
            self.message_type_specific_flags = message_type_specific_flags
            self.serial_method = serial_method or ByteDanceTTSStreaming.NO_SERIALIZATION
            self.compression_type = compression_type or ByteDanceTTSStreaming.COMPRESSION_NO
            self.reserved_data = reserved_data

        def as_bytes(self) -> bytes:
            return bytes([
                (self.protocol_version << 4) | self.header_size,
                (self.message_type << 4) | self.message_type_specific_flags,
                (self.serial_method << 4) | self.compression_type,
                self.reserved_data
            ])

    class Optional:
        """可选参数类"""
        def __init__(self, event: int = None, sessionId: str = None, sequence: int = None):
            self.event = event or ByteDanceTTSStreaming.EVENT_NONE
            self.sessionId = sessionId
            self.errorCode: int = 0
            self.connectionId: TypingOptional[str] = None
            self.response_meta_json: TypingOptional[str] = None
            self.sequence = sequence

        def as_bytes(self) -> bytes:
            option_bytes = bytearray()
            if self.event != ByteDanceTTSStreaming.EVENT_NONE:
                option_bytes.extend(self.event.to_bytes(4, "big", signed=True))
            if self.sessionId is not None:
                session_id_bytes = str.encode(self.sessionId)
                size = len(session_id_bytes).to_bytes(4, "big", signed=True)
                option_bytes.extend(size)
                option_bytes.extend(session_id_bytes)
            if self.sequence is not None:
                option_bytes.extend(self.sequence.to_bytes(4, "big", signed=True))
            return option_bytes

    class Response:
        """响应类"""
        def __init__(self, header: 'ByteDanceTTSStreaming.Header', optional: 'ByteDanceTTSStreaming.Optional'):
            self.optional = optional
            self.header = header
            self.payload: TypingOptional[bytes] = None
            self.payload_json: TypingOptional[str] = None

    def __init__(self, app_id: str, token: str, 
                 speaker: str = 'zh_female_shuangkuaisisi_moon_bigtts',
                 audio_format: str = 'mp3',
                 audio_sample_rate: int = 24000):
        """
        初始化TTS客户端
        
        Args:
            app_id: 应用ID
            token: 访问令牌
            speaker: 说话人
            audio_format: 音频格式 ('mp3' 或 'pcm')
            audio_sample_rate: 音频采样率
        """
        self.app_id = app_id
        self.token = token
        self.speaker = speaker
        self.audio_format = audio_format
        self.audio_sample_rate = audio_sample_rate
        self.url = 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'
        self.ws: TypingOptional[ClientConnection] = None
        self.session_id: TypingOptional[str] = None
        
        # 新增：用于追踪活动的任务和连接
        self._active_workers: List[asyncio.Task] = []
        self._active_connections: List[ClientConnection] = []
        self._text_queue: TypingOptional[asyncio.Queue] = None
        self._word_stream: TypingOptional[asyncio.Queue] = None
        self._voice_queue: TypingOptional[asyncio.Queue] = None
        self._stream_processor: TypingOptional['ByteDanceTTSStreaming.StreamTextProcessor'] = None
        self._stop_event: TypingOptional[asyncio.Event] = None
        self._processing_lock = Lock()
        self._is_processing = False
        self._active_sessions = 0
        self._pending_texts = 0

    def can_interrupt(self) -> bool:
        """
        判断是否有正在转换或者等待转换的任务
        
        Returns:
            bool: 如果有活动的任务返回True，否则返回False
        """
        with self._processing_lock:
            
            # # 检查是否有活动的工作器
            # if self._active_workers:
            #     active_count = sum(1 for worker in self._active_workers if not worker.done())
            #     if active_count > 0:
            #         return True
            
            # 检查队列中是否有待处理的文本
            if self._text_queue and not self._text_queue.empty():
                return True
            
            # 检查流处理器缓冲区是否有数据
            if self._stream_processor and len(self._stream_processor.word_buffer) > 0:
                return True
            
            # 检查是否有活动的会话
            # if self._active_sessions > 0:
            #     return True
            
            # 检查是否有待处理的文本数量
            if self._pending_texts > 0:
                return True
            
            return False

    async def interrupt(self):
        """
        打断当前转换的任务并清空数据
        """
        logging.info("Interrupting TTS processing...")
        
        with self._processing_lock:
            self._is_processing = False
        
        # 设置停止事件
        # if self._stop_event:
        self._stop_event.set()
        
        # 清空所有队列
        await self._clear_queues()
        
        # 取消所有活动的工作器任务
        await self._cancel_workers()
        
        # 关闭所有活动的WebSocket连接
        await self._close_connections()
        
        # 重置流处理器
        if self._stream_processor:
            self._stream_processor.word_buffer.clear()
            self._stream_processor = None
        
        # 重置计数器
        self._active_sessions = 0
        self._pending_texts = 0
        
        logging.info("TTS processing interrupted and cleared")

    async def _clear_queues(self):
        """清空所有队列"""
        # 清空文本队列
        if self._text_queue:
            while not self._text_queue.empty():
                try:
                    self._text_queue.get_nowait()
                    self._text_queue.task_done()
                except asyncio.QueueEmpty:
                    break
        
        # 清空单词流队列
        if self._word_stream:
            while not self._word_stream.empty():
                try:
                    self._word_stream.get_nowait()
                    self._word_stream.task_done()
                except asyncio.QueueEmpty:
                    break
        
        # 清空语音队列
        if self._voice_queue:
            while not self._voice_queue.empty():
                try:
                    self._voice_queue.get_nowait()
                    self._voice_queue.task_done()
                except asyncio.QueueEmpty:
                    break

    async def _cancel_workers(self):
        """取消所有活动的工作器任务"""
        if self._active_workers:
            for worker in self._active_workers:
                if not worker.done():
                    worker.cancel()
            
            # 等待所有任务完成或被取消
            await asyncio.gather(*self._active_workers, return_exceptions=True)
            
            # 清空工作器列表
            self._active_workers.clear()
    async def resume(self, text_queue, voice_queue) -> bool:
        """
        恢复被中断的TTS处理
        
        Returns:
            bool: 如果成功恢复返回True，否则返回False
        """
        if not self._stop_event.is_set():
            return None
        with self._processing_lock:
            # 检查是否处于中断状态
            self._is_interrupted = False
            self._is_processing = True
            
            # 创建新的停止事件
            self._stop_event.clear()
            # asyncio.create_task(
            #     self.run_tts_worker(
            #         self._text_queue, 
            #         self._voice_queue, 
            #         1, 
            #         self._stop_event
            #     )
            # )

        processer_task = asyncio.create_task(self.run_stream_tts_processor(
            word_stream=text_queue,
            voice_queue=voice_queue,
            config=self._config,
            max_workers=self._max_workers,
            # on_audio_callback=audio_callback
        ))
        return processer_task
            
            
    async def _close_connections(self):
        """关闭所有活动的WebSocket连接"""
        if self._active_connections:
            for conn in self._active_connections:
                if conn and not conn.closed:
                    try:
                        await conn.close()
                    except Exception as e:
                        logging.error(f"Error closing connection: {e}")
            
            # 清空连接列表
            self._active_connections.clear()
        
        # 关闭主连接
        if self.ws and not self.ws.closed:
            try:
                await self.ws.close()
            except Exception as e:
                logging.error(f"Error closing main connection: {e}")
            self.ws = None

    @staticmethod
    def gen_log_id():
        """生成日志ID"""
        ts = int(time.time() * 1000)
        r = fastrand.pcg32bounded(1 << 24) + (1 << 20)
        local_ip = "00000000000000000000000000000000"
        return f"02{ts}{local_ip}{r:08x}"

    async def _send_event(self, header: bytes, optional: TypingOptional[bytes] = None,
                         payload: TypingOptional[bytes] = None):
        """发送事件"""
        full_client_request = bytearray(header)
        if optional is not None:
            full_client_request.extend(optional)
        if payload is not None:
            payload_size = len(payload).to_bytes(4, 'big', signed=True)
            full_client_request.extend(payload_size)
            full_client_request.extend(payload)
        await self.ws.send(full_client_request)

    @staticmethod
    def _read_res_content(res: bytes, offset: int):
        """读取响应内容"""
        content_size = int.from_bytes(res[offset: offset + 4])
        offset += 4
        content = str(res[offset: offset + content_size], encoding='utf8')
        offset += content_size
        return content, offset

    @staticmethod
    def _read_res_payload(res: bytes, offset: int):
        """读取负载"""
        payload_size = int.from_bytes(res[offset: offset + 4])
        offset += 4
        payload = res[offset: offset + payload_size]
        offset += payload_size
        return payload, offset

    def _parse_response(self, res) -> Response:
        """解析响应"""
        if isinstance(res, str):
            raise RuntimeError(res)
        
        response = self.Response(self.Header(), self.Optional())
        header = response.header
        num = 0b00001111
        
        # 解析header
        header.protocol_version = res[0] >> 4 & num
        header.header_size = res[0] & 0x0f
        header.message_type = (res[1] >> 4) & num
        header.message_type_specific_flags = res[1] & 0x0f
        header.serialization_method = res[2] >> num
        header.message_compression = res[2] & 0x0f
        header.reserved = res[3]
        
        offset = 4
        optional = response.optional
        
        if header.message_type == self.FULL_SERVER_RESPONSE or self.AUDIO_ONLY_RESPONSE:
            if header.message_type_specific_flags == self.MSG_TYPE_FLAG_WITH_EVENT:
                optional.event = int.from_bytes(res[offset:8])
                offset += 4
                
                if optional.event == self.EVENT_NONE:
                    return response
                elif optional.event == self.EVENT_CONNECTION_STARTED:
                    optional.connectionId, offset = self._read_res_content(res, offset)
                elif optional.event == self.EVENT_CONNECTION_FAILED:
                    optional.response_meta_json, offset = self._read_res_content(res, offset)
                elif optional.event in [self.EVENT_SESSION_STARTED, self.EVENT_SESSION_FAILED, 
                                      self.EVENT_SESSION_FINISHED]:
                    optional.sessionId, offset = self._read_res_content(res, offset)
                    optional.response_meta_json, offset = self._read_res_content(res, offset)
                elif optional.event == self.EVENT_TTS_RESPONSE:
                    optional.sessionId, offset = self._read_res_content(res, offset)
                    response.payload, offset = self._read_res_payload(res, offset)
                elif optional.event in [self.EVENT_TTS_SENTENCE_END, self.EVENT_TTS_SENTENCE_START]:
                    optional.sessionId, offset = self._read_res_content(res, offset)
                    response.payload_json, offset = self._read_res_content(res, offset)

        elif header.message_type == self.ERROR_INFORMATION:
            optional.errorCode = int.from_bytes(res[offset:offset + 4], "big", signed=True)
            offset += 4
            response.payload, offset = self._read_res_payload(res, offset)
            
        return response

    def _get_payload_bytes(self, uid='1234', event=None, text=''):
        """生成请求负载"""
        event = event or self.EVENT_NONE
        return str.encode(json.dumps({
            "user": {"uid": uid},
            "event": event,
            "namespace": "BidirectionalTTS",
            "req_params": {
                "text": text,
                "speaker": self.speaker,
                "audio_params": {
                    "format": self.audio_format,
                    "sample_rate": self.audio_sample_rate,
                    "enable_timestamp": True,
                }
            }
        }))

    async def connect(self):
        """建立连接"""
        log_id = self.gen_log_id()
        
        ws_header = {
            "X-Api-App-Key": self.app_id,
            "X-Api-Access-Key": self.token,
            "X-Api-Resource-Id": 'volc.service_type.10029',
            "X-Api-Connect-Id": uuid.uuid4(),
            "X-Tt-Logid": log_id,
        }
        
        print(f"logID: {log_id}")
        print(f"Audio format: {self.audio_format}")
        
        self.ws = await websockets.connect(
            self.url,
            additional_headers=ws_header,
            ssl=ssl.create_default_context()
        )

    async def start_connection(self):
        """开始连接"""
        header = self.Header(
            message_type=self.FULL_CLIENT_REQUEST,
            message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT
        ).as_bytes()
        optional = self.Optional(event=self.EVENT_START_CONNECTION).as_bytes()
        payload = str.encode("{}")
        await self._send_event(header, optional, payload)

    async def start_session(self):
        """开始会话"""
        self.session_id = uuid.uuid4().__str__().replace('-', '')
        
        header = self.Header(
            message_type=self.FULL_CLIENT_REQUEST,
            message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=self.JSON
        ).as_bytes()
        optional = self.Optional(event=self.EVENT_START_SESSION, sessionId=self.session_id).as_bytes()
        payload = self._get_payload_bytes(event=self.EVENT_START_SESSION)
        await self._send_event(header, optional, payload)

    async def send_text(self, text: str):
        """发送文本进行TTS"""
        header = self.Header(
            message_type=self.FULL_CLIENT_REQUEST,
            message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=self.JSON
        ).as_bytes()
        optional = self.Optional(event=self.EVENT_TASK_REQUEST, sessionId=self.session_id).as_bytes()
        payload = self._get_payload_bytes(event=self.EVENT_TASK_REQUEST, text=text)
        await self._send_event(header, optional, payload)

    async def finish_session(self):
        """结束会话"""
        header = self.Header(
            message_type=self.FULL_CLIENT_REQUEST,
            message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=self.JSON
        ).as_bytes()
        optional = self.Optional(event=self.EVENT_FINISH_SESSION, sessionId=self.session_id).as_bytes()
        payload = str.encode('{}')
        await self._send_event(header, optional, payload)

    async def finish_connection(self):
        """结束连接"""
        header = self.Header(
            message_type=self.FULL_CLIENT_REQUEST,
            message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=self.JSON
        ).as_bytes()
        optional = self.Optional(event=self.EVENT_FINISH_CONNECTION).as_bytes()
        payload = str.encode('{}')
        await self._send_event(header, optional, payload)

    def print_response(self, res, tag: str):
        """打印响应信息"""
        print(f'===>{tag} header:{res.header.__dict__}')
        print(f'===>{tag} optional:{res.optional.__dict__}')
        print(f'===>{tag} payload len:{0 if res.payload is None else len(res.payload)}')
        print(f'===>{tag} payload_json:{res.payload_json}')

    # ======= 流式文本处理功能 =======
    
    class StreamTextProcessor:
        """流式文本处理器"""
        
        def __init__(self, config: StreamConfig, text_queue: asyncio.Queue):
            self.config = config
            self.text_queue = text_queue
            self.word_buffer = []
            self.last_word_time = time.time()
            self.chunk_id = 0
            
        def should_trigger(self, word: str) -> bool:
            """判断是否应该触发TTS处理"""
            current_time = time.time()
            time_elapsed = current_time - self.last_word_time
            
            if self.config.trigger_strategy == TriggerStrategy.WORD_COUNT:
                return len(self.word_buffer) >= self.config.max_words_per_chunk
                
            elif self.config.trigger_strategy == TriggerStrategy.TIME_BASED:
                return time_elapsed >= self.config.timeout_seconds
                
            elif self.config.trigger_strategy == TriggerStrategy.PUNCTUATION:
                has_punctuation = any(p in word for p in self.config.punctuation_marks)
                return (has_punctuation and 
                       len(self.word_buffer) >= self.config.min_words_for_punctuation)
                
            elif self.config.trigger_strategy == TriggerStrategy.HYBRID:
                # 混合策略：标点符号优先，然后是词数限制，最后是超时
                has_punctuation = any(p in word for p in self.config.punctuation_marks)
                if (has_punctuation and 
                    len(self.word_buffer) >= self.config.min_words_for_punctuation):
                    return True
                if len(self.word_buffer) >= self.config.max_words_per_chunk:
                    return True
                if time_elapsed >= self.config.timeout_seconds and len(self.word_buffer) > 0:
                    return True
                    
            return False
            
        async def add_word(self, word: str):
            """添加单词到缓冲区"""
            if not word.strip():
                return
                
            self.word_buffer.append(word.strip())
            self.last_word_time = time.time()
            
            if self.should_trigger(word):
                await self.flush_buffer()
                
        async def flush_buffer(self):
            """刷新缓冲区，将累积的文本发送到队列"""
            if not self.word_buffer:
                return
                
            text = ' '.join(self.word_buffer)
            self.chunk_id += 1
            
            logging.info(f"Flushing text chunk {self.chunk_id}: '{text}'")
            await self.text_queue.put(text)
            
            self.word_buffer.clear()
            self.last_word_time = time.time()
            
        async def finalize(self):
            """完成处理，刷新剩余的缓冲区内容"""
            await self.flush_buffer()
            # 发送结束信号
            await self.text_queue.put(None)

    async def run_stream_tts_processor(self, 
                                     word_stream: asyncio.Queue,
                                     voice_queue: asyncio.Queue,
                                     config: StreamConfig = None,
                                     max_workers: int = 2,
                                     on_audio_callback: Callable[[int, np.ndarray], None] = None):
        """
        运行流式TTS处理器
        
        Args:
            word_stream: 输入的单词流队列
            voice_queue: 输出的语音队列
            config: 流式处理配置
            max_workers: TTS工作器数量
            on_audio_callback: 音频数据回调函数
        """
        if config is None:
            config = StreamConfig()
        
        with self._processing_lock:
            self._is_processing = True
            
        # 保存队列引用
        self._word_stream = word_stream
        self._voice_queue = voice_queue
        self._config = config
        self._max_workers = max_workers
            
        # 创建内部文本队列
        text_queue = asyncio.Queue()
        self._text_queue = text_queue
        
        # 创建停止事件
        stop_event = asyncio.Event()
        self._stop_event = stop_event
        
        # 创建流式文本处理器
        stream_processor = self.StreamTextProcessor(config, text_queue)
        self._stream_processor = stream_processor
        
        # 启动TTS工作器
        tts_task = asyncio.create_task(
            self.run_tts_worker(text_queue, voice_queue, max_workers, stop_event)
        )
        print("tts_task running")
        # 启动音频处理任务（如果有回调函数）
        audio_task = None
        if on_audio_callback:
            audio_task = asyncio.create_task(
                self._process_audio_output(voice_queue, on_audio_callback, stop_event)
            )
        
        try:
            # 处理单词流
            timeout_task = None
            while True:
                if stop_event.is_set():
                    break
                    
                try:
                    # 设置一个周期性检查超时的任务
                    if timeout_task is None or timeout_task.done():
                        timeout_task = asyncio.create_task(
                            self._check_timeout(stream_processor, config.timeout_seconds)
                        )
                    
                    # 等待单词或超时检查
                    done, pending = await asyncio.wait(
                        [asyncio.create_task(word_stream.get()), timeout_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    # 取消未完成的任务
                    for task in pending:
                        task.cancel()
                    
                    # 处理完成的任务
                    for task in done:
                        if task == timeout_task:
                            # 超时检查触发
                            continue
                        else:
                            # 收到新单词
                            word = await task
                            if word is None:  # 结束信号
                                logging.info("Received end signal from word stream")
                                await stream_processor.finalize()
                                break
                            
                            await stream_processor.add_word(word)
                            word_stream.task_done()
                            
                except Exception as e:
                    logging.error(f"Error processing word stream: {e}")
                    break
            
            # 等待所有文本处理完成
            await text_queue.join()
            
        finally:
            # 清理资源
            stop_event.set()
            timeout_task.cancel()
            
            # await tts_task
            # if audio_task:
            #     await audio_task
            
            # with self._processing_lock:
            #     self._is_processing = False
            #     self._stream_processor = None
            #     self._text_queue = None
            #     self._word_stream = None
            #     self._voice_queue = None
            #     self._stop_event = None
                
        logging.info("Stream TTS processor completed")
        
    async def _check_timeout(self, stream_processor, timeout_seconds: float):
        """检查超时并刷新缓冲区"""
        while True:
            if self._stop_event and self._stop_event.is_set():
                break
            await asyncio.sleep(timeout_seconds / 2)  # 检查频率为超时时间的一半
            current_time = time.time()
            if (current_time - stream_processor.last_word_time >= timeout_seconds and 
                len(stream_processor.word_buffer) > 0):
                await stream_processor.flush_buffer()
                
    async def _process_audio_output(self, voice_queue: asyncio.Queue, 
                                  callback: Callable[[int, np.ndarray], None],
                                  stop_event: asyncio.Event):
        """处理音频输出"""
        while not stop_event.is_set():
            try:
                sample_rate, pcm_array = await asyncio.wait_for(voice_queue.get(), timeout=1.0)
                callback(sample_rate, pcm_array)
                voice_queue.task_done()
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logging.error(f"Error in audio output processing: {e}")
                break

    async def text_to_speech(self, text: str, output_path: str):
        """
        文本转语音主流程
        
        Args:
            text: 要转换的文本
            output_path: 输出文件路径
        """
        with self._processing_lock:
            self._is_processing = True
            self._pending_texts = 1
            
        try:
            # 建立连接
            await self.connect()
            
            # 开始连接
            await self.start_connection()
            res = self._parse_response(await self.ws.recv())
            self.print_response(res, 'start_connection response:')
            if res.optional.event != self.EVENT_CONNECTION_STARTED:
                raise RuntimeError("start connection failed")

            # 开始会话
            await self.start_session()
            self._active_sessions += 1
            res = self._parse_response(await self.ws.recv())
            self.print_response(res, 'start_session response:')
            if res.optional.event != self.EVENT_SESSION_STARTED:
                raise RuntimeError('start session failed!')

            # 发送文本
            await self.send_text(text)
            await self.finish_session()
            self._active_sessions -= 1
            
            # 接收音频数据并保存
            async with aiofiles.open(output_path, mode="wb") as output_file:
                while True:
                    res = self._parse_response(await self.ws.recv())
                    self.print_response(res, 'send_text response:')
                    
                    if (res.optional.event == self.EVENT_TTS_RESPONSE and 
                        res.header.message_type == self.AUDIO_ONLY_RESPONSE):
                        await output_file.write(res.payload)
                    elif res.optional.event in [self.EVENT_TTS_SENTENCE_START, self.EVENT_TTS_SENTENCE_END]:
                        continue
                    else:
                        break
            
            # 结束连接
            await self.finish_connection()
            res = self._parse_response(await self.ws.recv())
            self.print_response(res, 'finish_connection response:')
            print('===> TTS转换完成')
            
        finally:
            if self.ws:
                await self.ws.close()
            with self._processing_lock:
                self._is_processing = False
                self._pending_texts = 0

    async def run_tts_worker(self, text_queue: asyncio.Queue, voice_queue: asyncio.Queue, 
                             max_workers: int = 3, stop_event: TypingOptional[asyncio.Event] = None):
        """
        运行TTS工作器，从文本队列读取文本，将音频数据放入语音队列
        
        Args:
            text_queue: 输入文本队列
            voice_queue: 输出语音队列，格式为 (sample_rate, pcm_np_array)
            max_workers: 最大并发工作器数量
            stop_event: 停止事件，用于优雅停止工作器
        """
        if stop_event is None:
            stop_event = asyncio.Event()
        
        # 创建工作器任务
        workers = []
        for i in range(max_workers):
            worker = asyncio.create_task(
                self._tts_worker(text_queue, voice_queue, stop_event, worker_id=i)
            )
            workers.append(worker)
            self._active_workers.append(worker)
        
        try:
            # 等待所有工作器完成
            await asyncio.gather(*workers, return_exceptions=True)
        except Exception as e:
            logging.error(f"TTS workers error: {e}")
            # 停止所有工作器
            stop_event.set()
            for worker in workers:
                if not worker.done():
                    worker.cancel()
            await asyncio.gather(*workers, return_exceptions=True)

    async def _tts_worker(self, text_queue: asyncio.Queue, voice_queue: asyncio.Queue, 
                         stop_event: asyncio.Event, worker_id: int):
        """
        单个TTS工作器
        
        Args:
            text_queue: 输入文本队列
            voice_queue: 输出语音队列
            stop_event: 停止事件
            worker_id: 工作器ID
        """
        worker_name = f"TTS-Worker-{worker_id}"
        logging.info(f"{worker_name} started")
        
        # 为每个工作器创建独立的连接
        worker_ws = None
        
        try:
            # 建立连接
            worker_ws = await self._create_worker_connection()
            self._active_connections.append(worker_ws)
            
            # 开始连接
            await self._worker_start_connection(worker_ws)
            res = self._parse_response(await worker_ws.recv())
            if res.optional.event != self.EVENT_CONNECTION_STARTED:
                raise RuntimeError(f"{worker_name}: start connection failed")
            logging.info(f"{worker_name}: connection established")

            while not stop_event.is_set():
                try:
                    # 从队列获取文本，设置超时避免无限等待
                    text = await asyncio.wait_for(text_queue.get(), timeout=1.0)
                    
                    if text is None:  # 结束信号
                        logging.info(f"{worker_name}: received stop signal")
                        break
                    
                    self._pending_texts += 1
                    logging.info(f"{worker_name}: processing text: {text[:50]}...")
                    
                    # 处理文本转语音
                    await self._worker_process_text(worker_ws, text, voice_queue, worker_name)
                    self._pending_texts -= 1
                    
                    # 标记任务完成
                    text_queue.task_done()
                    
                except asyncio.TimeoutError:
                    # 超时继续循环，检查停止事件
                    continue
                except Exception as e:
                    logging.error(f"{worker_name}: error processing text: {e}")
                    text_queue.task_done()
                    self._pending_texts = max(0, self._pending_texts - 1)
                    continue
                    
        except Exception as e:
            logging.error(f"{worker_name}: worker error: {e}")
        finally:
            # 清理连接
            if worker_ws:
                try:
                    await self._worker_finish_connection(worker_ws)
                    await worker_ws.close()
                    if worker_ws in self._active_connections:
                        self._active_connections.remove(worker_ws)
                except:
                    pass
            logging.info(f"{worker_name}: stopped")

    async def _create_worker_connection(self):
        """为工作器创建独立的WebSocket连接"""
        log_id = self.gen_log_id()
        
        ws_header = {
            "X-Api-App-Key": self.app_id,
            "X-Api-Access-Key": self.token,
            "X-Api-Resource-Id": 'volc.service_type.10029',
            "X-Api-Connect-Id": uuid.uuid4(),
            "X-Tt-Logid": log_id,
        }
        
        return await websockets.connect(
            self.url,
            additional_headers=ws_header,
            ssl=ssl.create_default_context()
        )

    async def _worker_start_connection(self, ws):
        """工作器开始连接"""
        header = self.Header(
            message_type=self.FULL_CLIENT_REQUEST,
            message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT
        ).as_bytes()
        optional = self.Optional(event=self.EVENT_START_CONNECTION).as_bytes()
        payload = str.encode("{}")
        await self._send_event_to_ws(ws, header, optional, payload)

    async def _worker_finish_connection(self, ws):
        """工作器结束连接"""
        try:
            header = self.Header(
                message_type=self.FULL_CLIENT_REQUEST,
                message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
                serial_method=self.JSON
            ).as_bytes()
            optional = self.Optional(event=self.EVENT_FINISH_CONNECTION).as_bytes()
            payload = str.encode('{}')
            await self._send_event_to_ws(ws, header, optional, payload)
            
            # 等待响应
            res = self._parse_response(await asyncio.wait_for(ws.recv(), timeout=5.0))
        except:
            pass

    async def _worker_process_text(self, ws, text: str, voice_queue: asyncio.Queue, worker_name: str):
        """工作器处理单个文本"""
        session_id = uuid.uuid4().__str__().replace('-', '')
        
        try:
            self._active_sessions += 1
            # 开始会话
            header = self.Header(
                message_type=self.FULL_CLIENT_REQUEST,
                message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
                serial_method=self.JSON
            ).as_bytes()
            optional = self.Optional(event=self.EVENT_START_SESSION, sessionId=session_id).as_bytes()
            payload = self._get_payload_bytes(event=self.EVENT_START_SESSION)
            await self._send_event_to_ws(ws, header, optional, payload)
            
            res = self._parse_response(await ws.recv())
            if res.optional.event != self.EVENT_SESSION_STARTED:
                raise RuntimeError(f'{worker_name}: start session failed!')

            # 发送文本
            header = self.Header(
                message_type=self.FULL_CLIENT_REQUEST,
                message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
                serial_method=self.JSON
            ).as_bytes()
            optional = self.Optional(event=self.EVENT_TASK_REQUEST, sessionId=session_id).as_bytes()
            payload = self._get_payload_bytes(event=self.EVENT_TASK_REQUEST, text=text)
            await self._send_event_to_ws(ws, header, optional, payload)

            # 结束会话
            header = self.Header(
                message_type=self.FULL_CLIENT_REQUEST,
                message_type_specific_flags=self.MSG_TYPE_FLAG_WITH_EVENT,
                serial_method=self.JSON
            ).as_bytes()
            optional = self.Optional(event=self.EVENT_FINISH_SESSION, sessionId=session_id).as_bytes()
            payload = str.encode('{}')
            await self._send_event_to_ws(ws, header, optional, payload)
            
            # 接收音频数据
            while True:
                if self._stop_event and self._stop_event.is_set():
                    break
                    
                res = self._parse_response(await ws.recv())
                
                if (res.optional.event == self.EVENT_TTS_RESPONSE and 
                    res.header.message_type == self.AUDIO_ONLY_RESPONSE):
                    if res.payload:
                        pcm_data = np.frombuffer(res.payload, dtype=np.int16)
                        await voice_queue.put((self.audio_sample_rate, pcm_data[None,:]))
                elif res.optional.event in [self.EVENT_TTS_SENTENCE_START, self.EVENT_TTS_SENTENCE_END]:
                    continue
                else:
                    break
        
        except Exception as e:
            logging.error(f"{worker_name}: error in process_text: {e}")
        finally:
            self._active_sessions = max(0, self._active_sessions - 1)

    async def _send_event_to_ws(self, ws, header: bytes, optional: TypingOptional[bytes] = None,
                               payload: TypingOptional[bytes] = None):
        """向指定WebSocket发送事件"""
        full_client_request = bytearray(header)
        if optional is not None:
            full_client_request.extend(optional)
        if payload is not None:
            payload_size = len(payload).to_bytes(4, 'big', signed=True)
            full_client_request.extend(payload_size)
            full_client_request.extend(payload)
        await ws.send(full_client_request)


# ======= 使用示例 =======

async def stream_tts_with_interrupt_example():
    """带中断功能的流式TTS处理示例"""
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 从环境变量获取配置
    app_id = os.getenv('appId')
    token = os.getenv('token')
    
    if not app_id or not token:
        print("请设置环境变量 appId 和 token")
        return
    
    # 创建TTS客户端
    tts_client = ByteDanceTTSStreaming(
        app_id=app_id,
        token=token,
        speaker='zh_female_shuangkuaisisi_moon_bigtts',
        audio_format='pcm'
    )
    
    # 配置流式处理参数
    stream_config = StreamConfig(
        trigger_strategy=TriggerStrategy.HYBRID,
        max_words_per_chunk=15,
        timeout_seconds=3.0,
        min_words_for_punctuation=5
    )
    
    # 创建队列
    word_stream = asyncio.Queue()
    voice_queue = asyncio.Queue()
    
    # 音频数据处理回调
    audio_files = []
    def audio_callback(sample_rate: int, pcm_array: np.ndarray):
        file_index = len(audio_files)
        filename = f"stream_output_{file_index}.pcm"
        with open(filename, 'wb') as f:
            f.write(pcm_array.tobytes())
        audio_files.append(filename)
        print(f"Saved audio chunk to {filename}, length: {len(pcm_array)}")
    
    # 启动流式TTS处理器
    tts_task = asyncio.create_task(
        tts_client.run_stream_tts_processor(
            word_stream=word_stream,
            voice_queue=voice_queue,
            config=stream_config,
            max_workers=2,
            on_audio_callback=audio_callback
        )
    )
    
    # 模拟流式输入
    test_words = [
        "明朝", "开国", "皇帝", "朱元璋", "也", "称", "这本", "书", "为", "万物", "之", "根", "。",
        "这是", "第二", "句话", "，", "用来", "测试", "流式", "处理", "功能", "。",
        "最后", "一段", "文本", "用于", "验证", "超时", "触发", "机制"
    ]
    
    # 模拟逐词输入（带延迟）和中断
    async def simulate_word_input_with_interrupt():
        for i, word in enumerate(test_words):
            await word_stream.put(word)
            print(f"Added word: {word}")
            
            # 检查是否可以中断
            if tts_client.can_interrupt():
                print(f"Can interrupt: True (after word {i+1})")
            
            # 在第10个词后模拟中断
            if i == 10:
                print("\n=== Simulating interrupt ===")
                await tts_client.interrupt()
                print("=== Interrupt completed ===\n")
                
                # 检查中断后的状态
                print(f"Can interrupt after interrupt: {tts_client.can_interrupt()}")
                break
            
            await asyncio.sleep(0.3)  # 模拟输入延迟
        
        # 发送结束信号
        await word_stream.put(None)
        print("Sent end signal")
    
    # 启动模拟输入
    input_task = asyncio.create_task(simulate_word_input_with_interrupt())
    
    # 等待处理完成
    try:
        await asyncio.gather(input_task, tts_task)
    except Exception as e:
        logging.error(f"Error in example: {e}")
    
    print(f"Stream TTS processing completed. Generated {len(audio_files)} audio files.")
    
    # 最终状态检查
    print(f"Final can_interrupt status: {tts_client.can_interrupt()}")


# 其他示例函数保持不变...

# 主函数
async def main():
    """主函数 - 选择运行模式"""
    # 选择运行模式
    mode = "interrupt"  # "single", "queue", "stream", 或 "interrupt"
    
    if mode == "single":
        await single_tts_example()
    elif mode == "queue":
        await example_queue_processing()
    elif mode == "stream":
        await stream_tts_example()
    elif mode == "interrupt":
        await stream_tts_with_interrupt_example()
    else:
        print("Unknown mode. Please choose 'single', 'queue', 'stream', or 'interrupt'")


if __name__ == "__main__":
    asyncio.run(main())