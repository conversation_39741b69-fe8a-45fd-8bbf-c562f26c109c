# 交互Agent 技术方案

# ProductInteractionAgent 技术方案

## 1. 概述

ProductInteractionAgent 是一个商品交互代理，负责处理用户输入的自然语言指令，并将其转换为对展示Agent的API调用，实现对商品展示界面的远程控制。它作为用户与展示界面之间的桥梁，使用户能够通过文本命令操作商品卡片。

## 2. 核心功能

1.  **指令处理**：解析用户输入的自然语言指令（如"左滑"、"右滑"、"选择商品"等）
    
2.  **API通信**：与ProductDisplayAgent进行HTTP通信
    
3.  **状态监控**：实时检查展示Agent的状态
    
4.  **操作执行**：将用户指令转换为具体操作并执行
    
5.  **结果反馈**：向用户提供操作执行的结果和当前状态
    

## 3. 技术架构

### 3.1 通信架构

*   **通信协议**：HTTP/REST
    
*   **数据格式**：JSON
    
*   **请求方式**：requests库
    
*   **端点管理**：基于URL构建API端点
    

### 3.2 交互模式

*   **命令行交互**：通过控制台输入指令
    
*   **批处理模式**：通过预设指令列表自动执行
    
*   **实时反馈**：每次操作后返回执行结果
    

## 4. API调用设计

|  调用端点  |  方法  |  功能  |  参数  |  返回  |
| --- | --- | --- | --- | --- |
|  `/api/status`  |  GET  |  获取展示Agent状态  |  无  |  当前状态信息  |
|  `/api/action`  |  POST  |  执行操作  |  `action`  |  操作结果  |

## 5. 关键技术实现

### 5.1 状态检查机制

`check_display_agent_status()` 方法通过调用展示Agent的状态API，获取当前展示界面的状态信息，包括：

*   当前显示的商品ID
    
*   是否已关闭
    
*   已选择的商品
    
*   最后一次用户操作
    

这些信息用于判断展示Agent是否可用，以及操作后的状态变化。

### 5.2 操作执行流程

`execute_action()` 方法实现了操作执行的核心流程：

1.  向展示Agent发送操作请求
    
2.  等待操作完成（通过延时确保前端更新）
    
3.  获取最新状态
    
4.  合并操作结果和状态信息
    
5.  返回完整的结果数据
    

### 5.3 用户输入处理

`process_user_input()` 方法处理用户输入的指令：

1.  首先检查展示Agent是否可用
    
2.  执行用户指定的操作
    
3.  构建包含操作结果和当前状态的响应
    
4.  特殊处理"选择商品"和"关闭"等终止操作
    

### 5.4 交互模式实现

提供两种交互模式：

1.  **实时交互模式**（`display_action_main()`）：用户通过控制台实时输入指令
    
2.  **批处理模式**（`display_action_main_v2()`）：预设指令列表，自动按顺序执行
    

## 6. 错误处理与容错机制

1.  **连接错误处理**：捕获与展示Agent通信时可能发生的异常
    
2.  **状态检查**：每次操作前检查展示Agent状态，避免对已关闭的Agent执行操作
    
3.  **超时设置**：API请求设置超时时间，防止长时间阻塞
    
4.  **异常捕获**：全局try-except捕获用户中断和其他异常
    

## 7. 性能优化

1.  **延时优化**：操作后添加适当延时，确保前端状态更新
    
2.  **状态缓存**：操作结果中包含最新状态，减少额外的状态查询
    
3.  **轻量级通信**：使用简单的JSON格式，减少数据传输量
    

## 8. 安全考虑

1.  **本地通信**：仅与本地展示Agent通信，不涉及外部网络
    
2.  **输入验证**：对用户输入进行基本验证，避免注入攻击
    
3.  **超时控制**：API请求添加超时限制，防止资源耗尽
    

## 9. 扩展性

该方案具有良好的扩展性：

1.  **支持更多操作**：可以轻松添加新的操作类型
    
2.  **多Agent协作**：可以扩展为与多个展示Agent通信
    
3.  **自然语言理解**：可以集成NLU模块，支持更复杂的指令解析
    
4.  **状态管理**：可以添加状态缓存和历史记录功能
    

## 10. 使用场景

1.  **语音控制**：结合语音识别，实现语音控制商品展示
    
2.  **自动化测试**：通过预设指令序列测试展示界面
    
3.  **远程操作**：允许用户通过文本指令远程操作展示界面
    
4.  **多模态交互**：作为文本模态与视觉模态的桥接组件
    

## 11. 总结

ProductInteractionAgent 提供了一种灵活、高效的方式，使用户能够通过自然语言指令控制商品展示界面。它与ProductDisplayAgent协同工作，形成完整的商品展示与交互系统，支持多种交互模式和操作类型，具有良好的扩展性和容错能力。