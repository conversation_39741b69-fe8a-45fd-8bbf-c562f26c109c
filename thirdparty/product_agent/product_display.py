import os
import signal
import webbrowser
import threading
import time
import json
import requests
from flask import Flask, request, jsonify


class ProductDisplayAgent:
    """
    商品展示Agent，负责渲染HTML并处理交互操作
    """

    def __init__(self, html_content, port=8000):
        """
        初始化展示Agent
        参数:
        html_content: 生成的HTML内容
        port: 服务器端口
        """
        self.html_content = html_content
        self.port = port
        self.app = Flask(__name__)
        self.server_thread = None
        self.selected_product = None
        self.is_closed = False
        self.current_product_id = None
        self.pending_js_code = None  # 存储待执行的JavaScript代码
        self.last_user_action = None  # 存储用户最后一次操作
        self.setup_routes()

    def setup_routes(self):
        """设置Flask路由"""
        # 禁用Flask的请求日志
        import logging
        log = logging.getLogger('werkzeug')
        log.setLevel(logging.ERROR)  # 只显示错误级别的日志

        # 设置Flask不输出请求日志
        self.app.logger.disabled = True

        @self.app.route('/')
        def index():
            """渲染商品卡片页面"""
            return self.html_content

        @self.app.route('/api/select_product', methods=['POST'])
        def select_product():
            """处理商品选择事件"""
            data = request.json
            product_id = data.get('product_id')
            action = data.get('action', '选择商品')  # 获取用户操作
            self.selected_product = product_id
            self.current_product_id = product_id
            self.last_user_action = action  # 保存最后一次用户操作
            print(f"API: 已选择商品 {product_id}，操作: {action}")
            # 选择商品后设置关闭标志
            self.is_closed = True
            # 设置一个定时器，延迟关闭服务器，确保响应能够返回
            threading.Timer(1.0, self.shutdown_server).start()
            return jsonify({
                'status': 'success',
                'message': f'已选择商品: {product_id}',
                'product_id': product_id,
                'action': action
            })

        @self.app.route('/api/close', methods=['POST'])
        def close_popup():
            """处理关闭事件"""
            data = request.json
            action = data.get('action', '关闭退出')  # 获取用户操作
            print(f"API: 收到关闭请求，操作: {action}")
            # 用户主动关闭时设置关闭标志
            self.is_closed = True
            self.last_user_action = action  # 保存最后一次用户操作
            # 设置一个定时器，延迟关闭服务器，确保响应能够返回
            threading.Timer(1.0, self.shutdown_server).start()
            return jsonify({
                'status': 'success',
                'message': '关闭弹窗',
                'current_product_id': self.current_product_id,
                'action': action
            })

        @self.app.route('/api/status', methods=['GET'])
        def get_status():
            """获取当前状态"""
            return jsonify({
                'current_product_id': self.current_product_id,
                'is_closed': self.is_closed,
                'selected_product': self.selected_product,
                'last_user_action': getattr(self, 'last_user_action', None)  # 返回最后一次用户操作
            })

        @self.app.route('/api/current_product', methods=['POST'])
        def update_current_product():
            """更新当前显示的商品ID"""
            data = request.json
            product_id = data.get('product_id')
            action = data.get('action', '浏览')  # 获取用户操作
            if product_id:
                self.current_product_id = product_id
                self.last_user_action = action  # 保存最后一次用户操作
                print(f"API: 更新当前商品ID为 {product_id}，操作: {action}")
            return jsonify({
                'status': 'success',
                'current_product_id': self.current_product_id,
                'action': action
            })

        @self.app.route('/api/action', methods=['POST'])
        def perform_action():
            """执行操作"""
            data = request.json
            action = data.get('action')
            print(f"API: 收到操作请求 - {action}")

            # 根据操作类型生成JavaScript代码
            if action == "左滑":
                self.pending_js_code = "showPreviousProduct();"
                result = {"success": True, "action": "左滑"}
            elif action == "右滑":
                self.pending_js_code = "showNextProduct();"
                result = {"success": True, "action": "右滑"}
            elif action == "选择商品":
                if self.current_product_id:
                    self.pending_js_code = "selectCurrentProduct();"
                    self.selected_product = self.current_product_id
                    self.is_closed = True  # 设置关闭标志
                    result = {"success": True, "action": "选择商品", "product_id": self.current_product_id}
                    # 设置一个定时器，延迟关闭服务器
                    threading.Timer(1.0, self.shutdown_server).start()
                else:
                    result = {"success": False, "message": "无法获取当前商品ID"}
            elif action in ["关闭", "关闭退出", "关闭页面", "关闭商品卡片"]:
                self.pending_js_code = "closePopup();"
                self.is_closed = True  # 设置关闭标志
                threading.Timer(1.0, self.shutdown_server).start()
                result = {"success": True, "action": "关闭", "current_product_id": self.current_product_id}
            else:
                result = {"success": False, "message": f"未知操作: {action}"}

            return jsonify(result)

        @self.app.route('/api/poll_js', methods=['GET'])
        def poll_js():
            """轮询是否有JavaScript代码需要执行"""
            js_code = self.pending_js_code
            self.pending_js_code = None  # 清除代码，确保只执行一次
            return jsonify({"js_code": js_code})

    def shutdown_server(self):
        """关闭Flask服务器，只在用户完成交互后调用"""
        # 确认是否满足关闭条件
        if self.is_closed or self.selected_product:
            print("正在关闭服务器...")
            try:
                os.kill(os.getpid(), signal.SIGINT)
                print("服务器关闭信号已发送")
            except Exception as e:
                print(f"关闭服务器时出错: {e}")
        else:
            print("尝试关闭服务器，但未满足关闭条件")

    def run_server(self):
        """在后台线程中运行Flask服务器"""
        try:
            print(f"正在启动服务器，端口: {self.port}...")
            self.app.run(host='127.0.0.1', port=self.port, debug=False, threaded=True)
        except Exception as e:
            print(f"服务器运行出错: {e}")
            # 尝试使用不同的端口
            try:
                alt_port = self.port + 1
                print(f"尝试使用备用端口 {alt_port}...")
                self.port = alt_port
                self.app.run(host='127.0.0.1', port=alt_port, debug=False, threaded=True)
            except Exception as e2:
                print(f"备用端口也失败: {e2}")

    def display_products(self):
        """
        启动服务，渲染HTML，并等待用户交互
        返回:
        dict: 包含用户选择的商品信息或关闭状态
        """
        # 修改HTML内容，添加与后端API通信的JavaScript代码
        modified_html = self.inject_api_communication()
        self.html_content = modified_html

        # 创建并启动服务器线程
        self.server_thread = threading.Thread(target=self.run_server)
        self.server_thread.daemon = True
        self.server_thread.start()

        # 等待服务器启动
        print("等待服务器启动...")
        server_started = False
        max_retries = 5
        retry_count = 0

        while not server_started and retry_count < max_retries:
            try:
                time.sleep(1)  # 等待1秒
                response = requests.get(f'http://127.0.0.1:{self.port}/', timeout=1)
                if response.status_code == 200:
                    server_started = True
                    print(f"服务器成功启动在端口 {self.port}")
                else:
                    print(f"服务器响应异常，状态码: {response.status_code}")
                    retry_count += 1
            except requests.exceptions.RequestException:
                print(f"尝试连接服务器 {retry_count + 1}/{max_retries}...")
                retry_count += 1

        if not server_started:
            print("服务器启动失败，请检查端口是否被占用或Flask是否正确安装")
            return {'status': 'error', 'message': '服务器启动失败'}

        # 在浏览器中打开页面
        try:
            webbrowser.open(f'http://127.0.0.1:{self.port}/')
            print(f"已在浏览器中打开商品卡片: http://127.0.0.1:{self.port}/")
        except Exception as e:
            print(f"无法打开浏览器: {e}")
            print(f"请手动在浏览器中访问: http://127.0.0.1:{self.port}/")

        # 等待用户交互（选择商品或关闭）- 只在四种特定情况下关闭
        try:
            # 无限循环，直到满足四种关闭条件之一
            while True:
                # 检查是否满足关闭条件
                if self.is_closed:  # 用户主动关闭了商品卡片或通过交互Agent关闭
                    print("用户关闭了商品卡片")
                    break

                if self.selected_product:  # 用户选择了商品（直接在卡片上或通过交互Agent）
                    print(f"用户选择了商品: {self.selected_product}")
                    break

                # 检查服务器是否仍在运行
                if not self.server_thread.is_alive():
                    print("服务器已停止运行")
                    break

                time.sleep(0.5)  # 短暂休眠以减少CPU使用

        except KeyboardInterrupt:
            print("\n收到键盘中断，但展示Agent将继续运行直到用户完成交互")
            # 不设置self.is_closed，让展示Agent继续运行

        # 返回结果
        if self.selected_product:
            return {
                'status': 'selected',
                'product_id': self.selected_product,
                'action': self.last_user_action
            }
        else:
            return {
                'status': 'closed',
                'current_product_id': self.current_product_id,
                'action': self.last_user_action
            }

    def inject_api_communication(self):
        """
        向HTML注入与后端API通信的JavaScript代码
        返回:
        str: 修改后的HTML内容
        """
        # 查找</script>标签的位置，在其前面插入我们的API通信代码
        script_end_tag = '</script>'
        script_end_pos = self.html_content.rfind(script_end_tag)

        if script_end_pos == -1:
            # 如果没有找到script标签，则在body结束前添加
            body_end_tag = '</body>'
            body_end_pos = self.html_content.rfind(body_end_tag)

            if body_end_pos == -1:
                # 如果连body标签都没有，就添加到HTML末尾
                return self.html_content + f'<script>{self.get_api_script()}</script>'
            else:
                return self.html_content[
                       :body_end_pos] + f'<script>{self.get_api_script()}</script>' + self.html_content[body_end_pos:]
        else:
            # 在现有script的结束标签前插入
            return self.html_content[:script_end_pos] + self.get_api_script() + self.html_content[script_end_pos:]

    def get_api_script(self):
        """
        生成与后端API通信的JavaScript代码
        返回:
        str: JavaScript代码
        """
        return """
        // 添加用户操作记录功能
        let userActionHistory = [];

        // 在页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 获取初始商品ID
            const initialProductItem = document.querySelector('.product-item');
            if (initialProductItem) {
                const initialProductId = initialProductItem.id.replace('item-', '');
                updateCurrentProductId(initialProductId, "初始加载");
            }
            // 启动轮询，检查是否有JavaScript代码需要执行
            startJsPolling();
        });

        // 更新当前显示的商品ID，同时传递操作信息
        function updateCurrentProductId(productId, action) {
            if (!productId) {
                try {
                    const productContainer = document.getElementById('product-container');
                    const productItem = productContainer.querySelector('.product-item');
                    productId = productItem.id.replace('item-', '');
                } catch (error) {
                    console.error('获取商品ID失败:', error);
                    return;
                }
            }

            fetch('/api/current_product', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    action: action || "浏览" // 如果没有提供action，则默认为浏览
                })
            }).catch(error => {
                console.error('更新商品ID失败:', error);
            });
        }

        // 修改原有的滑动函数，添加事件触发和操作记录
        const originalShowNextProduct = window.showNextProduct;
        window.showNextProduct = function() {
            if (originalShowNextProduct) {
                originalShowNextProduct();
            }
            // 记录用户操作
            const action = "右滑";
            userActionHistory.push(action);

            // 获取当前商品ID并更新
            setTimeout(() => {
                const productItem = document.querySelector('.product-item');
                if (productItem) {
                    const productId = productItem.id.replace('item-', '');
                    updateCurrentProductId(productId, action);
                }
            }, 100);
        };

        const originalShowPreviousProduct = window.showPreviousProduct;
        window.showPreviousProduct = function() {
            if (originalShowPreviousProduct) {
                originalShowPreviousProduct();
            }
            // 记录用户操作
            const action = "左滑";
            userActionHistory.push(action);

            // 获取当前商品ID并更新
            setTimeout(() => {
                const productItem = document.querySelector('.product-item');
                if (productItem) {
                    const productId = productItem.id.replace('item-', '');
                    updateCurrentProductId(productId, action);
                }
            }, 100);
        };

        const originalGoToProduct = window.goToProduct;
        window.goToProduct = function(index) {
            if (originalGoToProduct) {
                originalGoToProduct(index);
            }
            // 记录用户操作
            const action = "跳转到商品";
            userActionHistory.push(action);

            // 获取当前商品ID并更新
            setTimeout(() => {
                const productItem = document.querySelector('.product-item');
                if (productItem) {
                    const productId = productItem.id.replace('item-', '');
                    updateCurrentProductId(productId, action);
                }
            }, 100);
        };

        // 修改选择商品函数，添加API调用和操作记录
        const originalSelectProduct = window.selectProduct;
        window.selectProduct = function(itemId) {
            // 记录用户操作
            const action = "选择商品";
            userActionHistory.push(action);

            fetch('/api/select_product', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: itemId,
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('选择商品成功:', data);
                // 可以在这里添加选择成功的视觉反馈
                if (originalSelectProduct) {
                    originalSelectProduct(itemId);
                }
                setTimeout(() => {
                    closePopup();
                }, 500);
            })
            .catch(error => {
                console.error('选择商品失败:', error);
            });
        };

        // 修改关闭弹窗函数，添加API调用和操作记录
        const originalClosePopup = window.closePopup;
        window.closePopup = function() {
            // 记录用户操作
            const action = "关闭退出";
            userActionHistory.push(action);

            fetch('/api/close', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('关闭弹窗成功:', data);
                if (originalClosePopup) {
                    originalClosePopup();
                }
                // 尝试关闭浏览器窗口
                window.close();
            })
            .catch(error => {
                console.error('关闭弹窗失败:', error);
                if (originalClosePopup) {
                    originalClosePopup();
                }
                // 即使API调用失败，也尝试关闭窗口
                window.close();
            });
        };

        // 添加页面卸载事件处理
        window.addEventListener('beforeunload', function() {
            // 如果用户直接关闭浏览器窗口，也发送关闭信号
            navigator.sendBeacon('/api/close', JSON.stringify({action: "浏览器关闭"}));
        });

        // 增强滑动手势处理，确保更新商品ID和记录操作
        const originalHandleSwipe = window.handleSwipe;
        window.handleSwipe = function() {
            const swipeThreshold = 50; // 滑动阈值
            let action = null;

            if (touchEndX < touchStartX - swipeThreshold) {
                // 向左滑动，显示下一个
                action = "右滑";
            } else if (touchEndX > touchStartX + swipeThreshold) {
                // 向右滑动，显示上一个
                action = "左滑";
            }

            if (action) {
                userActionHistory.push(action);
            }

            if (originalHandleSwipe) {
                originalHandleSwipe();
            }

            // 滑动后更新商品ID
            if (action) {
                setTimeout(() => {
                    const productItem = document.querySelector('.product-item');
                    if (productItem) {
                        const productId = productItem.id.replace('item-', '');
                        updateCurrentProductId(productId, action);
                    }
                }, 100);
            }
        };

        // 添加选择当前商品的辅助函数
        function selectCurrentProduct() {
            const productItem = document.querySelector('.product-item');
            if (productItem) {
                const productId = productItem.id.replace('item-', '');
                selectProduct(productId);
            }
        }

        // 轮询JavaScript执行
        let jsPollingInterval;
        function startJsPolling() {
            if (jsPollingInterval) {
                clearInterval(jsPollingInterval);
            }
            jsPollingInterval = setInterval(pollJsCode, 500);
            console.log('JavaScript轮询已启动');
        }

        function stopJsPolling() {
            if (jsPollingInterval) {
                clearInterval(jsPollingInterval);
                jsPollingInterval = null;
                console.log('JavaScript轮询已停止');
            }
        }

        function pollJsCode() {
            fetch('/api/poll_js')
                .then(response => response.json())
                .then(data => {
                    if (data.js_code) {
                        console.log('执行JavaScript:', data.js_code);
                        eval(data.js_code);
                    }
                })
                .catch(error => {
                    console.error('轮询JavaScript失败:', error);
                });
        }

        // 页面卸载时停止轮询
        window.addEventListener('beforeunload', function() {
            stopJsPolling();
        });
        """


def generate_product_html(products):
    """
    根据商品数据生成HTML
    参数:
    products: 商品数据列表
    返回:
    str: 生成的HTML内容
    """
    html_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选择商品</title>
    <style>
        :root {
            --safe-area-inset-top: env(safe-area-inset-top);
            --safe-area-inset-bottom: env(safe-area-inset-bottom);
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: rgba(0, 0, 0, 0.5);
            width: 390px; /* iPhone 16 宽度 */
            height: 844px; /* iPhone 16 高度 */
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0 15px;
        }
        .popup-container {
            background-color: #fff;
            width: 100%;
            max-height: 75%;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
        }
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        .popup-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .close-button {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: #666;
        }
        .product-carousel {
            flex: 1;
            position: relative;
            overflow: hidden;
            padding: 20px 0 30px;
        }
        .product-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 0 16px;
        }
        .product-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            position: relative;
            transition: transform 0.3s ease;
        }
        .product-image-container {
            width: 180px;
            height: 180px;
            margin-bottom: 16px;
        }
        .product-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .product-details {
            width: 100%;
            text-align: center;
        }
        .product-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
        }
        .product-price {
            font-size: 20px;
            font-weight: bold;
            color: #ff6700;
            margin-bottom: 8px;
        }
        .product-price::before {
            content: "¥";
            font-size: 16px;
        }
        .shop-name {
            font-size: 14px;
            color: #999;
            margin-top: 4px;
        }
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            display: flex;
            justify-content: center;
            align-items: center;
            border: none;
            cursor: pointer;
            z-index: 10;
            color: #666;
            font-size: 18px;
            transition: all 0.2s ease;
        }
        .nav-button:hover {
            background-color: #ff6700;
            color: white;
        }
        .prev-button {
            left: 10px;
        }
        .next-button {
            right: 10px;
        }
        .product-indicator {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #ddd;
            margin: 0 4px;
            transition: all 0.2s ease;
        }
        .indicator-dot.active {
            background-color: #ff6700;
            width: 16px;
            border-radius: 4px;
        }
        .select-button {
            background-color: #ff6700;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 30px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 16px;
            transition: all 0.2s ease;
        }
        .select-button:hover {
            background-color: #ff4e00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 103, 0, 0.2);
        }
        /* 适配iPhone动态岛 */
        @supports (padding-top: env(safe-area-inset-top)) {
            body {
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
            }
        }
        /* 添加状态提示 */
        .status-message {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 100;
        }
        .status-message.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="popup-container">
        <div class="popup-header">
            <div class="popup-title">选择商品</div>
            <button class="close-button" onclick="closePopup()">✕</button>
        </div>
        <div class="product-carousel">
            <button class="nav-button prev-button" onclick="showPreviousProduct()">❮</button>
            <button class="nav-button next-button" onclick="showNextProduct()">❯</button>
            <div class="product-container" id="product-container">
                <!-- 商品内容将通过JavaScript动态加载 -->
            </div>
            <div class="product-indicator" id="product-indicator">
                <!-- 指示器将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>
    <div class="status-message" id="status-message"></div>
    <script>
        // 商品数据
        const products = PRODUCT_DATA_PLACEHOLDER;
        let currentIndex = 0;

        // 初始化页面
        function initPage() {
            renderProduct();
            renderIndicators();
            // 初始化后更新当前商品ID
            setTimeout(() => {
                const productItem = document.querySelector('.product-item');
                if (productItem) {
                    const productId = productItem.id.replace('item-', '');
                    try {
                        // 如果API通信代码已注入，则调用更新函数
                        if (typeof updateCurrentProductId === 'function') {
                            updateCurrentProductId(productId, "初始加载");
                        }
                    } catch (e) {
                        console.log("API通信代码尚未注入");
                    }
                }
            }, 500);
        }

        // 显示状态消息
        function showStatusMessage(message, duration = 2000) {
            const statusElement = document.getElementById('status-message');
            statusElement.textContent = message;
            statusElement.classList.add('show');

            setTimeout(() => {
                statusElement.classList.remove('show');
            }, duration);
        }

        // 渲染当前商品
        function renderProduct() {
            const container = document.getElementById('product-container');
            const product = products[currentIndex];
            container.innerHTML = `
                <div class="product-item" id="item-${product.id}">
                    <div class="product-image-container">
                        <img class="product-image" src="${product.image}" alt="${product.title}">
                    </div>
                    <div class="product-details">
                        <div class="product-title">${product.title}</div>
                        <div class="product-price">${product.price}</div>
                        <div class="shop-name">${product.shop}</div>
                        <button class="select-button" onclick="selectProduct('${product.id}')">选择此商品</button>
                    </div>
                </div>
            `;
        }

        // 渲染指示器
        function renderIndicators() {
            const container = document.getElementById('product-indicator');
            container.innerHTML = '';
            for (let i = 0; i < products.length; i++) {
                const dot = document.createElement('div');
                dot.className = `indicator-dot ${i === currentIndex ? 'active' : ''}`;
                dot.onclick = () => goToProduct(i);
                container.appendChild(dot);
            }
        }

        // 显示下一个商品
        function showNextProduct() {
            currentIndex = (currentIndex + 1) % products.length;
            renderProduct();
            renderIndicators();
        }

        // 显示上一个商品
        function showPreviousProduct() {
            currentIndex = (currentIndex - 1 + products.length) % products.length;
            renderProduct();
            renderIndicators();
        }

        // 直接跳转到指定商品
        function goToProduct(index) {
            currentIndex = index;
            renderProduct();
            renderIndicators();
        }

        // 选择商品
        function selectProduct(itemId) {
            console.log('选择商品:', itemId);
            showStatusMessage('已选择商品');
            // 在实际应用中，这里可以将选中的商品ID传回父窗口
        }

        // 关闭弹窗
        function closePopup() {
            console.log('关闭弹窗');
            showStatusMessage('已关闭');
            // 示例：window.parent.closeProductPopup();
        }

        // 添加触摸滑动支持
        let touchStartX = 0;
        let touchEndX = 0;
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('product-container');
            container.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            }, false);
            container.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            }, false);
            // 初始化页面
            initPage();
        });

        // 处理滑动手势
        function handleSwipe() {
            const swipeThreshold = 50; // 滑动阈值
            if (touchEndX < touchStartX - swipeThreshold) {
                // 向左滑动，显示下一个
                showNextProduct();
            }
            if (touchEndX > touchStartX + swipeThreshold) {
                // 向右滑动，显示上一个
                showPreviousProduct();
            }
        }
    </script>
</body>
</html>
"""
    # 将商品数据转换为JSON字符串并插入模板
    products_json = json.dumps(products, ensure_ascii=False)
    return html_template.replace('PRODUCT_DATA_PLACEHOLDER', products_json)


# 主函数，演示完整流程
def display_operation(products):
    """
    单独测试展示Agent，确保只在用户完成交互后才关闭
    参数:
    products: 商品数据列表
    """
    # 步骤1: 生成HTML
    html_content = generate_product_html(products)

    # 步骤2: 创建展示Agent
    display_agent = ProductDisplayAgent(html_content)

    # 使用一个队列来存储display_products的返回值
    import queue
    result_queue = queue.Queue()

    # 修改线程函数，将结果放入队列
    def run_display_agent():
        result = display_agent.display_products()
        result_queue.put(result)

    # 启动展示Agent线程
    display_thread = threading.Thread(target=run_display_agent)
    display_thread.daemon = True
    display_thread.start()

    # 等待展示Agent启动
    time.sleep(3)  # 增加等待时间确保服务器启动

    print("展示Agent已启动，请在浏览器中操作商品卡片...")
    print("展示Agent将在以下情况关闭:")
    print("1. 用户在商品卡片上选择了商品")
    print("2. 用户主动关闭了商品卡片")
    print("3. 用户在交互Agent中输入action选择了商品")
    print("4. 用户在交互Agent中输入action主动关闭了商品卡片")

    try:
        # 等待展示Agent线程结束
        # 这里不设置超时，让线程一直运行直到用户完成交互
        display_thread.join()

    except KeyboardInterrupt:
        print("\n收到键盘中断，但展示Agent将继续运行直到用户完成交互")
        print("请在浏览器中完成交互以关闭展示Agent")

    # 获取展示Agent的最终结果
    try:
        if not result_queue.empty():
            final_result = result_queue.get(block=False)
            print(f"展示Agent最终结果: {json.dumps(final_result, indent=2, ensure_ascii=False)}")
        else:
            print("未能获取展示Agent的最终结果")
    except queue.Empty:
        print("未能获取展示Agent的最终结果")

    print("程序结束")


if __name__ == "__main__":
    # 示例商品数据
    products = [
        {
            "id": "844758148374",
            "title": "REDMI K80智能学生游戏手机",
            "price": "2299.00",
            "image": "http://g.search2.alicdn.com/img/bao/uploaded/i4/i1/1714128138/O1CN01gVu9Xs29zGF8StyK7_!!4611686018427381002-2-item_pic.png",
            "shop": "小米官方旗舰店"
        },
        {
            "id": "902731028409",
            "title": "REDMI Turbo 4 Pro新品手机",
            "price": "2199.00",
            "image": "http://g.search2.alicdn.com/img/bao/uploaded/i4/i1/1714128138/O1CN01CcpKqv29zGFFPZXSh_!!4611686018427381002-2-item_pic.png",
            "shop": "小米官方旗舰店"
        },
        {
            "id": "837833364201",
            "title": "小米红米note14Pro+新品手机5G",
            "price": "1559.00",
            "image": "http://g.search.alicdn.com/img/bao/uploaded/i4/i2/2217260545425/O1CN01XXrj4p1pwhiUM5uzf_!!4611686018427385233-0-item_pic.jpg",
            "shop": "小米探源专卖店"
        },
        {
            "id": "738982589760",
            "title": "红米Redmi Note 13 5G手机",
            "price": "1699.00",
            "image": "http://g.search1.alicdn.com/img/bao/uploaded/i4/i3/1714128138/O1CN01Xg77ul29zGF7BCjiB_!!4611686018427381002-2-item_pic.png",
            "shop": "小米官方旗舰店"
        }
    ]

    ## 展示 + 交互 Agent功能测试：针对商品卡片，用户可以浏览器直接操作，也可以在控制台输入语言指令操作
    display_operation(products)
