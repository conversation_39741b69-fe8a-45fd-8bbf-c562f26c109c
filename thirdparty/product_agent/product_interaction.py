import os
import signal
import webbrowser
import threading
import time
import json
import requests
from flask import Flask, request, jsonify




class ProductInteractionAgent:
    """
    商品交互Agent，负责处理用户输入并执行相应操作
    """

    def __init__(self, url="http://127.0.0.1:8000"):
        """
        初始化交互Agent
        参数:
        url: 展示Agent的URL
        """
        self.url = url
        self.api_url = f"{url}/api"

    def check_display_agent_status(self):
        """
        检查展示Agent的状态
        返回:
        dict: 状态信息
        """
        try:
            response = requests.get(f"{self.api_url}/status", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"获取状态失败，状态码: {response.status_code}"}
        except Exception as e:
            return {"error": f"连接展示Agent失败: {str(e)}"}

    def execute_action(self, action):
        """
        执行单个操作
        参数:
        action: 操作名称，如 "左滑", "右滑", "选择商品", "关闭退出"
        返回:
        dict: 操作结果
        """
        try:
            print(f"执行操作: {action}")
            response = requests.post(
                f"{self.api_url}/action",
                json={"action": action},
                timeout=5
            )
            if response.status_code == 200:
                result = response.json()
                # 操作后等待一段时间，让前端更新商品ID
                time.sleep(1.0)  # 增加等待时间，确保操作完成
                # 获取最新状态
                status = self.check_display_agent_status()
                if "error" not in status:
                    result["current_product_id"] = status.get("current_product_id")
                    result["last_user_action"] = status.get("last_user_action")
                return result
            else:
                return {"success": False, "message": f"操作失败，状态码: {response.status_code}"}
        except Exception as e:
            return {"success": False, "message": f"执行操作时出错: {str(e)}"}

    def process_user_input(self, action):
        """
        处理用户输入并执行相应操作
        参数:
        action: 用户操作
        返回:
        dict: 操作结果
        """
        # 检查展示Agent是否可用
        status = self.check_display_agent_status()
        if "error" in status:
            return {"status": "error", "message": status["error"]}
        if status.get("is_closed", False):
            return {"status": "error", "message": "展示Agent已关闭"}

        # 执行操作
        action_result = self.execute_action(action)

        # 构建结果
        result = {
            "action": action,
            "success": action_result.get("success", False),
            "message": action_result.get("message", ""),
            "current_product_id": action_result.get("current_product_id", status.get("current_product_id")),
            "user_action": action_result.get("last_user_action", status.get("last_user_action"))  # 添加用户操作信息
        }

        # 如果是选择商品或关闭操作，添加相应状态
        if action == "选择商品" and action_result.get("success", False):
            result["status"] = "selected"
            result["product_id"] = action_result.get("product_id", result["current_product_id"])
        elif action in ["关闭", "关闭退出", "关闭页面", "关闭商品卡片"] and action_result.get("success", False):
            result["status"] = "closed"

        return result


def user_action_main():
    """

    用户需要在控制台输入
    :param products:
    :return:
    """
    # 创建交互Agent
    interaction_agent = ProductInteractionAgent()

    # 模拟用户交互
    try:
        while True:
            # 获取用户输入
            user_input = input("请输入操作 (左滑/右滑/选择商品/关闭退出): ")
            if not user_input:
                continue

            # 处理用户输入
            result = interaction_agent.process_user_input(user_input)
            print(f"操作结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

            # 如果操作是选择商品或关闭，则退出循环
            if "status" in result and result["status"] in ["selected", "closed"]:
                break

            time.sleep(0.5)  # 短暂暂停，等待操作完成
    except KeyboardInterrupt:
        print("程序被用户中断")

    print("程序结束")


def prefine_action_main(action_lst):
    """
    提前给定了用户输入action列表
    :return:
    """
    # 创建交互Agent
    interaction_agent = ProductInteractionAgent()



    for action in action_lst:
        # 处理用户输入
        result = interaction_agent.process_user_input(action)
        print(f"操作结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        # 如果操作是选择商品或关闭，则退出循环
        if "status" in result and result["status"] in ["selected", "closed"]:
            break
        time.sleep(0.5)  # 短暂暂停，等待操作完成


    print("程序结束")


if __name__ == "__main__":
    # ## 展示 + 交互 Agent功能测试：针对商品卡片，用户可以浏览器直接操作，也可以在控制台输入语言指令操作
    # user_action_main()  ## 用户需要在控制台输入action: 左滑/右滑/选择商品/关闭退出


    ## 展示 + 交互 Agent功能测试：针对商品卡片，用户可以浏览器直接操作，也可以在控制台输入语言指令操作
    action_lst = ["左滑", "左滑", "左滑", "左滑","左滑", "右滑", "右滑", "右滑", "右滑"]
    prefine_action_main(action_lst)  ## 提前预设了用户的语言指令action



