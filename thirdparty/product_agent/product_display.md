# 展示Agent 技术方案

# ProductDisplayAgent 技术方案

## 1. 概述

ProductDisplayAgent 是一个商品展示代理，负责渲染商品卡片的 HTML 内容并处理用户交互操作。它通过 Flask 创建本地服务器，在浏览器中展示商品卡片，并提供 API 接口实现前后端通信，记录用户操作并返回操作结果。

## 2. 核心功能

1.  **HTML 渲染与展示**：将商品数据转换为交互式 HTML 页面并在浏览器中展示
    
2.  **用户交互处理**：支持左右滑动、点击选择、关闭等操作
    
3.  **API 通信**：提供 RESTful API 接口，实现前后端数据交换
    
4.  **操作记录**：记录用户的所有交互行为
    
5.  **远程控制**：支持通过 API 远程执行页面操作（如左滑、右滑、选择商品等）
    

## 3. 技术架构

### 3.1 后端架构

*   **Web 框架**：Flask
    
*   **多线程处理**：使用 Python threading 模块实现后台服务
    
*   **通信协议**：RESTful API + JSON
    
*   **浏览器集成**：webbrowser 模块
    

### 3.2 前端架构

*   **UI 框架**：原生 HTML/CSS/JavaScript
    
*   **交互方式**：触摸滑动 + 按钮点击
    
*   **通信方式**：Fetch API
    
*   **轮询机制**：JavaScript 定时器实现指令轮询
    

## 4. API 接口设计

|  接口  |  方法  |  功能  |  参数  |  返回  |
| --- | --- | --- | --- | --- |
|  `/`  |  GET  |  渲染商品卡片页面  |  无  |  HTML 页面  |
|  `/api/select_product`  |  POST  |  处理商品选择事件  |  `product_id`, `action`  |  选择结果  |
|  `/api/close`  |  POST  |  处理关闭事件  |  `action`  |  关闭结果  |
|  `/api/status`  |  GET  |  获取当前状态  |  无  |  当前状态信息  |
|  `/api/current_product`  |  POST  |  更新当前显示商品  |  `product_id`, `action`  |  更新结果  |
|  `/api/action`  |  POST  |  执行操作  |  `action`  |  操作结果  |
|  `/api/poll_js`  |  GET  |  轮询待执行的 JS 代码  |  无  |  JavaScript 代码  |

## 5. 关键技术实现

### 5.1 HTML 注入与通信

通过 `inject_api_communication()` 方法，向原始 HTML 注入与后端通信的 JavaScript 代码，实现前后端双向通信。注入的代码包括：

*   API 通信函数
    
*   用户操作记录
    
*   事件监听器
    
*   JavaScript 轮询机制
    

### 5.2 服务器生命周期管理

服务器在以下四种情况下会自动关闭：

1.  用户在商品卡片上选择了商品
    
2.  用户主动关闭了商品卡片
    
3.  用户通过交互 Agent 选择了商品
    
4.  用户通过交互 Agent 主动关闭了商品卡片
    

通过 `shutdown_server()` 方法实现服务器的安全关闭。

### 5.3 远程控制机制

通过 `/api/action` 接口和 JavaScript 轮询机制，实现对页面的远程控制：

1.  接收操作指令（如"左滑"、"右滑"、"选择商品"）
    
2.  生成对应的 JavaScript 代码
    
3.  前端通过轮询获取并执行这些代码
    

### 5.4 用户操作记录

前端记录所有用户交互操作，包括：

*   滑动操作（左滑/右滑）
    
*   选择商品
    
*   关闭页面
    
*   浏览商品
    

这些操作通过 API 发送到后端并保存在 `last_user_action` 中。

## 6. 错误处理与容错机制

1.  **服务器启动失败处理**：尝试使用备用端口
    
2.  **API 调用失败处理**：前端添加错误捕获和重试机制
    
3.  **浏览器关闭事件处理**：使用 `beforeunload` 事件和 `navigator.sendBeacon` 确保数据发送
    
4.  **超时处理**：使用定时器确保响应能够返回
    

## 7. 性能优化

1.  **减少日志输出**：禁用 Flask 的请求日志
    
2.  **轻量级通信**：使用简单的 JSON 格式
    
3.  **延迟关闭**：使用定时器延迟关闭服务器，确保响应返回
    
4.  **资源释放**：页面卸载时停止轮询
    

## 8. 安全考虑

1.  **本地服务**：仅在本地 127.0.0.1 上运行，不对外开放
    
2.  **无状态设计**：不存储敏感信息
    
3.  **输入验证**：API 接口对输入参数进行验证
    

## 9. 扩展性

该方案具有良好的扩展性：

1.  可以轻松添加新的 API 接口
    
2.  支持自定义 HTML 模板
    
3.  可以集成到更大的系统中
    
4.  支持添加更多交互功能
    

## 10. 总结

ProductDisplayAgent 通过 Flask 和 JavaScript 实现了一个轻量级但功能完善的商品展示系统，支持丰富的交互方式和远程控制能力，适合作为智能代理系统的展示组件使用。