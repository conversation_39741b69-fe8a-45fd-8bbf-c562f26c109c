@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 实时语音商品搜索代理 - Windows环境安装脚本
REM 使用方法: install.bat

echo 🚀 开始安装实时语音商品搜索代理环境...

REM 检查Python版本
echo 📋 检查Python版本...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.10或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✅ Python版本: %python_version%

REM 检查uv是否安装
echo 📋 检查uv包管理器...
uv --version >nul 2>&1
if errorlevel 1 (
    echo 📦 安装uv包管理器...
    powershell -Command "irm https://astral.sh/uv/install.ps1 | iex"
    
    REM 刷新环境变量
    call refreshenv >nul 2>&1
    
    REM 再次检查
    uv --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ uv安装失败，请手动安装
        echo 方法1: pip install uv
        echo 方法2: 访问 https://docs.astral.sh/uv/getting-started/installation/
        pause
        exit /b 1
    )
)
echo ✅ uv包管理器已就绪

REM 创建虚拟环境并安装依赖
echo 📦 安装项目依赖...
uv sync
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 安装修改过的OpenAI包
echo 🔧 安装修改过的OpenAI包...
if exist "thirdparty\realtime_chat\openai-python" (
    uv add --editable .\thirdparty\realtime_chat\openai-python
    if errorlevel 1 (
        echo ⚠️ 修改过的OpenAI包安装失败，请手动安装
    ) else (
        echo ✅ 修改过的OpenAI包安装成功
    )
) else (
    echo ⚠️ 警告: 未找到修改过的OpenAI包目录
)

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist "image_cache" mkdir image_cache
if not exist "logs" mkdir logs
echo ✅ 目录创建完成

REM 检查SSL证书
echo 🔐 检查SSL证书...
if not exist "cert.pem" (
    echo ⚠️ 警告: 未找到SSL证书文件 cert.pem
)
if not exist "key.pem" (
    echo ⚠️ 警告: 未找到SSL证书文件 key.pem
)
if not exist "cert.pem" if not exist "key.pem" (
    echo    如需HTTPS访问，请提供SSL证书文件
    echo    或者可以使用HTTP模式运行（不推荐生产环境）
)

REM 检查环境变量配置
echo ⚙️ 检查环境配置...
if not exist ".env" if not exist "key.env" (
    echo 📝 创建示例环境配置文件...
    (
        echo # Azure OpenAI配置
        echo AZURE_OPENAI_API_KEY=your_api_key_here
        echo AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
        echo AZURE_OPENAI_MODEL_NAME=gpt-4
        echo.
        echo # 运行模式配置
        echo MODE=WEB
        echo.
        echo # 可选配置
        echo # DEBUG=true
        echo # LOG_LEVEL=INFO
    ) > .env
    echo ✅ 已创建 .env 配置文件，请编辑其中的API密钥等配置
) else (
    echo ✅ 环境配置文件已存在
)

REM 运行完整验证
echo 🔍 运行完整安装验证...
uv run python verify_installation.py
if errorlevel 1 (
    echo ⚠️ 安装验证发现问题，请查看上述输出
) else (
    echo ✅ 安装验证通过
)

echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步操作：
echo 1. 编辑 .env 文件，配置您的API密钥
echo 2. 运行应用: uv run python app.py
echo 3. 访问: https://localhost:7878
echo.
echo 📚 更多信息请查看 README.md
echo.
echo 🔧 故障排除：
echo - 如遇到依赖问题: uv sync --reinstall
echo - 如遇到OpenAI包问题: uv add --editable .\thirdparty\realtime_chat\openai-python
echo - 查看详细日志: uv run python app.py --verbose
echo.
pause
