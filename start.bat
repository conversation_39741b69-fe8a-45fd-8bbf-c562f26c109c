@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 实时语音商品搜索代理 - Windows快速启动脚本
REM 使用方法: start.bat [mode]
REM mode可选: web(默认), ui, phone

REM 默认运行模式
set MODE=%1
if "%MODE%"=="" set MODE=web

echo 🚀 启动实时语音商品搜索代理...
echo 📋 运行模式: %MODE%

REM 检查环境
if not exist ".venv" if not exist "pyproject.toml" (
    echo ❌ 错误: 未找到虚拟环境，请先运行 install.bat
    pause
    exit /b 1
)

REM 检查配置文件
if not exist ".env" if not exist "key.env" (
    echo ⚠️ 警告: 未找到环境配置文件，请确保已配置API密钥
)

REM 设置运行模式环境变量
if /i "%MODE%"=="web" (
    set MODE=WEB
    echo 🌐 Web模式启动中...
) else if /i "%MODE%"=="ui" (
    set MODE=UI
    echo 🖥️ UI模式启动中...
) else if /i "%MODE%"=="phone" (
    set MODE=PHONE
    echo 📞 电话模式启动中...
) else (
    echo ❌ 未知模式: %MODE%
    echo 支持的模式: web, ui, phone
    pause
    exit /b 1
)

REM 检查端口占用
netstat -an | find "7878" | find "LISTENING" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ 警告: 端口7878已被占用
    echo 请关闭占用端口的程序或修改app.py中的端口配置
)

REM 启动应用
echo 🎯 启动应用...
echo 📍 访问地址: https://localhost:7878
echo 🛑 按 Ctrl+C 停止服务
echo.

REM 运行应用
uv run python app.py
