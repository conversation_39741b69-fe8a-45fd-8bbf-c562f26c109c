#!/bin/bash

# 实时语音商品搜索代理 - 环境安装脚本
# 使用方法: bash install.sh

set -e  # 遇到错误时退出

echo "🚀 开始安装实时语音商品搜索代理环境..."

# 检查Python版本
echo "📋 检查Python版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.10"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 错误: 需要Python 3.10或更高版本，当前版本: $python_version"
    exit 1
fi
echo "✅ Python版本检查通过: $python_version"

# 检查uv是否安装
echo "📋 检查uv包管理器..."
if ! command -v uv &> /dev/null; then
    echo "📦 安装uv包管理器..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    export PATH="$HOME/.cargo/bin:$PATH"
    
    # 检查安装是否成功
    if ! command -v uv &> /dev/null; then
        echo "❌ uv安装失败，请手动安装: https://docs.astral.sh/uv/getting-started/installation/"
        exit 1
    fi
fi
echo "✅ uv包管理器已就绪"

# 创建虚拟环境并安装依赖
echo "📦 安装项目依赖..."
uv sync

# 安装修改过的OpenAI包
echo "🔧 安装修改过的OpenAI包..."
if [ -d "thirdparty/realtime_chat/openai-python" ]; then
    uv add --editable ./thirdparty/realtime_chat/openai-python
    echo "✅ 修改过的OpenAI包安装成功"
else
    echo "⚠️  警告: 未找到修改过的OpenAI包目录"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p image_cache
mkdir -p logs
echo "✅ 目录创建完成"

# 检查SSL证书
echo "🔐 检查SSL证书..."
if [ ! -f "cert.pem" ] || [ ! -f "key.pem" ]; then
    echo "⚠️  警告: 未找到SSL证书文件 (cert.pem, key.pem)"
    echo "   如需HTTPS访问，请提供SSL证书文件"
    echo "   或者可以使用HTTP模式运行（不推荐生产环境）"
fi

# 检查环境变量配置
echo "⚙️  检查环境配置..."
if [ ! -f ".env" ] && [ ! -f "key.env" ]; then
    echo "📝 创建示例环境配置文件..."
    cat > .env << EOF
# Azure OpenAI配置
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_MODEL_NAME=gpt-4

# 运行模式配置
MODE=WEB

# 可选配置
# DEBUG=true
# LOG_LEVEL=INFO
EOF
    echo "✅ 已创建 .env 配置文件，请编辑其中的API密钥等配置"
else
    echo "✅ 环境配置文件已存在"
fi

# 运行完整验证
echo "🔍 运行完整安装验证..."
if uv run python verify_installation.py; then
    echo "✅ 安装验证通过"
else
    echo "⚠️  安装验证发现问题，请查看上述输出"
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 编辑 .env 文件，配置您的API密钥"
echo "2. 运行应用: uv run python app.py"
echo "3. 访问: https://localhost:7878"
echo ""
echo "📚 更多信息请查看 README.md"
echo ""
echo "🔧 故障排除："
echo "- 如遇到依赖问题: uv sync --reinstall"
echo "- 如遇到OpenAI包问题: uv add --editable ./thirdparty/realtime_chat/openai-python"
echo "- 查看详细日志: uv run python app.py --verbose"
