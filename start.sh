#!/bin/bash

# 实时语音商品搜索代理 - 快速启动脚本
# 使用方法: bash start.sh [mode]
# mode可选: web(默认), ui, phone

set -e

# 默认运行模式
MODE=${1:-web}

echo "🚀 启动实时语音商品搜索代理..."
echo "📋 运行模式: $MODE"

# 检查环境
if [ ! -f ".venv/pyvenv.cfg" ] && [ ! -f "pyproject.toml" ]; then
    echo "❌ 错误: 未找到虚拟环境，请先运行 bash install.sh"
    exit 1
fi

# 检查配置文件
if [ ! -f ".env" ] && [ ! -f "key.env" ]; then
    echo "⚠️  警告: 未找到环境配置文件，请确保已配置API密钥"
fi

# 设置运行模式环境变量
case $MODE in
    "web"|"WEB")
        export MODE="WEB"
        echo "🌐 Web模式启动中..."
        ;;
    "ui"|"UI")
        export MODE="UI"
        echo "🖥️  UI模式启动中..."
        ;;
    "phone"|"PHONE")
        export MODE="PHONE"
        echo "📞 电话模式启动中..."
        ;;
    *)
        echo "❌ 未知模式: $MODE"
        echo "支持的模式: web, ui, phone"
        exit 1
        ;;
esac

# 检查端口占用
if lsof -Pi :7878 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  警告: 端口7878已被占用"
    echo "请关闭占用端口的程序或修改app.py中的端口配置"
fi

# 启动应用
echo "🎯 启动应用..."
echo "📍 访问地址: https://localhost:7878"
echo "🛑 按 Ctrl+C 停止服务"
echo ""

# 运行应用
uv run python app.py
