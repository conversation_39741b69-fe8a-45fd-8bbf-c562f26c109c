# 语音交互智能意图处理功能说明

## 功能概述

在现有的语音交互系统基础上，新增了智能意图处理功能，能够智能区分和处理不同类型的语音输入：

1. **无意图内容（直接忽略）** - 无意义杂音、环境杂音、语气词、明显在和其他人说话的情况，系统直接忽略，不做任何处理
2. **语音识别无法理解的内容** - 当ASR识别结果包含无意义内容时，系统会请用户重新说一次
3. **用户意图不明确或模糊** - 当用户表达过于简短或模糊时，系统会反问以获取更具体的信息

## 技术实现

### 新增意图类型

在原有的 `search`、`interaction`、`ambiguous` 基础上，增加了：

- `no_intent` - 无意图：无意义杂音、环境杂音、语气词、明显在和其他人说话，直接忽略
- `unclear` - 语音识别内容无法理解
- `need_clarification` - 需要澄清的模糊意图

### 核心功能模块

#### 1. 无意图检测 (`_is_no_intent`)

检测以下情况并直接忽略：
- 空输入或只有空白字符
- 无意义的短词汇：语气词（"唉"、"啊"、"嗯"、"呃"等）
- 只包含标点符号和空格
- 特别短的无意义内容（1-2个字符且不是有意义的词）
- 明显在和其他人说话：包含称呼（"妈妈"、"老公"等）或对话特征（"你过来"、"我们走"等）
- 环境杂音特征：重复字符（"呃呃呃"）或过多杂音字符组合

#### 2. 无法理解输入检测 (`_is_unclear_input`)

检测以下情况：
- 包含过多无意义字符（如"那个"、"就是"等连接词）
- 输入过短（清理后长度≤1）
- 疑似乱码（有意义字符占比<50%）

#### 3. 无法理解输入处理 (`handle_unclear_input`)

当检测到无法理解的输入时：
- 随机选择友好的反问回复
- 请用户重新说一遍
- 示例回复：
  - "抱歉，我没有听清楚您说的话，能请您再说一遍吗？"
  - "不好意思，刚才没听明白，您能再重复一下吗？"

#### 3. 澄清需求处理 (`handle_clarification_needed`)

当用户意图需要澄清时：
- 使用LLM生成针对性的澄清问题
- 根据上下文提供合适的引导
- 保持友好自然的语气

#### 4. 智能澄清问题生成 (`generate_clarification_question`)

使用LLM根据用户输入和意图分析结果生成个性化的澄清问题：
- 语气友好、自然
- 简洁明了（不超过30字）
- 能够引导用户提供更具体信息
- 考虑购物场景但不强制引导

### 增强的意图分析

#### 备选意图分析 (`fallback_intent_analysis`)

增强了关键词匹配逻辑：
- 优先检测无法理解的输入
- 识别过于简短或模糊的表达
- 区分明确的搜索关键词和交互关键词
- 对短输入采用更严格的澄清策略

## 处理场景示例

### 场景1：无意图（直接忽略）

**用户输入**：`"嗯"`
**系统响应**：直接忽略，不做任何处理

**用户输入**：`"妈妈你过来"`
**系统响应**：直接忽略，不做任何处理

**用户输入**：`"咳"`
**系统响应**：直接忽略，不做任何处理

### 场景2：语音识别无法理解

**用户输入**：`"那个...就是..."`
**系统响应**：`"抱歉，我没有听清楚您说的话，能请您再说一遍吗？"`

### 场景3：意图过于模糊

**用户输入**：`"这个"`
**系统响应**：`"您想要搜索什么类型的商品呢？"`

### 场景4：缺乏上下文

**用户输入**：`"要"`
**系统响应**：`"能告诉我您具体想要什么吗？"`

### 场景5：正常意图（不触发反问）

**用户输入**：`"我想买手机"`
**系统响应**：正常执行商品搜索流程

**用户输入**：`"你好"`
**系统响应**：正常执行交互意图处理

## 配置参数

### 无法理解检测阈值
- 最小有意义字符长度：1
- 有意义字符占比阈值：50%
- 澄清触发的最大输入长度：5字符

### 反问回复模板
系统内置多种友好的反问模板，随机选择以避免重复：
- 听不清类：`"抱歉，我没有听清楚..."`
- 理解困难类：`"不好意思，我没有理解..."`
- 请求重复类：`"您能再说一遍吗？"`

## 使用方式

反问功能已集成到现有的语音交互流程中，无需额外配置：

1. 用户进行语音输入
2. 系统自动进行意图分析
3. 如果检测到需要反问的情况，自动触发相应的反问逻辑
4. 用户收到反问后可以重新输入或提供更多信息

## 测试验证

已通过 `test_clarification.py` 脚本验证了以下场景：
- ✅ 空输入检测
- ✅ 标点符号检测  
- ✅ 无意义词汇检测
- ✅ 模糊表达检测
- ✅ 明确意图识别（不误触发反问）
- ✅ 澄清问题生成

## 注意事项

1. **保持原有功能完整性**：反问功能不会影响现有的ASR、TTS、语音打断等核心功能
2. **LLM依赖**：澄清问题生成依赖于 `self.chat.stream_chat_with_interrupt` 方法
3. **语音提醒**：在需要语音输入时，系统会提醒用户说话
4. **错误容错**：当LLM调用失败时，系统会使用预设的默认澄清问题

## 后续优化建议

1. **个性化反问**：根据用户历史交互记录，生成更个性化的反问
2. **上下文记忆**：记住之前的对话上下文，避免重复询问相同信息
3. **多轮澄清**：支持多轮反问，逐步收集用户需求
4. **情感识别**：根据用户语音的情感状态调整反问策略
