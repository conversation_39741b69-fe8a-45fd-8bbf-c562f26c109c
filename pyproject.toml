[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "realtime_speech_search_agent"
version = "1.18.0.dev0"
description = "实时语音商品搜索代理 - 基于smolagents的语音交互购物助手"
authors = [
  { name="Realtime Speech Search Team", email="<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  # Core smolagents dependencies
  "huggingface-hub>=0.31.2",
  "requests>=2.32.3",
  "rich>=13.9.4",
  "jinja2>=3.1.4",
  "pillow>=10.0.1",
  "python-dotenv>=1.0.1",
  # Web framework dependencies
  "fastapi>=0.115.11",
  "uvicorn>=0.34.0",
  "starlette>=0.46.1",
  "python-multipart>=0.0.20",
  # Audio processing dependencies
  "sounddevice>=0.5.1",
  "soundfile>=0.13.1",
  "pyaudio",
  "pydub>=0.25.1",
  "librosa>=0.11.0",
  "audioread>=3.0.1",
  # Real-time communication dependencies
  "websockets>=15.0.1",
  "aiortc>=1.10.1",
  "fastrtc>=0.0.14",
  "aioice>=0.9.0",
  "pylibsrtp>=0.11.0",
  # HTTP and async dependencies
  "aiohttp>=3.11.13",
  "aiofiles>=23.2.1",
  "aiohappyeyeballs>=2.6.1",
  "aiohttp-retry>=2.9.1",
  "httpx>=0.28.1",
  "httpcore>=1.0.7",
  # Data processing dependencies
  "numpy>=2.1.3",
  "pandas>=2.2.3",
  "pydantic>=2.10.6",
  "orjson>=3.10.15",
  # UI dependencies
  "gradio>=5.20.1",
  "gradio_client>=1.7.2",
  # TTS and speech dependencies
  "kokoro-onnx>=0.4.5",
  "phonemizer-fork>=3.3.1",
  "espeakng-loader>=0.2.4",
  "fastrand>=1.0.0",
  # Machine learning dependencies
  "onnxruntime>=1.21.0",
  "scikit-learn>=1.6.1",
  "scipy>=1.15.2",
  "numba>=0.61.0",
  "llvmlite>=0.44.0",
  "joblib>=1.4.2",
  "threadpoolctl>=3.5.0",
  # Utility dependencies
  "coloredlogs>=15.0.1",
  "colorlog>=6.9.0",
  "tqdm>=4.67.1",
  "typer>=0.15.2",
  "click>=8.1.8",
  "PyYAML>=6.0.2",
  "tomlkit>=0.13.2",
  "python-dateutil>=2.9.0.post0",
  "pytz>=2025.1",
  # Security and crypto dependencies
  "cryptography>=44.0.2",
  "pyOpenSSL>=25.0.0",
  "PyJWT>=2.10.1",
  "certifi>=2025.1.31",
  # Communication dependencies
  "twilio>=9.5.0",
  # Data format dependencies
  "protobuf>=6.30.0",
  "flatbuffers>=25.2.10",
  "msgpack>=1.1.0",
  # Semantic web dependencies
  "rdflib>=7.1.3",
  "csvw>=3.5.1",
  "language-tags>=1.2.0",
  "isodate>=0.7.2",
  # Additional utilities
  "babel>=2.17.0",
  "segments>=2.3.0",
  "semantic-version>=2.10.0",
  "shellingham>=1.5.4",
  "uritemplate>=4.1.1",
  "urllib3>=2.3.0",
  "dnspython>=2.7.0",
  "ifaddr>=0.2.0",
  "google-crc32c>=1.6.0",
  "pooch>=1.8.2",
  "platformdirs>=4.3.6",
  "lazy_loader>=0.4",
  "soxr>=0.5.0.post1",
  "av>=13.1.0",
  "groovy>=0.1.2",
  "safehttpx>=0.1.6",
  "ffmpy>=0.5.0",
  # JSON processing
  "jiter>=0.9.0",
  "jsonschema>=4.23.0",
  "jsonschema-specifications>=2024.10.1",
  "referencing>=0.36.2",
  "rpds-py>=0.23.1",
  # Regex and text processing
  "regex>=2024.11.6",
  "rfc3986>=1.5.0",
  # Async utilities
  "sniffio>=1.3.1",
  "anyio>=4.8.0",
  "pyee>=12.1.1",
  "openai",
  "smolagents>=1.18.0",
]

# 本地依赖 - 修改过的openai包
# 注意：这个包位于 thirdparty/realtime_chat/openai-python 目录
# 安装时需要使用: uv add --editable ./thirdparty/realtime_chat/openai-python

[project.optional-dependencies]
# 开发和测试依赖
dev = [
  "ruff>=0.9.0",
  "pytest>=8.1.0",
  "pytest-asyncio",
  "ipython>=8.31.0",
]

# 搜索和工具依赖
search = [
  "duckduckgo-search>=6.3.7",
  "markdownify>=0.14.1",
  "rank-bm25",
  "Wikipedia-API>=0.8.1",
]

# AI模型依赖
ai = [
  "torch",
  "torchvision",
  "transformers>=4.0.0",
  "accelerate",
]

# 完整功能依赖
all = [
  "realtime_speech_search_agent[dev,search,ai]",
]

[tool.pytest.ini_options]
addopts = "-sv --durations=0"
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.ruff]
line-length = 119
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "I", "W", "B"]
ignore = [
  "F403", # undefined-local-with-import-star
  "E501", # line-too-long
  "B006", # mutable defaults
]

[tool.ruff.lint.per-file-ignores]
"thirdparty/*" = ["E", "F", "I", "W", "B"]  # 忽略第三方代码的检查
"app.py" = ["E501"]  # 主应用文件允许长行

[tool.ruff.lint.isort]
known-first-party = ["realtime_speech_search_agent"]
lines-after-imports = 2

[tool.setuptools]
packages = []  # 这是一个依赖收集项目，不安装包

[tool.uv.sources]
openai = { path = "thirdparty/realtime_chat/openai-python", editable = true }

[project.scripts]
# 主应用启动脚本
realtime-speech-search = "app:main"
