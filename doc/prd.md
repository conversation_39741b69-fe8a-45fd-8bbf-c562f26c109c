# PRD: 实时语音交互商品搜索助手 (Realtime Speech Search Agent)

**版本**: 2.0  
**日期**: 2025-01-27  
**基于**: prd_1.md 和 prd_2.md 的技术整合与优化

---

## 1. 项目概述

### 1.1 项目目标

基于 `smolagents` 框架和 `fastrtc` 实时通信技术，构建一个具备智能交互决策能力的语音商品搜索助手。该助手能够：
- 通过实时语音对话理解用户意图
- 智能判断何时需要与用户交互澄清需求
- 避免不必要的交互，提升用户体验
- 最终输出完整的商品信息（包括图片、详情等）

### 1.2 核心特性

- **实时语音交互**: 支持连续、可打断的语音输入，基于 WebRTC 的双向音频流
- **智能交互决策**: Agent 自主判断何时需要向用户提问以澄清意图
- **多步决策流程**: 
  - 直接商品搜索（意图明确时）
  - 网络搜索 + 澄清（意图不明确时）
  - 网络搜索 + 信息补全（意图明确但需要更多数据时）
- **多模态UI展示**: Web界面实时显示ASR转录、Agent回复、商品图片和详情
- **完整的对话管理**: 支持语音指令控制对话状态（开始、结束、重置）

### 1.3 技术栈

- **后端框架**: Python, FastAPI
- **Agent框架**: `smolagents` (基于 ReAct 框架)
- **实时通信**: `fastrtc` (WebRTC)
- **ASR**: Azure OpenAI `whisper-1` (通过修改版 openai-python)
- **LLM**: Azure OpenAI `gpt-4o-0806` (通过 CustomModel)
- **TTS**: 字节跳动 TTS API (参考 realtime_chat/tts4.py)
- **前端**: HTML, CSS, JavaScript (WebRTC + WebSocket)

---

## 2. 系统架构与数据流

### 2.1 整体架构

```mermaid
graph TB
    A[用户语音输入] --> B[FastRTC WebSocket]
    B --> C[Azure Whisper ASR]
    C --> D[SpeechSearchAgent]
    D --> E{智能决策}
    E -->|意图明确| F[TaobaoMainSearchTool]
    E -->|需要澄清| G[AskUserQuestionTool]
    E -->|需要增强| H[WebSearchTool]
    F --> I[TaobaoItemDetailsTool]
    G --> J[TTS语音播放]
    J --> K1[用户语音回复]
    K1 --> C
    H --> F
    I --> K2[字节跳动TTS]
    K2 --> L[FastRTC音频输出]
    I --> M[Web UI商品展示]
```

### 2.2 数据流转路径

1. **语音输入流**: 用户麦克风 → FastRTC → PCM16音频流 → Azure Whisper WebSocket
2. **ASR处理流**: Whisper实时转录 → interim/final结果 → DataChannel → 前端显示
3. **Agent决策流**: 完整语音指令 → SpeechSearchAgent → 多步决策 → 工具调用
4. **交互处理流**: Agent生成问题 → AskUserQuestionTool → TTS语音播放 → 用户语音回复 → ASR转录
5. **商品搜索流**: TaobaoMainSearchTool → 包含itemId的商品列表 → TaobaoItemDetailsTool → 完整商品信息
6. **语音输出流**: Agent回复文本 → 字节跳动TTS → PCM16音频 → FastRTC → 用户播放
7. **商品展示流**: 商品详情JSON → DataChannel → 前端图片展示

---

## 3. 详细功能与技术实现

### 3.1 项目结构

```
realtime_speech_search/
├── main.py                 # FastAPI应用入口
├── agent_core.py          # SpeechSearchAgent核心逻辑
├── rtc_handler.py         # FastRTC连接和音频流处理
├── custom_tools.py        # 自定义工具集合
├── custom_model.py        # Azure OpenAI CustomModel
├── tts_client.py          # 字节跳动TTS客户端
├── config.py              # 配置文件
├── static/                # 前端静态文件
│   ├── index.html
│   ├── style.css
│   └── app.js
├── prompts/               # Agent提示词模板
│   └── speech_search_agent.yaml
└── cert.pem, key.pem      # SSL证书文件
```

### 3.2 Agent核心实现 (agent_core.py)

#### 3.2.1 SpeechSearchAgent类设计

基于 `smolagents.MultiStepAgent`，增强交互决策能力：

```python
class SpeechSearchAgent(MultiStepAgent):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 状态跟踪
        self._has_searched_products = False
        self._has_detailed_info = False
        self._pending_item_ids = []
        
        # 交互策略配置
        self.interaction_threshold = {
            'query_clarity_score': 0.6,
            'result_confidence_score': 0.7,
            'max_interactions_per_session': 3
        }
```

#### 3.2.2 智能决策逻辑

```python
def _determine_next_action(self, task: str) -> dict:
    """根据当前状态和任务确定下一步行动"""
    if self.current_phase == "analysis":
        return {
            "tool_name": "query_analysis",
            "arguments": {"query": task},
            "reason": "分析用户查询的搜索意图"
        }
    elif self.current_phase == "clarification":
        return {
            "tool_name": "ask_user_question", 
            "arguments": {
                "original_query": task,
                "missing_info": json.dumps(missing_info),
                "use_web_search": False
            },
            "reason": "向用户询问更多信息"
        }
    # ... 其他决策分支
```

#### 3.2.3 强制商品详情获取

```python
def execute_tool_call(self, tool_name: str, arguments: dict):
    """执行工具调用，自动处理商品详情获取"""
    result = super().execute_tool_call(tool_name, arguments)
    
    # 搜索完成后自动获取详情
    if tool_name == "taobao_main_search":
        self._extract_item_ids_from_search_result(result)
        self._has_searched_products = True
        
    elif tool_name == "taobao_item_details":
        self._has_detailed_info = True
        
    return result
```

### 3.3 自定义工具集 (custom_tools.py)

#### 3.3.1 核心工具列表

1. **TaobaoMainSearchTool**: 淘宝主搜接口调用（返回包含itemId的商品列表）
2. **TaobaoItemDetailsTool**: 商品详情获取（必须调用，基于itemId）
3. **AskUserQuestionTool**: 纯语音交互问答（无选项，直接TTS播放问题）
4. **QueryAnalysisTool**: 查询意图分析
5. **SearchSummaryTool**: 网络搜索增强（备用）

#### 3.3.2 AskUserQuestionTool实现

```python
class AskUserQuestionTool(Tool):
    name = "ask_user_question"
    description = "当需要澄清用户需求时，向用户提问，通过语音交互获取回复"

    def forward(self, original_query: str, missing_info: str, use_web_search: bool = False) -> str:
        """生成澄清问题，通过TTS播放并等待语音回复"""
        # 分析缺失信息类型
        missing_list = json.loads(missing_info)

        # 生成针对性问题（纯文本，无选项）
        if "category" in missing_list:
            question = f"您想要搜索什么类型的商品？比如服装、数码产品、家居用品等。"
        elif "price_range" in missing_list:
            question = f"您的预算大概是多少？请告诉我价格范围。"
        elif "color" in missing_list:
            question = f"您希望是什么颜色的？"
        elif "size" in missing_list:
            question = f"您需要什么尺寸的？"
        elif "brand" in missing_list:
            question = f"您有偏好的品牌吗？"
        elif "occasion" in missing_list:
            question = f"您打算在什么场合使用？"
        elif "season" in missing_list:
            question = f"您是为哪个季节准备的？"
        else:
            question = f"关于'{original_query}'，您能提供更多具体信息吗？"

        return json.dumps({
            "question": question,
            "requires_user_input": True,
            "interaction_type": "voice_only"
        }, ensure_ascii=False)
```

### 3.4 实时通信实现 (rtc_handler.py)

#### 3.4.1 FastRTC音频处理

```python
class SpeechAudioHandler(AsyncStreamHandler):
    def __init__(self):
        super().__init__(
            expected_layout="mono",
            output_sample_rate=48000,
            input_sample_rate=24000
        )
        self.ws = None  # Azure Whisper WebSocket
        self.session = None  # aiohttp session
        self.agent = None  # SpeechSearchAgent实例
        self.tts_client = ByteDanceTTS(...)  # TTS客户端
        
        # 队列管理
        self.asr_output_queue = asyncio.Queue()
        self.agent_input_queue = asyncio.Queue()
        self.tts_input_queue = asyncio.Queue()
        self.tts_output_queue = asyncio.Queue()
```

#### 3.4.2 ASR集成

```python
async def start_up(self):
    """建立Azure Whisper WebSocket连接"""
    azure_endpoint = "wss://idealab.alibaba-inc.com/api/openai/realtime?model=gpt-4o-realtime-preview-1001"
    headers = {"api-key": "27db1fc058c1861870be4c21a7f93cdc"}
    
    self.session = aiohttp.ClientSession()
    self.ws = await self.session.ws_connect(azure_endpoint, headers=headers)
    
    # 配置session参数
    session_update = {
        "type": "session.update",
        "session": {
            "modalities": ["text", "audio"],
            "input_audio_format": "pcm16",
            "input_audio_transcription": {
                "model": "whisper-1",
                "language": "zh"
            },
            "turn_detection": {
                "type": "server_vad",
                "threshold": 0.8,
                "silence_duration_ms": 500
            }
        }
    }
    await self.ws.send_str(json.dumps(session_update))
    
    # 启动消息处理任务
    asyncio.create_task(self.receive_asr_messages())
    asyncio.create_task(self.tts_client.run_tts_worker(self.tts_input_queue, self.tts_output_queue))
```

#### 3.4.3 打断处理

```python
async def handle_interruption(self, transcript: str, conv_id: str):
    """处理用户打断"""
    # 检查是否可以打断
    if await self.agent.can_interrupt():
        success = await self.agent.interrupt()
        
    if self.tts_client.can_interrupt(self.tts_input_queue, self.tts_output_queue):
        # 发送打断事件
        interrupt_event = InterruptEvent()
        interrupt_event.type = "[interruption]"
        interrupt_event.id = conv_id
        interrupt_event.content = "[Conversation interrupted]"
        
        await self.tts_output_queue.put(AdditionalOutputs(interrupt_event))
        await self.tts_client.interrupt(self.tts_input_queue, self.tts_output_queue)
    
    # 处理新的用户输入
    asyncio.create_task(self.process_agent_response(transcript, conv_id))
```

### 3.5 TTS集成 (tts_client.py)

#### 3.5.1 字节跳动TTS配置

```python
class ByteDanceTTS:
    def __init__(self):
        self.appid = "4301754327"
        self.token = "iTyYQaoJGz1dNspdaXdBx0LltSwIHjF5"
        self.cluster = "volcano_tts"
        self.voice_type = "zh_male_M392_conversation_wvae_bigtts"
        self.min_text_length = 30
        self.sentence_endings = {'.', '。', '!', '！', '?', '？', '\n'}

        # 打断控制
        self._interrupt_generation = 0
        self._task_status = TaskStatus.IDLE
```

#### 3.5.2 打断机制

```python
async def interrupt(self, output_queue: asyncio.Queue, voice_queue: asyncio.Queue) -> bool:
    """打断当前TTS处理"""
    async with self._task_lock:
        # 增加打断代数
        self._interrupt_generation += 1
        self._interrupt_event.set()

        # 关闭WebSocket连接
        if self._websocket and self._websocket.close_code is None:
            await self._websocket.close()

        # 清空队列
        while not output_queue.empty():
            output_queue.get_nowait()
        while not voice_queue.empty():
            voice_queue.get_nowait()

        # 取消当前任务
        if self._current_conversion_task:
            self._current_conversion_task.cancel()

        return True
```

### 3.6 前端实现 (static/)

#### 3.6.1 WebRTC连接管理

```javascript
class SpeechUI {
    constructor() {
        this.pc = null;  // RTCPeerConnection
        this.dataChannel = null;
        this.audioContext = null;
        this.mediaStream = null;

        this.setupWebRTC();
    }

    async setupWebRTC() {
        // 建立RTCPeerConnection
        this.pc = new RTCPeerConnection({
            iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
        });

        // 设置数据通道
        this.dataChannel = this.pc.createDataChannel('messages');
        this.dataChannel.onmessage = (event) => {
            this.handleDataChannelMessage(JSON.parse(event.data));
        };

        // 获取麦克风权限
        this.mediaStream = await navigator.mediaDevices.getUserMedia({
            audio: {
                sampleRate: 48000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            }
        });

        // 添加音频轨道
        this.mediaStream.getAudioTracks().forEach(track => {
            this.pc.addTrack(track, this.mediaStream);
        });

        // 监听远程音频流（TTS输出）
        this.pc.ontrack = (event) => {
            const audio = new Audio();
            audio.srcObject = event.streams[0];
            audio.play();
        };
    }
}
```

#### 3.6.2 商品展示组件

```javascript
displayProductResults(products) {
    const container = document.getElementById('product-container');
    container.innerHTML = '';

    products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        productCard.innerHTML = `
            <img src="${product.image_url}" alt="${product.title}"
                 onerror="this.src='placeholder.jpg'">
            <div class="product-info">
                <h3>${product.title}</h3>
                <p class="price">¥${product.price}</p>
                <p class="item-id">商品ID: ${product.itemId}</p>
                <a href="${product.detail_url}" target="_blank" class="view-btn">查看详情</a>
            </div>
        `;
        container.appendChild(productCard);
    });
}
```

#### 3.6.3 纯语音交互处理

```javascript
handleAgentInteraction(data) {
    // 显示Agent问题
    this.addMessage('agent', data.question);

    // 显示语音交互提示
    this.showVoiceInteractionPrompt();

    // 设置等待语音回复状态
    this.setWaitingForVoiceResponse(true);

    // 存储当前交互ID，用于后续语音回复
    this.currentInteractionId = data.interaction_id;
}

showVoiceInteractionPrompt() {
    const promptDiv = document.createElement('div');
    promptDiv.className = 'voice-prompt';
    promptDiv.innerHTML = `
        <div class="voice-indicator">
            <div class="pulse-animation"></div>
            <span>🎤 请语音回复...</span>
        </div>
    `;
    this.messagesContainer.appendChild(promptDiv);
}

setWaitingForVoiceResponse(waiting) {
    this.isWaitingForVoiceResponse = waiting;
    const indicator = document.querySelector('.voice-indicator');
    if (indicator) {
        indicator.style.display = waiting ? 'flex' : 'none';
    }
}
```

---

## 4. 核心配置与环境

### 4.1 环境变量配置 (config.py)

```python
import os
from dotenv import load_dotenv

load_dotenv()

# Azure OpenAI配置
AZURE_API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "27db1fc058c1861870be4c21a7f93cdc")
AZURE_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api")
AZURE_MODEL_NAME = os.getenv("AZURE_OPENAI_MODEL_NAME", "gpt-4o-0806")

# 字节跳动TTS配置
TTS_APPID = "4301754327"
TTS_TOKEN = "iTyYQaoJGz1dNspdaXdBx0LltSwIHjF5"
TTS_CLUSTER = "volcano_tts"
TTS_VOICE_TYPE = "zh_male_M392_conversation_wvae_bigtts"

# 搜索API配置
SERPER_API_KEY = os.getenv("SERPER_API_KEY", "4b1915a54600d6c6a2d84742f0332346dbb0a6d6")

# SSL配置
SSL_CERT_FILE = os.getenv("SSL_CERT_FILE", "./cert.pem")
SSL_KEY_FILE = os.getenv("SSL_KEY_FILE", "./key.pem")

# 服务器配置
HOST = "0.0.0.0"
PORT = 7878
```

### 4.2 Agent提示词模板 (prompts/speech_search_agent.yaml)

```yaml
system_prompt: |
  你是一个专业的商品搜索助手，具备以下能力：

  **核心原则**：
  1. 智能判断何时需要与用户交互，避免不必要的提问
  2. 所有搜索任务最终必须输出完整的商品信息（包括图片、详情）
  3. 优先使用淘宝搜索，必要时使用网络搜索增强查询

  **决策流程**：
  1. 分析用户查询的明确度和完整性
  2. 如果意图明确且信息充足 → 直接搜索商品
  3. 如果意图不明确 → 网络搜索 + 向用户澄清
  4. 如果意图明确但需要更多数据 → 网络搜索 + 信息补全

  **工具使用规则**：
  1. 使用 taobao_main_search 搜索商品后，必须调用 taobao_item_details 获取详情
  2. 使用 ask_user_question 时，生成自然语言问题，通过TTS播放，等待语音回复
  3. 使用 web_search 增强查询时，重点关注商品推荐信息
  4. 确保所有搜索结果包含完整的itemId字段

  **交互策略**：
  - 每次会话最多3次语音交互
  - 问题要具体、有针对性，适合语音表达
  - 纯语音问答，无选项按钮
  - 根据用户语音回复动态调整搜索策略

tools:
  - taobao_main_search
  - taobao_item_details
  - ask_user_question
  - query_analysis
  - web_search
  - search_summary
  - final_answer

max_steps: 8
verbosity_level: 1
```

---

## 5. API接口文档

### 5.1 WebSocket接口

#### 5.1.1 连接端点
- **URL**: `wss://domain.com/ws/{session_id}`
- **协议**: WebRTC + WebSocket

#### 5.1.2 消息格式

**客户端 → 服务器**:
```json
{
  "type": "start_search",
  "query": "用户查询内容"
}

{
  "type": "voice_response",
  "interaction_id": "interaction_123",
  "response_text": "用户语音回复转录文本"
}
```

**服务器 → 客户端**:
```json
{
  "type": "asr_interim",
  "text": "正在识别的文字..."
}

{
  "type": "asr_final",
  "text": "最终识别结果"
}

{
  "type": "agent_interaction",
  "interaction_id": "interaction_123",
  "question": "Agent的问题（将通过TTS播放）",
  "interaction_type": "voice_only"
}

{
  "type": "search_completed",
  "result": "[{\"itemId\":\"商品ID\",\"title\":\"商品标题\",\"price\":\"价格\",\"image_url\":\"图片URL\",\"detail_url\":\"详情链接\"}]"
}

{
  "type": "status_update",
  "status": {
    "step": "analyzing_query",
    "query": "用户查询",
    "analysis": {...}
  }
}
```

### 5.2 音频流接口

#### 5.2.1 输入音频格式
- **格式**: PCM16
- **采样率**: 48000 Hz
- **声道**: 单声道
- **编码**: 16-bit

#### 5.2.2 输出音频格式
- **格式**: PCM16
- **采样率**: 48000 Hz
- **声道**: 单声道
- **来源**: 字节跳动TTS

---

## 6. 开发里程碑与测试

### 6.1 开发阶段

**阶段1: 基础架构搭建 (1周)**
- [ ] FastAPI + FastRTC 基础服务
- [ ] Azure Whisper ASR 集成
- [ ] 字节跳动TTS集成
- [ ] 基础WebRTC前端

**阶段2: Agent核心开发 (1.5周)**
- [ ] SpeechSearchAgent实现
- [ ] 自定义工具开发
- [ ] 智能决策逻辑
- [ ] 商品详情强制获取

**阶段3: 交互功能完善 (1周)**
- [ ] AskUserQuestionTool
- [ ] Web UI交互界面
- [ ] 打断机制实现
- [ ] 状态管理优化

**阶段4: 集成测试 (1周)**
- [ ] 端到端功能测试
- [ ] 语音交互测试
- [ ] 商品展示测试
- [ ] 性能优化

### 6.2 测试用例

**基础功能测试**:
1. 明确查询: "我要买红色连衣裙" → 直接搜索 → 返回包含itemId的结果
2. 模糊查询: "春游要带什么" → 语音澄清交互 → 搜索
3. 推荐查询: "秋天水果推荐" → 网络增强 → 商品搜索 → 包含itemId

**交互功能测试**:
1. 语音问答: Agent TTS播放问题 → 用户语音回复 → ASR转录
2. 多轮交互: 连续语音问答流程
3. 打断测试: TTS播放时用户说话
4. 对话重置: "重新开始"指令

**商品展示测试**:
1. itemId字段完整显示
2. 图片正常显示
3. 详情链接可点击
4. 价格信息准确
5. 多商品网格布局

---

## 7. 部署与运维

### 7.1 部署要求

**硬件要求**:
- CPU: 4核以上
- 内存: 8GB以上
- 存储: 50GB以上
- 网络: 稳定的互联网连接

**软件依赖**:
```bash
pip install fastapi uvicorn fastrtc smolagents
pip install aiohttp websockets pydub numpy
pip install python-multipart jinja2 python-dotenv
```

**SSL证书**:
- 生产环境必须配置SSL证书
- 支持自签名证书（开发环境）
- 证书路径: `./cert.pem`, `./key.pem`

### 7.2 启动命令

```bash
# 开发环境
python main.py

# 生产环境（带SSL）
python main.py --ssl-cert ./cert.pem --ssl-key ./key.pem

# 指定端口
python main.py --port 8080
```

### 7.3 监控指标

- WebSocket连接数
- ASR转录准确率
- Agent响应时间
- TTS合成延迟
- 用户交互成功率
- 商品搜索成功率

---

## 8. 风险评估与缓解

### 8.1 技术风险

**风险1: ASR识别准确率**
- 缓解: 多轮确认机制，支持文本输入备用
- 监控: 实时转录准确率统计

**风险2: TTS延迟过高**
- 缓解: 文本分段处理，流式TTS输出
- 监控: TTS响应时间监控

**风险3: 网络连接不稳定**
- 缓解: 自动重连机制，离线模式支持
- 监控: 连接稳定性统计

### 8.2 业务风险

**风险1: 语音交互理解偏差**
- 缓解: 智能交互阈值调优，语音确认机制，交互次数限制
- 监控: 语音交互成功率统计，用户满意度

**风险2: 商品搜索结果不准确或缺少itemId**
- 缓解: 多重搜索策略，强制itemId验证，结果质量评估
- 监控: 用户满意度反馈，itemId完整性检查

---

## 9. 后续优化方向（此次项目暂不涉及）

### 9.1 功能增强
- 多语言支持
- 语音情感识别
- 个性化推荐
- 购物车集成

### 9.2 性能优化
- 边缘计算部署
- 模型量化优化
- 缓存策略优化
- 并发处理能力提升

### 9.3 用户体验
- 语音指令扩展
- 手势控制支持
- AR/VR集成
- 多模态交互

---

**文档版本**: 2.0
**最后更新**: 2025-06-09
**审核状态**: 待技术评审
