# 实时语音商品搜索系统 - 模块状态和接口文档

**版本**: 1.0
**日期**: 2025-06-09
**基于**: 技术方案_优化.md (Version 4.0)

## 1. 文档概述

本文档详细定义了实时语音商品搜索系统各模块的状态管理、接口规范和数据流，用于指导整体项目的设计、开发和管理。

### 1.1 文档目标

* **统一接口标准**: 定义各模块间的通信协议和数据格式
* **状态管理规范**: 明确各模块的状态定义和状态转换规则
* **开发指导**: 为开发团队提供详细的实现指南
* **项目管理**: 支持项目进度跟踪和质量控制

### 1.2 模块架构概览

```mermaid
graph TB
    subgraph "前端层"
        UI[Web UI Module]
        UI_VC[VoiceUIController]
        UI_PC[ProductDisplayController]
        UI_IH[InterruptionHandler]
    end
    
    subgraph "核心服务层"
        MA[Main Agent Module]
        MA_VP[VoiceUICommandParser]
        MA_IM[InterruptionManager]
        MA_TSM[TaskScheduleManager]
    end
    
    subgraph "业务逻辑层"
        SA[Search Agent Module]
        RA[RAG Agent Module]
    end
    
    subgraph "外部服务层"
        ASR[Azure Whisper ASR]
        TTS[ByteDance TTS]
        TB[Taobao API]
        WS[Web Search API]
    end
    
    UI <==> MA
    MA <==> SA
    MA <==> RA
    MA <==> ASR
    MA <==> TTS
    SA <==> TB
    RA <==> WS
```

---

## 2. Web UI Module - 状态和接口定义

### 2.1 模块状态定义

#### 2.1.1 连接状态 (ConnectionState)
```typescript
enum ConnectionState {
    DISCONNECTED = "disconnected",
    CONNECTING = "connecting", 
    CONNECTED = "connected",
    RECONNECTING = "reconnecting",
    ERROR = "error"
}
```

#### 2.1.2 语音状态 (VoiceState)
```typescript
enum VoiceState {
    IDLE = "idle",                    // 空闲状态
    LISTENING = "listening",          // 监听用户语音
    PROCESSING = "processing",        // 处理语音输入
    PLAYING_TTS = "playing_tts",      // 播放TTS音频
    INTERRUPTED = "interrupted"       // 被打断状态
}
```

#### 2.1.3 UI控制状态 (UIControlState)
```typescript
interface UIControlState {
    currentPage: number;              // 当前页码
    selectedProductIndex: number;     // 选中商品索引
    scrollPosition: number;           // 滚动位置
    isDetailView: boolean;           // 是否在详情视图
    totalProducts: number;           // 商品总数
    viewMode: 'grid' | 'list';       // 显示模式
}
```

#### 2.1.4 商品展示状态 (ProductDisplayState)
```typescript
interface ProductDisplayState {
    products: Product[];             // 商品列表
    loading: boolean;               // 加载状态
    error: string | null;           // 错误信息
    hasMore: boolean;               // 是否有更多商品
    lastQuery: string;              // 最后查询内容
    searchId: string;               // 搜索ID
}
```

### 2.2 核心接口定义

#### 2.2.1 WebSocket 消息接口

**接收消息格式 (服务器 → 客户端)**:
```typescript
// ASR转录结果
interface ASRTranscriptMessage {
    type: "asr_transcript";
    data: {
        text: string;
        is_final: boolean;
        confidence: number;
        timestamp: string;
    };
}

// Agent交互请求
interface AgentInteractionMessage {
    type: "agent_interaction";
    data: {
        interaction_id: string;
        question: string;
        interaction_type: "voice_only" | "voice_with_options";
        options?: string[];
        timeout?: number;
    };
}

// 搜索完成结果
interface SearchCompletedMessage {
    type: "search_completed";
    data: {
        search_id: string;
        results: Product[];
        total_count: number;
        has_more: boolean;
        query: string;
    };
}

// UI控制指令
interface UICommandMessage {
    type: "ui_command";
    data: {
        command: "next_page" | "prev_page" | "scroll_left" | "scroll_right" | "select_item";
        params?: {
            index?: number;
            direction?: number;
            animation?: boolean;
        };
    };
}

// 打断控制
interface InterruptionMessage {
    type: "interruption";
    data: {
        action: "stop_tts" | "clear_queue" | "resume";
        generation_id?: string;
    };
}

// 状态更新
interface StatusUpdateMessage {
    type: "status_update";
    data: {
        module: string;
        status: string;
        progress?: number;
        message?: string;
        timestamp: string;
    };
}
```

**发送消息格式 (客户端 → 服务器)**:
```typescript
// 开始搜索
interface StartSearchMessage {
    type: "start_search";
    data: {
        query: string;
        search_type?: "product" | "general";
        context?: any;
    };
}

// 语音回复
interface VoiceResponseMessage {
    type: "voice_response";
    data: {
        interaction_id: string;
        response_text: string;
        confidence: number;
    };
}

// 语音UI控制指令
interface UIVoiceCommandMessage {
    type: "ui_voice_command";
    data: {
        command: string;
        raw_text: string;
        context: UIControlState;
        timestamp: string;
    };
}

// 打断信号
interface InterruptSignalMessage {
    type: "interrupt_signal";
    data: {
        timestamp: string;
        reason: "user_speech" | "manual" | "timeout";
    };
}

// 用户操作事件
interface UserActionMessage {
    type: "user_action";
    data: {
        action: "click" | "scroll" | "select" | "navigate";
        target: string;
        params?: any;
    };
}
```

#### 2.2.2 商品数据接口
```typescript
interface Product {
    itemId: string;                  // 商品ID
    title: string;                   // 商品标题
    price: string;                   // 价格
    image_url: string;               // 图片URL
    detail_url: string;              // 详情链接
    shop_name?: string;              // 店铺名称
    rating?: number;                 // 评分
    sales_count?: number;            // 销量
    tags?: string[];                 // 标签
    description?: string;            // 描述
    specifications?: {               // 规格参数
        [key: string]: string;
    };
}
```

### 2.3 状态转换规则

#### 2.3.1 连接状态转换
```mermaid
stateDiagram-v2
    [*] --> DISCONNECTED
    DISCONNECTED --> CONNECTING: connect()
    CONNECTING --> CONNECTED: onopen
    CONNECTING --> ERROR: onerror
    CONNECTED --> RECONNECTING: connection_lost
    RECONNECTING --> CONNECTED: reconnect_success
    RECONNECTING --> ERROR: reconnect_failed
    ERROR --> CONNECTING: retry()
    CONNECTED --> DISCONNECTED: disconnect()
```

#### 2.3.2 语音状态转换
```mermaid
stateDiagram-v2
    [*] --> IDLE
    IDLE --> LISTENING: start_listening()
    LISTENING --> PROCESSING: voice_detected
    PROCESSING --> PLAYING_TTS: tts_start
    PLAYING_TTS --> INTERRUPTED: interrupt_signal
    INTERRUPTED --> PROCESSING: resume_processing
    PROCESSING --> IDLE: processing_complete
    PLAYING_TTS --> IDLE: tts_complete
```

---

## 3. Main Agent Module - 状态和接口定义

### 3.1 模块状态定义

#### 3.1.1 Agent状态 (AgentState)
```typescript
enum AgentState {
    INITIALIZING = "initializing",
    IDLE = "idle",
    ANALYZING = "analyzing",
    SEARCHING = "searching", 
    INTERACTING = "interacting",
    PROCESSING_UI_COMMAND = "processing_ui_command",
    ERROR = "error",
    SHUTTING_DOWN = "shutting_down"
}
```

#### 3.1.2 对话状态 (ConversationState)
```typescript
interface ConversationState {
    session_id: string;
    current_query: string;
    conversation_history: ConversationTurn[];
    user_intent: UserIntent;
    interaction_count: number;
    last_interaction_time: string;
    context: {
        search_results?: Product[];
        ui_state?: UIControlState;
        user_preferences?: UserPreferences;
    };
}

interface ConversationTurn {
    turn_id: string;
    timestamp: string;
    user_input: string;
    agent_response: string;
    intent: string;
    confidence: number;
}
```

#### 3.1.3 任务状态 (TaskState)
```typescript
interface TaskState {
    task_id: string;
    task_type: "search" | "rag" | "ui_control" | "interaction";
    status: TaskStatus;
    priority: number;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    progress: number;
    result?: any;
    error?: string;
}

enum TaskStatus {
    CREATED = "created",
    QUEUED = "queued", 
    RUNNING = "running",
    PAUSED = "paused",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    FAILED = "failed"
}
```

### 3.2 核心接口定义

#### 3.2.1 内部API接口

**Search Agent调用接口**:
```typescript
interface SearchAgentAPI {
    // 执行搜索
    search(request: SearchRequest): Promise<SearchResponse>;
    
    // 获取搜索状态
    getSearchStatus(search_id: string): Promise<TaskState>;
    
    // 取消搜索
    cancelSearch(search_id: string): Promise<boolean>;
}

interface SearchRequest {
    query: string;
    search_id: string;
    options?: {
        page?: number;
        limit?: number;
        sort_by?: string;
        filters?: any;
        exclude_items?: string[];
    };
}

interface SearchResponse {
    search_id: string;
    products: Product[];
    total_count: number;
    has_more: boolean;
    relevance_score: number;
    processing_time: number;
}
```

**RAG Agent调用接口**:
```typescript
interface RAGAgentAPI {
    // 执行RAG查询
    query(request: RAGRequest): Promise<RAGResponse>;
    
    // 获取处理状态
    getStatus(query_id: string): Promise<TaskState>;
}

interface RAGRequest {
    query: string;
    query_id: string;
    context?: string;
    max_results?: number;
}

interface RAGResponse {
    query_id: string;
    summary: string;
    suggested_keywords: string[];
    confidence: number;
    sources: string[];
    processing_time: number;
}
```

#### 3.2.2 语音处理接口

**ASR服务接口**:
```typescript
interface ASRServiceAPI {
    // 开始语音识别
    startRecognition(config: ASRConfig): Promise<string>;
    
    // 停止语音识别
    stopRecognition(session_id: string): Promise<void>;
    
    // 获取识别结果
    getTranscript(session_id: string): Promise<ASRResult>;
}

interface ASRConfig {
    language: string;
    sample_rate: number;
    format: string;
    enable_interim_results: boolean;
}

interface ASRResult {
    session_id: string;
    transcript: string;
    confidence: number;
    is_final: boolean;
    alternatives?: Array<{
        transcript: string;
        confidence: number;
    }>;
}
```

**TTS服务接口**:
```typescript
interface TTSServiceAPI {
    // 合成语音
    synthesize(request: TTSRequest): Promise<TTSResponse>;
    
    // 停止合成
    stopSynthesis(generation_id: string): Promise<void>;
    
    // 获取合成状态
    getStatus(generation_id: string): Promise<TTSStatus>;
}

interface TTSRequest {
    text: string;
    generation_id: string;
    voice_config: {
        voice_type: string;
        speed: number;
        pitch: number;
        volume: number;
    };
    stream: boolean;
}

interface TTSResponse {
    generation_id: string;
    audio_url?: string;
    audio_stream?: ReadableStream;
    duration: number;
}

interface TTSStatus {
    generation_id: string;
    status: "queued" | "processing" | "completed" | "failed";
    progress: number;
    error?: string;
}
```

### 3.3 状态管理机制

#### 3.3.1 状态持久化
```typescript
interface StateManager {
    // 保存状态
    saveState(session_id: string, state: any): Promise<void>;
    
    // 恢复状态
    restoreState(session_id: string): Promise<any>;
    
    // 清理状态
    clearState(session_id: string): Promise<void>;
    
    // 状态快照
    createSnapshot(session_id: string): Promise<string>;
    
    // 恢复快照
    restoreSnapshot(session_id: string, snapshot_id: string): Promise<void>;
}
```

#### 3.3.2 状态同步机制
```typescript
interface StateSyncManager {
    // 同步状态到前端
    syncToFrontend(session_id: string, state_diff: any): Promise<void>;
    
    // 从前端接收状态更新
    receiveFromFrontend(session_id: string, state_update: any): Promise<void>;
    
    // 状态冲突解决
    resolveConflict(session_id: string, conflicts: StateConflict[]): Promise<any>;
}

interface StateConflict {
    field: string;
    frontend_value: any;
    backend_value: any;
    timestamp_diff: number;
}
```

---

## 4. Search Agent Module - 状态和接口定义

### 4.1 模块状态定义

#### 4.1.1 搜索状态 (SearchState)
```typescript
enum SearchState {
    IDLE = "idle",
    QUERY_REWRITING = "query_rewriting",
    SEARCHING = "searching",
    RELEVANCE_CHECKING = "relevance_checking",
    DETAIL_FETCHING = "detail_fetching",
    COMPLETED = "completed",
    FAILED = "failed"
}
```

#### 4.1.2 查询处理状态 (QueryProcessingState)
```typescript
interface QueryProcessingState {
    original_query: string;
    rewritten_queries: string[];
    current_attempt: number;
    max_attempts: number;
    relevance_scores: number[];
    best_query: string;
    processing_time: number;
}
```

### 4.2 核心接口定义

#### 4.2.1 搜索工具接口
```typescript
interface TaobaoMainSearchTool {
    search(query: string, options?: SearchOptions): Promise<SearchResult>;
}

interface TaobaoItemDetailsTool {
    getDetails(item_ids: string[]): Promise<ProductDetail[]>;
}

interface SearchOptions {
    page?: number;
    limit?: number;
    sort?: 'relevance' | 'price_asc' | 'price_desc' | 'sales';
    category?: string;
    price_range?: {
        min: number;
        max: number;
    };
}

interface SearchResult {
    items: SearchItem[];
    total_count: number;
    page: number;
    has_more: boolean;
    search_time: number;
}

interface SearchItem {
    item_id: string;
    title: string;
    price: string;
    image_url: string;
    shop_name: string;
    sales_count: number;
    location: string;
}

interface ProductDetail {
    item_id: string;
    title: string;
    price: string;
    original_price?: string;
    images: string[];
    detail_url: string;
    shop_info: {
        shop_id: string;
        shop_name: string;
        shop_rating: number;
    };
    specifications: {
        [key: string]: string;
    };
    description: string;
    rating: number;
    review_count: number;
    sales_count: number;
}
```

### 4.3 相关性判断接口
```typescript
interface RelevanceJudge {
    // 判断搜索结果相关性
    judgeRelevance(query: string, results: SearchItem[]): Promise<RelevanceResult>;
    
    // 获取相关性阈值
    getThreshold(query_type: string): number;
}

interface RelevanceResult {
    overall_score: number;
    item_scores: Array<{
        item_id: string;
        score: number;
        reasons: string[];
    }>;
    suggestions: string[];
}
```

---

## 5. RAG Agent Module - 状态和接口定义

### 5.1 模块状态定义

#### 5.1.1 RAG处理状态 (RAGState)
```typescript
enum RAGState {
    IDLE = "idle",
    WEB_SEARCHING = "web_searching",
    CONTENT_PROCESSING = "content_processing", 
    SUMMARIZING = "summarizing",
    KEYWORD_EXTRACTING = "keyword_extracting",
    COMPLETED = "completed",
    FAILED = "failed"
}
```

### 5.2 核心接口定义

#### 5.2.1 网络搜索接口
```typescript
interface WebSearchTool {
    search(query: string, options?: WebSearchOptions): Promise<WebSearchResult>;
}

interface WebSearchOptions {
    num_results?: number;
    language?: string;
    region?: string;
    time_range?: 'day' | 'week' | 'month' | 'year';
}

interface WebSearchResult {
    results: WebSearchItem[];
    total_count: number;
    search_time: number;
}

interface WebSearchItem {
    title: string;
    url: string;
    snippet: string;
    content?: string;
    published_date?: string;
    source: string;
}
```

#### 5.2.2 内容总结接口
```typescript
interface SearchSummaryTool {
    summarize(content: string, query: string): Promise<SummaryResult>;
}

interface SummaryResult {
    summary: string;
    key_points: string[];
    confidence: number;
    word_count: number;
}
```

---

## 6. 错误处理和监控

### 6.1 错误代码定义
```typescript
enum ErrorCode {
    // 连接错误 (1000-1099)
    CONNECTION_FAILED = 1001,
    CONNECTION_TIMEOUT = 1002,
    CONNECTION_LOST = 1003,
    
    // 认证错误 (1100-1199)
    AUTH_FAILED = 1101,
    TOKEN_EXPIRED = 1102,
    PERMISSION_DENIED = 1103,
    
    // 语音处理错误 (1200-1299)
    ASR_SERVICE_ERROR = 1201,
    TTS_SERVICE_ERROR = 1202,
    AUDIO_FORMAT_ERROR = 1203,
    
    // 搜索错误 (1300-1399)
    SEARCH_API_ERROR = 1301,
    SEARCH_TIMEOUT = 1302,
    INVALID_QUERY = 1303,
    
    // UI控制错误 (1400-1499)
    INVALID_UI_COMMAND = 1401,
    UI_STATE_ERROR = 1402,
    
    // 系统错误 (1500-1599)
    INTERNAL_ERROR = 1501,
    SERVICE_UNAVAILABLE = 1502,
    RATE_LIMIT_EXCEEDED = 1503
}
```

### 6.2 监控指标定义
```typescript
interface MonitoringMetrics {
    // 性能指标
    response_time: {
        asr_latency: number;
        tts_latency: number;
        search_latency: number;
        ui_response_time: number;
    };
    
    // 成功率指标
    success_rate: {
        asr_success_rate: number;
        search_success_rate: number;
        ui_command_success_rate: number;
    };
    
    // 用户体验指标
    user_experience: {
        interruption_rate: number;
        session_duration: number;
        user_satisfaction: number;
    };
    
    // 系统资源指标
    system_resources: {
        cpu_usage: number;
        memory_usage: number;
        network_bandwidth: number;
    };
}
```

---

## 7. 部署和配置

### 7.1 环境配置
```typescript
interface SystemConfig {
    // 服务配置
    services: {
        main_agent: ServiceConfig;
        search_agent: ServiceConfig;
        rag_agent: ServiceConfig;
        web_ui: ServiceConfig;
    };
    
    // 外部API配置
    external_apis: {
        azure_whisper: APIConfig;
        bytedance_tts: APIConfig;
        taobao_search: APIConfig;
        serper_search: APIConfig;
    };
    
    // 性能配置
    performance: {
        max_concurrent_sessions: number;
        request_timeout: number;
        retry_attempts: number;
        cache_ttl: number;
    };
}

interface ServiceConfig {
    host: string;
    port: number;
    ssl_enabled: boolean;
    max_connections: number;
    health_check_interval: number;
}

interface APIConfig {
    endpoint: string;
    api_key: string;
    timeout: number;
    rate_limit: number;
}
```

### 7.2 健康检查接口
```typescript
interface HealthCheckAPI {
    // 整体健康状态
    getOverallHealth(): Promise<HealthStatus>;
    
    // 模块健康状态
    getModuleHealth(module: string): Promise<ModuleHealth>;
    
    // 依赖服务状态
    getDependencyHealth(): Promise<DependencyHealth[]>;
}

interface HealthStatus {
    status: "healthy" | "degraded" | "unhealthy";
    timestamp: string;
    uptime: number;
    version: string;
}

interface ModuleHealth {
    module: string;
    status: "healthy" | "degraded" | "unhealthy";
    metrics: any;
    last_error?: string;
}

interface DependencyHealth {
    service: string;
    status: "available" | "degraded" | "unavailable";
    response_time: number;
    last_check: string;
}
```

---

## 8. 总结

本文档定义了实时语音商品搜索系统的完整模块状态和接口规范，包括：

1. **Web UI Module**: 前端状态管理和WebSocket通信协议
2. **Main Agent Module**: 核心调度逻辑和内部API接口
3. **Search Agent Module**: 商品搜索状态和工具接口
4. **RAG Agent Module**: 知识增强处理和网络搜索接口
5. **错误处理**: 统一错误代码和监控指标
6. **部署配置**: 系统配置和健康检查机制

这些定义为项目的设计、开发、测试和运维提供了统一的标准和指导。

---

## 9. 数据流和时序图

### 9.1 标准搜索流程时序图

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Web UI
    participant MA as Main Agent
    participant ASR as Azure Whisper
    participant SA as Search Agent
    participant TB as Taobao API
    participant TTS as ByteDance TTS

    U->>UI: 语音输入
    UI->>MA: WebRTC音频流
    MA->>ASR: 音频数据
    ASR-->>MA: ASR转录结果
    MA->>MA: 意图分析
    MA->>SA: 搜索请求
    SA->>SA: 查询改写
    SA->>TB: API调用
    TB-->>SA: 商品数据
    SA->>SA: 相关性判断
    SA-->>MA: 搜索结果
    MA->>TTS: 回复文本
    TTS-->>MA: TTS音频
    MA->>UI: 搜索结果+音频
    UI->>U: 展示商品+播放语音
```

### 9.2 语音UI控制流程时序图

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Web UI
    participant MA as Main Agent
    participant VP as VoiceUICommandParser
    participant PC as ProductDisplayController

    U->>UI: 语音指令("换一批")
    UI->>MA: 语音数据
    MA->>VP: 解析UI指令
    VP-->>MA: 指令类型+参数
    MA->>MA: 查询当前UI状态
    MA->>SA: 新搜索请求
    SA-->>MA: 新商品数据
    MA->>UI: UI控制指令+新数据
    UI->>PC: 执行UI更新
    PC->>PC: 动画效果
    PC-->>UI: 更新完成
    UI->>U: 视觉反馈+新商品展示
```

### 9.3 流式语音打断流程时序图

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Web UI
    participant MA as Main Agent
    participant IM as InterruptionManager
    participant TTS as ByteDance TTS
    participant ASR as Azure Whisper

    MA->>TTS: 开始TTS播放
    TTS->>UI: 音频流
    UI->>U: 播放语音

    par 并发监听
        MA->>ASR: 监听用户输入
    and
        TTS->>UI: 继续播放
    end

    U->>UI: 打断语音输入
    UI->>MA: 音频数据
    MA->>IM: 检测到打断
    IM->>TTS: 停止当前播放
    IM->>IM: 保存当前状态
    MA->>ASR: 处理新输入
    ASR-->>MA: 新转录结果
    MA->>MA: 处理新请求
```

---

## 10. 状态机详细定义

### 10.1 Web UI状态机

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Connecting: 初始化完成
    Connecting --> Connected: WebSocket连接成功
    Connecting --> ConnectionError: 连接失败
    ConnectionError --> Connecting: 重试连接

    Connected --> Idle: 连接就绪
    Idle --> Listening: 开始语音输入
    Listening --> Processing: 检测到语音
    Processing --> WaitingResponse: 发送请求
    WaitingResponse --> DisplayingResults: 收到搜索结果
    WaitingResponse --> PlayingTTS: 收到TTS音频

    PlayingTTS --> Interrupted: 用户打断
    Interrupted --> Processing: 处理打断输入
    PlayingTTS --> Idle: TTS播放完成
    DisplayingResults --> Idle: 结果展示完成

    state UIControl {
        [*] --> GridView
        GridView --> DetailView: 查看详情
        DetailView --> GridView: 返回列表
        GridView --> ScrollingLeft: 左滑
        GridView --> ScrollingRight: 右滑
        ScrollingLeft --> GridView: 滚动完成
        ScrollingRight --> GridView: 滚动完成
    }

    Connected --> UIControl: UI控制模式
    UIControl --> Connected: 退出控制模式
```

### 10.2 Main Agent状态机

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Idle: 初始化完成

    Idle --> AnalyzingIntent: 收到用户输入
    AnalyzingIntent --> SearchIntent: 识别为搜索意图
    AnalyzingIntent --> UIControlIntent: 识别为UI控制意图
    AnalyzingIntent --> InteractionIntent: 识别为交互意图
    AnalyzingIntent --> RAGIntent: 识别为RAG意图

    SearchIntent --> CallingSearchAgent: 调用搜索代理
    CallingSearchAgent --> ProcessingResults: 处理搜索结果
    ProcessingResults --> GeneratingResponse: 生成回复

    UIControlIntent --> ProcessingUICommand: 处理UI指令
    ProcessingUICommand --> UpdatingUIState: 更新UI状态
    UpdatingUIState --> Idle: 完成UI更新

    InteractionIntent --> GeneratingQuestion: 生成澄清问题
    GeneratingQuestion --> WaitingUserResponse: 等待用户回复
    WaitingUserResponse --> AnalyzingIntent: 收到用户回复

    RAGIntent --> CallingRAGAgent: 调用RAG代理
    CallingRAGAgent --> ProcessingRAGResults: 处理RAG结果
    ProcessingRAGResults --> GeneratingResponse: 生成回复

    GeneratingResponse --> SynthesizingTTS: TTS合成
    SynthesizingTTS --> Idle: 完成回复

    state InterruptionHandling {
        [*] --> Monitoring
        Monitoring --> Detected: 检测到打断
        Detected --> Stopping: 停止当前输出
        Stopping --> Saving: 保存状态
        Saving --> Processing: 处理新输入
        Processing --> [*]: 处理完成
    }

    SynthesizingTTS --> InterruptionHandling: 打断信号
    InterruptionHandling --> AnalyzingIntent: 恢复处理
```

### 10.3 Search Agent状态机

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> ReceivingQuery: 收到搜索请求
    ReceivingQuery --> RewritingQuery: 开始查询改写

    RewritingQuery --> SearchingProducts: 查询改写完成
    SearchingProducts --> CheckingRelevance: 获得搜索结果

    CheckingRelevance --> RelevanceGood: 相关性良好
    CheckingRelevance --> RelevancePoor: 相关性较差

    RelevancePoor --> RewritingQuery: 重新改写查询
    RelevanceGood --> FetchingDetails: 获取商品详情

    FetchingDetails --> FormattingResults: 详情获取完成
    FormattingResults --> Completed: 结果格式化完成
    Completed --> Idle: 返回结果

    state ErrorHandling {
        [*] --> APIError
        APIError --> Retrying: 重试API调用
        Retrying --> Success: 重试成功
        Retrying --> Failed: 重试失败
        Success --> [*]
        Failed --> [*]
    }

    SearchingProducts --> ErrorHandling: API错误
    FetchingDetails --> ErrorHandling: API错误
    ErrorHandling --> Idle: 错误处理完成
```

---

## 11. 缓存和性能优化

### 11.1 缓存策略定义

```typescript
interface CacheStrategy {
    // 搜索结果缓存
    search_cache: {
        ttl: number;                    // 生存时间(秒)
        max_size: number;               // 最大缓存条目数
        key_pattern: string;            // 缓存键模式
        eviction_policy: 'LRU' | 'LFU' | 'FIFO';
    };

    // 商品详情缓存
    product_cache: {
        ttl: number;
        max_size: number;
        key_pattern: string;
        eviction_policy: 'LRU' | 'LFU' | 'FIFO';
    };

    // 用户会话缓存
    session_cache: {
        ttl: number;
        max_size: number;
        key_pattern: string;
        eviction_policy: 'LRU' | 'LFU' | 'FIFO';
    };

    // RAG结果缓存
    rag_cache: {
        ttl: number;
        max_size: number;
        key_pattern: string;
        eviction_policy: 'LRU' | 'LFU' | 'FIFO';
    };
}
```

### 11.2 性能监控接口

```typescript
interface PerformanceMonitor {
    // 记录性能指标
    recordMetric(metric: PerformanceMetric): void;

    // 获取性能统计
    getStats(timeRange: TimeRange): Promise<PerformanceStats>;

    // 设置性能阈值
    setThreshold(metric: string, threshold: number): void;

    // 获取性能告警
    getAlerts(): Promise<PerformanceAlert[]>;
}

interface PerformanceMetric {
    name: string;
    value: number;
    timestamp: string;
    tags?: { [key: string]: string };
}

interface PerformanceStats {
    metrics: {
        [metric_name: string]: {
            avg: number;
            min: number;
            max: number;
            p50: number;
            p95: number;
            p99: number;
            count: number;
        };
    };
    time_range: TimeRange;
}

interface PerformanceAlert {
    metric: string;
    threshold: number;
    current_value: number;
    severity: 'warning' | 'critical';
    timestamp: string;
    description: string;
}

interface TimeRange {
    start: string;
    end: string;
}
```

---

## 12. 安全和权限管理

### 12.1 认证和授权接口

```typescript
interface AuthenticationAPI {
    // 用户认证
    authenticate(credentials: UserCredentials): Promise<AuthResult>;

    // 刷新令牌
    refreshToken(refresh_token: string): Promise<TokenResult>;

    // 验证令牌
    validateToken(access_token: string): Promise<TokenValidation>;

    // 用户登出
    logout(session_id: string): Promise<void>;
}

interface UserCredentials {
    username?: string;
    password?: string;
    oauth_token?: string;
    device_id?: string;
    session_token?: string;
}

interface AuthResult {
    success: boolean;
    user_id?: string;
    access_token?: string;
    refresh_token?: string;
    expires_in?: number;
    permissions?: string[];
    error?: string;
}

interface TokenResult {
    access_token: string;
    expires_in: number;
}

interface TokenValidation {
    valid: boolean;
    user_id?: string;
    permissions?: string[];
    expires_at?: string;
}
```

### 12.2 权限控制接口

```typescript
interface AuthorizationAPI {
    // 检查权限
    checkPermission(user_id: string, resource: string, action: string): Promise<boolean>;

    // 获取用户权限
    getUserPermissions(user_id: string): Promise<Permission[]>;

    // 设置资源权限
    setResourcePermission(resource: string, permissions: ResourcePermission[]): Promise<void>;
}

interface Permission {
    resource: string;
    actions: string[];
    conditions?: PermissionCondition[];
}

interface ResourcePermission {
    user_id?: string;
    role?: string;
    actions: string[];
    conditions?: PermissionCondition[];
}

interface PermissionCondition {
    field: string;
    operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'contains';
    value: any;
}
```

### 12.3 数据安全接口

```typescript
interface DataSecurityAPI {
    // 数据加密
    encrypt(data: string, key_id: string): Promise<string>;

    // 数据解密
    decrypt(encrypted_data: string, key_id: string): Promise<string>;

    // 数据脱敏
    maskSensitiveData(data: any, mask_rules: MaskRule[]): any;

    // 审计日志
    logAuditEvent(event: AuditEvent): Promise<void>;
}

interface MaskRule {
    field: string;
    mask_type: 'partial' | 'full' | 'hash';
    mask_char?: string;
    preserve_length?: boolean;
}

interface AuditEvent {
    event_type: string;
    user_id: string;
    resource: string;
    action: string;
    timestamp: string;
    ip_address?: string;
    user_agent?: string;
    details?: any;
}
```

---

## 13. 测试接口和模拟数据

### 13.1 测试工具接口

```typescript
interface TestingAPI {
    // 创建测试会话
    createTestSession(config: TestSessionConfig): Promise<TestSession>;

    // 模拟用户输入
    simulateUserInput(session_id: string, input: UserInput): Promise<void>;

    // 获取测试结果
    getTestResults(session_id: string): Promise<TestResult>;

    // 清理测试数据
    cleanupTestData(session_id: string): Promise<void>;
}

interface TestSessionConfig {
    test_type: 'unit' | 'integration' | 'e2e';
    modules: string[];
    mock_external_apis: boolean;
    record_interactions: boolean;
    timeout: number;
}

interface TestSession {
    session_id: string;
    config: TestSessionConfig;
    created_at: string;
    status: 'active' | 'completed' | 'failed';
}

interface UserInput {
    type: 'voice' | 'text' | 'ui_action';
    content: string;
    metadata?: any;
}

interface TestResult {
    session_id: string;
    test_cases: TestCase[];
    overall_status: 'passed' | 'failed' | 'partial';
    execution_time: number;
    coverage: TestCoverage;
}

interface TestCase {
    name: string;
    status: 'passed' | 'failed' | 'skipped';
    execution_time: number;
    assertions: TestAssertion[];
    error?: string;
}

interface TestAssertion {
    description: string;
    expected: any;
    actual: any;
    passed: boolean;
}

interface TestCoverage {
    lines_covered: number;
    lines_total: number;
    functions_covered: number;
    functions_total: number;
    branches_covered: number;
    branches_total: number;
}
```

### 13.2 模拟数据生成器

```typescript
interface MockDataGenerator {
    // 生成模拟商品数据
    generateProducts(count: number, category?: string): Product[];

    // 生成模拟用户会话
    generateUserSession(duration: number): ConversationTurn[];

    // 生成模拟语音数据
    generateVoiceInput(text: string, voice_config?: VoiceConfig): AudioData;

    // 生成模拟搜索结果
    generateSearchResults(query: string, count: number): SearchResult;
}

interface VoiceConfig {
    language: string;
    accent: string;
    speed: number;
    noise_level: number;
}

interface AudioData {
    format: string;
    sample_rate: number;
    channels: number;
    duration: number;
    data: ArrayBuffer;
}
```

---

## 14. 部署和运维接口

### 14.1 部署管理接口

```typescript
interface DeploymentAPI {
    // 部署服务
    deployService(config: DeploymentConfig): Promise<DeploymentResult>;

    // 更新服务
    updateService(service_id: string, config: UpdateConfig): Promise<DeploymentResult>;

    // 回滚服务
    rollbackService(service_id: string, version: string): Promise<DeploymentResult>;

    // 获取部署状态
    getDeploymentStatus(deployment_id: string): Promise<DeploymentStatus>;

    // 扩缩容服务
    scaleService(service_id: string, replicas: number): Promise<ScaleResult>;
}

interface DeploymentConfig {
    service_name: string;
    version: string;
    image: string;
    environment: 'dev' | 'staging' | 'prod';
    resources: ResourceRequirements;
    environment_variables: { [key: string]: string };
    health_check: HealthCheckConfig;
}

interface ResourceRequirements {
    cpu: string;
    memory: string;
    storage: string;
    gpu?: string;
}

interface HealthCheckConfig {
    path: string;
    port: number;
    interval: number;
    timeout: number;
    retries: number;
}

interface DeploymentResult {
    deployment_id: string;
    status: 'success' | 'failed' | 'in_progress';
    message: string;
    service_url?: string;
}

interface DeploymentStatus {
    deployment_id: string;
    service_id: string;
    status: 'deploying' | 'running' | 'failed' | 'stopped';
    replicas: {
        desired: number;
        ready: number;
        available: number;
    };
    last_updated: string;
}

interface UpdateConfig {
    version?: string;
    image?: string;
    environment_variables?: { [key: string]: string };
    resources?: ResourceRequirements;
}

interface ScaleResult {
    service_id: string;
    previous_replicas: number;
    new_replicas: number;
    status: 'success' | 'failed' | 'in_progress';
}
```

### 14.2 运维监控接口

```typescript
interface OperationsAPI {
    // 获取服务日志
    getLogs(service_id: string, options: LogOptions): Promise<LogResult>;

    // 获取服务指标
    getMetrics(service_id: string, metrics: string[], timeRange: TimeRange): Promise<MetricResult>;

    // 设置告警规则
    setAlertRule(rule: AlertRule): Promise<string>;

    // 获取告警历史
    getAlerts(filters: AlertFilters): Promise<Alert[]>;

    // 执行运维操作
    executeOperation(operation: Operation): Promise<OperationResult>;
}

interface LogOptions {
    level?: 'debug' | 'info' | 'warn' | 'error';
    start_time?: string;
    end_time?: string;
    limit?: number;
    search?: string;
}

interface LogResult {
    logs: LogEntry[];
    total_count: number;
    has_more: boolean;
}

interface LogEntry {
    timestamp: string;
    level: string;
    message: string;
    service: string;
    module?: string;
    trace_id?: string;
    metadata?: any;
}

interface MetricResult {
    metrics: {
        [metric_name: string]: MetricDataPoint[];
    };
    time_range: TimeRange;
}

interface MetricDataPoint {
    timestamp: string;
    value: number;
    tags?: { [key: string]: string };
}

interface AlertRule {
    name: string;
    metric: string;
    condition: 'gt' | 'lt' | 'eq' | 'ne';
    threshold: number;
    duration: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    notification_channels: string[];
}

interface AlertFilters {
    service_id?: string;
    severity?: string;
    status?: 'active' | 'resolved';
    start_time?: string;
    end_time?: string;
}

interface Alert {
    alert_id: string;
    rule_name: string;
    service_id: string;
    severity: string;
    status: 'active' | 'resolved';
    triggered_at: string;
    resolved_at?: string;
    message: string;
    current_value: number;
    threshold: number;
}

interface Operation {
    type: 'restart' | 'stop' | 'start' | 'cleanup' | 'backup';
    service_id: string;
    parameters?: any;
}

interface OperationResult {
    operation_id: string;
    status: 'success' | 'failed' | 'in_progress';
    message: string;
    started_at: string;
    completed_at?: string;
}
```

---

## 15. 总结和使用指南

### 15.1 文档使用指南

本模块状态和接口文档为实时语音商品搜索系统提供了完整的技术规范，包括：

1. **状态管理**: 定义了各模块的状态枚举、状态转换规则和状态持久化机制
2. **接口规范**: 详细描述了模块间通信协议、数据格式和API接口
3. **数据流**: 通过时序图和状态机图展示了系统的工作流程
4. **性能优化**: 提供了缓存策略和性能监控接口
5. **安全管理**: 定义了认证、授权和数据安全接口
6. **测试支持**: 提供了测试工具和模拟数据生成接口
7. **运维管理**: 包含了部署、监控和运维操作接口

### 15.2 开发团队使用建议

#### 15.2.1 前端开发团队
- 重点关注第2章Web UI Module的状态定义和WebSocket接口
- 参考第10.1章的Web UI状态机实现状态管理
- 使用第13.2章的模拟数据生成器进行开发测试

#### 15.2.2 后端开发团队
- 重点关注第3章Main Agent Module的核心接口定义
- 参考第10.2章的Main Agent状态机实现业务逻辑
- 使用第11章的缓存策略优化性能

#### 15.2.3 算法团队
- 重点关注第4章Search Agent和第5章RAG Agent的接口定义
- 参考相应的状态机实现算法流程
- 使用第6章的错误处理机制提高系统稳定性

#### 15.2.4 测试团队
- 使用第13章的测试接口进行自动化测试
- 参考第6章的错误代码进行异常测试
- 使用第11.2章的性能监控接口进行性能测试

#### 15.2.5 运维团队
- 使用第14章的部署和运维接口进行系统管理
- 参考第12章的安全接口实施安全策略
- 使用第6.2章的监控指标进行系统监控

### 15.3 版本管理和更新

本文档将随着项目的发展持续更新，建议：

1. **版本控制**: 每次重大更新都会增加版本号
2. **变更记录**: 在文档开头维护变更日志
3. **向后兼容**: 接口变更时保持向后兼容性
4. **通知机制**: 重要变更会通过邮件或会议通知相关团队

### 15.4 技术支持

如有疑问或建议，请联系：
- 架构团队：负责整体架构和接口设计
- 开发团队：负责具体实现和技术细节
- 测试团队：负责测试策略和质量保证
- 运维团队：负责部署和系统运维

本文档为实时语音商品搜索系统的核心技术文档，是项目成功实施的重要保障。
