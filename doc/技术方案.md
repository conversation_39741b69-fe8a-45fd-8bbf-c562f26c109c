# PRD: Modular Realtime Speech Search System (优化版)

**版本**: 4.0 (Enhanced Interactive)
**日期**: 2025-06-09
**基于**: 技术方案.md (Version 3.0) + 语音UI控制需求

## 1. 项目概述

此文档在原有模块化架构基础上，增强了语音UI控制功能和流式语音打断机制。系统现在支持用户通过语音指令操控商品展示界面，如"换一批"、"左划"、"右划"等功能，并具备完整的流式语音打断和搜索任务控制能力。

### 1.1 模块化目标（增强版）

* **Web UI (增强版)**: 负责所有用户侧的交互，包括实时语音通信、界面渲染、用户输入处理，**新增语音UI控制功能**，支持语音指令操控商品展示。
* **Main Agent (增强版)**: 作为系统的大脑和调度中心，管理对话流程，理解用户意图，协调其他Agent完成任务，**新增流式语音打断和搜索任务控制机制**。
* **Search Agent**: 专注于执行商品搜索，包括查询改写、调用搜索API和结果相关性判断。
* **RAG Agent**: 负责利用网络信息增强对模糊查询的理解，为用户提供开放性问题的解答和建议。

### 1.2 整体架构（增强版）

新的增强架构如下，展示了四个核心模块如何协同工作，并突出了语音UI控制和流式打断功能：

```mermaid
graph TD
    subgraph "Browser"
        A[Web UI - 增强版]
        A1[语音UI控制器]
        A2[商品展示控制器]
        A3[流式打断处理器]
    end

    subgraph "Backend Services"
        B[Main Agent - 增强版]
        B1[语音指令解析器]
        B2[流式打断控制器]
        B3[搜索任务管理器]
        C[Search Agent]
        D[RAG Agent]
    end
    
    subgraph "External APIs"
        E[ASR Service - Azure Whisper]
        F[TTS Service - ByteDance]
        G[Taobao Search API]
        H[Web Search API - Serper]
    end

    A -- "WebSocket (WebRTC Audio, UI Events)" --o B
    A1 -- "语音UI指令 (换一批/左划/右划)" --> B1
    A3 -- "打断信号" --> B2
    
    B -- "ASR Stream" --> E
    E -- "ASR Transcript" --> B
    B -- "Text for TTS" --> F
    F -- "TTS Audio Stream" --> B
    B -- "UI Updates (Product Cards, Status)" --o A
    B2 -- "打断控制" --> F
    B3 -- "任务控制" --> C
    B3 -- "任务控制" --> D
    
    B1 -- "解析后的UI指令" --> A2
    
    B -- "Internal API Call (Search Query)" --> C
    C -- "Call Taobao API" --> G
    G -- "Product List" --> C
    C -- "Structured Product Data" --> B
    
    B -- "Internal API Call (General Query)" --> D
    D -- "Call Web Search" --> H
    H -- "Web Results" --> D
    D -- "Summarized Text" --> B
```

---

## 2. 模块一: Web UI (增强版)

**负责人**: 前端开发团队

### 2.1 模块目标（增强版）

构建一个功能完备的前端应用，提供无缝的实时语音交互体验。UI需实时展示语音识别(ASR)结果、Agent回复、商品信息卡片，并处理用户的语音及指令输入。**新增语音UI控制功能**，支持用户通过语音指令操控商品展示界面。

### 2.2 核心功能（增强版）

* **实时语音通信**:
    * 通过 WebRTC 捕获麦克风音频流并发送至后端。
    * 接收并播放后端返回的实时TTS音频流。
    * 支持 `48000Hz` 采样率的单声道 `PCM16` 音频格式。
    * **新增**: 支持流式语音打断，用户可随时打断AI回复。

* **多模态信息展示**:
    * **ASR转录**: 实时显示 `interim` (中间) 和 `final` (最终) 的语音识别文本。
    * **商品卡片**: 以网格布局动态渲染商品列表，每张卡片包含图片、标题、价格、商品ID和详情链接。图片加载失败时显示占位图。
    * **Agent消息**: 清晰展示 Agent 的提问或状态更新。

* **用户交互处理**:
    * 提供开始、结束、重置对话的UI控件。
    * 当 Agent 发起提问时，显示醒目的语音交互提示（如"🎤 请语音回复..."），并进入等待用户语音回复的状态。

* **语音UI控制功能（新增）**:
    * **换一批**: 语音指令"换一批"、"下一页"、"更多商品"触发新的搜索请求。
    * **左划/右划**: 语音指令"左划"、"右划"、"上一个"、"下一个"控制商品卡片的横向滚动。
    * **商品选择**: 语音指令"第一个"、"第二个"、"这个"等选择特定商品。
    * **详情查看**: 语音指令"查看详情"、"更多信息"展开商品详情。
    * **返回操作**: 语音指令"返回"、"回去"返回上一级界面。

### 2.3 技术方案（增强版）

* **框架**: HTML, CSS, JavaScript (无特定框架要求，可使用原生JS或Vue/React)。

* **实时通信**:
    * 使用 `RTCPeerConnection` 建立与后端 `fastrtc` 的连接。
    * 通过 `addTrack` 发送本地麦克风音频流。
    * 通过 `ontrack` 事件接收并播放远程TTS音频流。
    * 使用 `createDataChannel` 建立一个名为 `messages` 的数据通道，用于收发JSON格式的信令（如ASR文本、商品数据等）。

* **组件设计（增强版）**:
    * `SpeechUI` 类：封装WebRTC连接、数据通道消息处理和DOM操作的逻辑。
    * `displayProductResults(products)` 函数：接收商品数组，动态生成商品卡片并渲染到页面。
    * `handleAgentInteraction(data)` 函数：处理Agent发起的交互请求，更新UI以提示用户进行语音回复。
    * **新增**: `VoiceUIController` 类：专门处理语音UI控制指令。
    * **新增**: `ProductDisplayController` 类：管理商品展示的滚动、分页、选择等操作。
    * **新增**: `InterruptionHandler` 类：处理流式语音打断逻辑。

* **语音UI控制实现（新增）**:
    * **指令识别**: 在ASR结果中识别UI控制关键词（换一批、左划、右划等）。
    * **状态管理**: 维护当前商品展示状态（当前页、选中项、滚动位置等）。
    * **动画效果**: 实现平滑的滚动、翻页、选择动画效果。
    * **反馈机制**: 语音指令执行后提供视觉和听觉反馈。

### 2.4 API接口 (WebSocket DataChannel) - 增强版

* **接收消息 (服务器 → 客户端)**:
    * `{"type": "asr_final", "text": "..."}`: 显示最终识别的文本。
    * `{"type": "agent_interaction", "question": "...", "interaction_id": "..."}`: 显示Agent的问题，并准备捕获用户的语音回复。
    * `{"type": "search_completed", "result": [...]}`: 接收商品JSON数组并调用 `displayProductResults` 函数。
    * `{"type": "status_update", "status": {...}}`: 显示Agent当前的工作状态。
    * **新增**: `{"type": "ui_command", "command": "next_page|prev_page|scroll_left|scroll_right", "data": {...}}`: UI控制指令。
    * **新增**: `{"type": "interruption", "action": "stop_tts|clear_queue"}`: 打断控制指令。

* **发送消息 (客户端 → 服务器)**:
    * `{"type": "start_search", "query": "..."}`: 用户通过语音输入了最终查询指令。
    * `{"type": "voice_response", "interaction_id": "...", "response_text": "..."}`: 针对Agent的问题，发送用户的语音回复文本。
    * **新增**: `{"type": "ui_voice_command", "command": "换一批|左划|右划|第一个", "context": {...}}`: 语音UI控制指令。
    * **新增**: `{"type": "interrupt_signal", "timestamp": "..."}`: 用户打断信号。

---

## 3. 模块二: Main Agent (增强版)

**负责人**: 算法与后端核心开发团队

### 3.1 模块目标（增强版）

作为系统的核心调度器，`Main Agent` 负责管理整个对话流程。它通过实时ASR/TTS与用户交互，准确识别用户意图，并智能地调度 `Search Agent` 或 `RAG Agent` 来完成具体任务，最后将结果通过 WebSocket 推送给 `Web UI`。**新增流式语音打断和搜索任务控制机制**，支持语音UI控制指令解析。

### 3.2 核心功能（增强版）

* **对话管理（增强版）**:
    * 维护对话的完整生命周期（开始、进行中、结束）。
    * **新增**: 处理用户的打断指令，能够中断正在进行的TTS播放或Agent任务。
    * **新增**: 支持流式语音打断，实时检测用户语音输入并中断当前输出。
    * 基于 `smolagents` 框架，管理多步骤的任务流程。

* **意图识别（增强版）**:
    * **初始分析**: 对用户的初始查询进行分析，判断其意图是明确的商品搜索，还是模糊的、需要知识增强的探索性问题。
    * **新增**: **UI控制意图识别**: 识别语音指令中的UI控制意图（换一批、左划、右划等）。
    * **工具**: `QueryAnalysisTool` - 一个内部工具，用于对查询进行分类和打分。
    * **新增**: `VoiceUICommandParser` - 专门解析语音UI控制指令。

* **与用户交互**:
    * **澄清需求**: 当意图明确但信息不足时（如缺少颜色、预算），主动向用户提问。
    * **工具**: `AskUserQuestionTool` - 生成自然语言问题，将文本发送至TTS模块，并等待用户的语音回复。

* **Agent调度（增强版）**:
    * **调度Search Agent**: 当意图是商品搜索时，调用 `Search Agent`。
    * **调度RAG Agent**: 当意图是探索性问题时（如"春游适合买什么？"），调用 `RAG Agent`。
    * **新增**: **搜索任务控制**: 支持任务的暂停、恢复、取消操作。

* **与Web UI交互（增强版）**:
    * 通过WebSocket与 `Web UI` 进行双向通信，处理音频流和数据流。
    * 管理 `fastrtc` 连接、Azure Whisper ASR 和字节跳动 TTS 的集成。
    * **新增**: 处理语音UI控制指令，并将解析结果发送给Web UI。

* **流式语音打断机制（新增）**:
    * **实时检测**: 监控用户语音输入，检测打断信号。
    * **快速响应**: 在检测到打断后立即停止TTS输出。
    * **状态恢复**: 支持打断后的对话状态恢复。
    * **队列管理**: 管理TTS输出队列，支持清空和重新排队。

### 3.3 技术方案（增强版）

* **后端框架**: Python, FastAPI。
* **Agent框架**: `smolagents.MultiStepAgent`。

* **实时通信（增强版）**:
    * `fastrtc`: 处理WebRTC连接和音视频流。
    * `rtc_handler.py`: 实现 `AsyncStreamHandler`，管理与ASR/TTS服务的WebSocket连接，并包含多个 `asyncio.Queue` 来解耦数据流。
    * **新增**: `InterruptionManager`: 管理流式语音打断逻辑。

* **核心类（增强版）**: `MainAgent(MultiStepAgent)`
    * 内部状态机，跟踪对话阶段（分析、澄清、搜索中等）。
    * `_determine_next_action` 方法，根据当前状态和用户输入，决定是调用 `AskUserQuestionTool`、`Search Agent` 还是 `RAG Agent`。
    * **新增**: `_handle_voice_ui_command` 方法，处理语音UI控制指令。
    * **新增**: `_manage_interruption` 方法，管理流式语音打断。

* **TTS/ASR集成（增强版）**:
    * `tts_client.py`: 封装字节跳动TTS API，支持流式合成和打断。
    * 与 Azure Whisper 的 WebSocket 连接，实时接收转录结果。
    * **新增**: `InterruptibleTTSClient`: 支持打断的TTS客户端。

* **内部API**: 通过直接的Python函数调用或HTTP请求与 `Search Agent` 和 `RAG Agent` 通信。

---

## 4. 核心模块如何协同工作（增强版）

### 4.1 标准搜索流程

1. **用户语音输入** → Web UI通过WebRTC捕获音频
2. **音频传输** → 音频流发送到Main Agent
3. **ASR处理** → Main Agent调用Azure Whisper进行语音识别
4. **意图分析** → Main Agent分析用户意图（搜索/澄清/UI控制）
5. **Agent调度** → 根据意图调用Search Agent或RAG Agent
6. **结果处理** → 获取搜索结果并格式化
7. **TTS合成** → 将回复转换为语音
8. **结果展示** → Web UI展示商品卡片和播放语音回复

### 4.2 流式语音打断流程（新增）

1. **并发监听** → Main Agent同时监听用户语音输入和TTS输出
2. **打断检测** → 检测到用户语音输入时触发打断信号
3. **立即停止** → 停止当前TTS播放和音频队列
4. **状态保存** → 保存当前对话状态和未完成的回复
5. **新输入处理** → 处理用户的新语音输入
6. **上下文恢复** → 基于保存的状态继续对话

### 4.3 语音UI控制流程（新增）

1. **指令识别** → Main Agent识别语音中的UI控制指令
2. **指令解析** → VoiceUICommandParser解析具体操作
3. **状态查询** → 查询当前UI状态（当前页、商品列表等）
4. **操作执行** → 根据指令执行相应操作（换一批/滚动等）
5. **结果反馈** → 向Web UI发送操作结果和新数据
6. **UI更新** → Web UI更新界面并提供反馈

### 4.4 搜索任务控制流程（新增）

1. **任务创建** → 为每个搜索请求创建任务ID
2. **状态跟踪** → 实时跟踪任务执行状态
3. **并发管理** → 支持多个搜索任务并发执行
4. **优先级控制** → 新任务可以中断低优先级任务
5. **资源管理** → 合理分配API调用和计算资源
6. **结果缓存** → 缓存搜索结果以支持"换一批"功能

### 4.5 错误处理和恢复机制（增强版）

1. **连接断开恢复** → WebRTC连接断开时自动重连
2. **ASR错误处理** → ASR服务异常时的降级策略
3. **TTS错误处理** → TTS服务异常时的备用方案
4. **搜索超时处理** → 搜索API超时时的重试机制
5. **打断冲突解决** → 多个打断信号的优先级处理
6. **状态一致性** → 确保各模块状态的一致性

---

## 5. 模块三: Search Agent

**负责人**: 搜索与推荐算法团队

### 5.1 模块目标

专注于商品搜索任务。接收来自 `Main Agent` 的查询，通过改写、调用淘宝主搜接口、判断相关性等步骤，最终返回结构化的、高相关的商品列表。

### 5.2 核心功能

* **查询改写 (Query Rewriting)**:
    * 将用户的自然语言查询（如"我想买个能用的手机"）改写成更适合搜索引擎的关键词（如"智能手机 最新"）。
    * 如果初次搜索结果不佳，能根据失败原因自动进行二次、三次改写。

* **商品搜索**:
    * 调用**淘宝主搜接口** (`TaobaoMainSearchTool`)，获取包含 `itemId` 的初步商品列表。
    * **强制获取详情**: 对搜索到的核心商品，自动调用**商品详情接口** (`TaobaoItemDetailsTool`)，补全图片、更准确的价格和详细规格等信息。这是为了确保返回给 `Main Agent` 的数据是完整的。

* **相关性判断**:
    * 内置一个评估模块，判断搜索返回的商品列表与改写后查询的语义相关性。
    * 如果相关性低于阈值（例如 0.7），则触发查询改写和重新搜索流程。最多重试N次（如3次）以避免死循环。

### 5.3 技术方案

* **框架**: 可作为一个独立的Python类或微服务来实现。
* **核心逻辑**:
    * `SearchAgent.run(query: str) -> List[dict]`: Agent的入口函数。
    * **内部循环**:
        1.  调用 `rewrite_query(query)`。
        2.  调用 `TaobaoMainSearchTool(rewritten_query)`。
        3.  调用 `judge_relevance(results, rewritten_query)`。
        4.  若相关性不足且未达重试上限，返回步骤1；否则继续。
        5.  遍历核心结果，调用 `TaobaoItemDetailsTool(item_id)` 补全信息。
        6.  返回格式化、完整的商品列表。
* **工具封装**:
    * `TaobaoMainSearchTool`: 封装对淘宝主搜API的HTTP请求。
    * `TaobaoItemDetailsTool`: 封装对商品详情API的HTTP请求。
* **输入**: 一个字符串 `query`。
* **输出**: 一个商品字典列表 `List[dict]`，每个字典必须包含：
    ```json
    {
      "itemId": "商品ID",
      "title": "商品标题",
      "price": "价格",
      "image_url": "图片URL",
      "detail_url": "详情链接"
    }
    ```

---

## 6. 模块四: RAG Agent

**负责人**: NLP与大模型应用团队

### 6.1 模块目标

处理开放式、探索性的用户查询。通过在互联网上进行搜索（Retrieval）并利用LLM进行总结（Generation），为用户提供有用的信息和建议，这些信息将作为后续商品搜索的上下文。

### 6.2 核心功能

* **网络信息检索**:
    * 当用户查询模糊时（如"秋天去露营要准备什么？"），使用网络搜索引擎获取相关文章、指南或推荐。
    * **工具**: `WebSearchTool` (基于 Serper API)。

* **信息整合与总结**:
    * 将检索到的多个信息源进行清洗和整合。
    * 利用大语言模型（LLM）对整合后的信息进行提炼和总结，生成一段通顺、简洁、有条理的文本回复。
    * **工具**: `SearchSummaryTool` (基于 GPT-4o)。

* **关键词提取**: 从总结的文本中提取核心商品词或品类词，为 `Main Agent` 下一步转入 `Search Agent` 提供建议。

### 6.3 技术方案

* **框架**: 可作为一个独立的Python类或微服务。
* **核心逻辑**: `RAGAgent.run(query: str) -> dict`:
    1.  调用 `WebSearchTool(query)` 获取网络搜索结果（如Top 10页面的内容）。
    2.  将所有内容拼接，并构造一个Prompt，要求LLM根据这些内容回答原始问题。
    3.  调用 `SearchSummaryTool(prompt)` 生成总结性文本。
    4.  （可选）再次调用LLM，从总结文本中提取可供搜索的关键词列表。
* **LLM调用**: 通过 `custom_model.py` 封装对 Azure OpenAI `gpt-4o-0806` 的调用。
* **输入**: 一个字符串 `query`。
* **输出**: 一个包含总结和关键词的字典：
    ```json
    {
      "summary": "这是为您生成的建议...",
      "suggested_keywords": ["帐篷", "睡袋", "防潮垫", "露营灯"]
    }
    ```

---

## 7. 开发与部署（增强版）

### 7.1 并行开发里程碑（增强版）

* **Week 1-2: 独立开发与单元测试**
    * **UI Team**: 完成 WebRTC 连接、音频处理和基础UI布局，**新增语音UI控制组件开发**。
    * **Main Agent Team**: 搭建 FastAPI 服务，集成 `fastrtc`，实现 ASR/TTS 基础通信，**新增流式打断和任务控制机制**。
    * **Search Agent Team**: 封装淘宝API，实现查询改写和相关性判断的初步逻辑。
    * **RAG Agent Team**: 封装网络搜索和LLM总结API。

* **Week 3: 模块集成与接口联调**
    * `Main Agent` 与 `Search Agent` / `RAG Agent` 进行内部API联调。
    * `Main Agent` 与 `Web UI` 进行 WebSocket 接口联调。
    * **新增**: 语音UI控制功能集成测试。
    * **新增**: 流式语音打断功能测试。

* **Week 4: 端到端测试与优化**
    * 进行完整的端到端功能测试、交互流程测试和性能测试。
    * **新增**: 语音UI控制场景测试（换一批、滚动、选择等）。
    * **新增**: 流式打断场景测试（各种打断时机和恢复情况）。

### 7.2 部署方案

* `Web UI` 作为静态文件由 `Main Agent` 的 FastAPI 服务托管，或部署在CDN上。
* `Main Agent`, `Search Agent`, `RAG Agent` 可以容器化（使用Docker），并使用 Kubernetes 或类似工具进行编排部署，方便独立扩展和管理。

### 7.3 性能优化建议（新增）

* **语音处理优化**: 使用音频缓冲和预处理减少延迟。
* **打断响应优化**: 优化打断检测算法，减少误触发。
* **UI渲染优化**: 使用虚拟滚动和懒加载优化商品展示性能。
* **缓存策略**: 实现多级缓存提高搜索响应速度。

---

## 8. 测试策略（新增）

### 8.1 功能测试

* **语音UI控制测试**: 测试各种语音指令的识别准确率和执行效果。
* **流式打断测试**: 测试不同时机的打断和恢复功能。
* **搜索任务控制测试**: 测试任务的创建、暂停、恢复、取消功能。

### 8.2 性能测试

* **并发用户测试**: 测试系统在多用户同时使用时的性能。
* **打断延迟测试**: 测试从检测到打断信号到停止输出的延迟。
* **UI响应测试**: 测试语音UI控制的响应速度。

### 8.3 用户体验测试

* **语音指令自然度测试**: 测试用户使用各种表达方式的识别效果。
* **界面流畅度测试**: 测试滚动、翻页等操作的流畅度。
* **错误恢复测试**: 测试各种异常情况下的用户体验。

---

## 9. 语音UI控制详细设计（新增）

### 9.1 支持的语音指令

#### 9.1.1 商品浏览控制
* **换一批商品**:
  - 触发词: "换一批"、"下一页"、"更多商品"、"再看看其他的"
  - 功能: 触发新的搜索请求，获取不同的商品结果
  - 实现: 调用Search Agent，使用不同的搜索参数或排序方式

* **商品滚动控制**:
  - 左滚动: "左划"、"往左"、"上一个"、"前一个"
  - 右滚动: "右划"、"往右"、"下一个"、"后一个"
  - 功能: 控制商品卡片的横向滚动显示
  - 实现: 前端JavaScript控制CSS transform或scroll属性

#### 9.1.2 商品选择操作
* **位置选择**:
  - 触发词: "第一个"、"第二个"、"第三个"、"最后一个"
  - 功能: 选择特定位置的商品
  - 实现: 根据位置索引高亮对应商品卡片

* **相对选择**:
  - 触发词: "这个"、"当前这个"、"中间的"
  - 功能: 选择当前焦点商品
  - 实现: 选择当前视窗中心或高亮的商品

#### 9.1.3 详情查看操作
* **查看详情**:
  - 触发词: "查看详情"、"更多信息"、"详细介绍"、"打开"
  - 功能: 展开选中商品的详细信息
  - 实现: 弹出详情模态框或跳转详情页面

* **返回操作**:
  - 触发词: "返回"、"回去"、"关闭"、"退出"
  - 功能: 关闭详情页面或返回上一级
  - 实现: 关闭模态框或历史记录回退

### 9.2 语音指令处理流程

#### 9.2.1 指令识别流程
```mermaid
graph TD
    A[用户语音输入] --> B[ASR转录]
    B --> C[文本预处理]
    C --> D[关键词匹配]
    D --> E{是否为UI指令?}
    E -->|是| F[指令分类]
    E -->|否| G[常规搜索处理]
    F --> H[参数提取]
    H --> I[指令执行]
    I --> J[UI更新]
    J --> K[反馈用户]
```

#### 9.2.2 指令优先级处理
1. **高优先级**: 打断指令（"停止"、"暂停"）
2. **中优先级**: UI控制指令（"换一批"、"左划"）
3. **低优先级**: 搜索指令（商品查询）

### 9.3 技术实现细节

#### 9.3.1 前端实现
```javascript
class VoiceUIController {
    constructor() {
        this.currentPage = 0;
        this.selectedIndex = 0;
        this.products = [];
        this.isDetailView = false;
    }

    handleVoiceCommand(command, params) {
        switch(command) {
            case 'next_batch':
                this.requestNewProducts();
                break;
            case 'scroll_left':
                this.scrollProducts(-1);
                break;
            case 'scroll_right':
                this.scrollProducts(1);
                break;
            case 'select_item':
                this.selectProduct(params.index);
                break;
            case 'show_details':
                this.showProductDetails();
                break;
            case 'go_back':
                this.goBack();
                break;
        }
    }

    scrollProducts(direction) {
        const container = document.getElementById('products-grid');
        const scrollAmount = 300; // 像素
        container.scrollBy({
            left: direction * scrollAmount,
            behavior: 'smooth'
        });
        this.provideFeedback(`已${direction > 0 ? '右' : '左'}滑`);
    }

    provideFeedback(message) {
        // 视觉反馈
        this.showToast(message);
        // 语音反馈
        this.sendToTTS(message);
    }
}
```

#### 9.3.2 后端指令解析
```python
class VoiceUICommandParser:
    def __init__(self):
        self.command_patterns = {
            'next_batch': ['换一批', '下一页', '更多商品', '再看看其他的'],
            'scroll_left': ['左划', '往左', '上一个', '前一个'],
            'scroll_right': ['右划', '往右', '下一个', '后一个'],
            'select_item': ['第一个', '第二个', '第三个', '这个'],
            'show_details': ['查看详情', '更多信息', '详细介绍'],
            'go_back': ['返回', '回去', '关闭', '退出']
        }

    def parse_command(self, text: str) -> dict:
        text = text.strip().lower()

        for command, patterns in self.command_patterns.items():
            for pattern in patterns:
                if pattern in text:
                    return {
                        'command': command,
                        'confidence': self._calculate_confidence(text, pattern),
                        'params': self._extract_params(text, command)
                    }

        return None

    def _extract_params(self, text: str, command: str) -> dict:
        params = {}

        if command == 'select_item':
            # 提取位置信息
            if '第一' in text or '1' in text:
                params['index'] = 0
            elif '第二' in text or '2' in text:
                params['index'] = 1
            elif '第三' in text or '3' in text:
                params['index'] = 2
            # ... 更多位置解析

        return params
```

---

## 10. 流式语音打断详细设计（新增）

### 10.1 打断检测机制

#### 10.1.1 检测策略
* **音量阈值检测**: 检测到超过阈值的音频输入
* **语音活动检测(VAD)**: 使用VAD算法识别真实语音
* **关键词检测**: 识别特定的打断关键词（"等等"、"停"）
* **时间窗口**: 在TTS播放期间持续监听用户输入

#### 10.1.2 检测流程
```mermaid
graph TD
    A[TTS开始播放] --> B[启动打断监听]
    B --> C[实时音频监控]
    C --> D{检测到语音?}
    D -->|否| C
    D -->|是| E[VAD验证]
    E --> F{是真实语音?}
    F -->|否| C
    F -->|是| G[触发打断]
    G --> H[停止TTS]
    H --> I[清空音频队列]
    I --> J[处理新输入]
```

### 10.2 打断处理机制

#### 10.2.1 快速响应
* **目标延迟**: 从检测到停止输出 < 200ms
* **实现方式**:
  - 使用异步队列管理音频输出
  - 实现立即停止机制
  - 预留打断检测的计算资源

#### 10.2.2 状态管理
```python
class InterruptionManager:
    def __init__(self):
        self.is_playing = False
        self.can_interrupt = True
        self.current_generation_id = None
        self.interrupted_content = None
        self.conversation_state = {}

    async def start_tts_with_interruption(self, text: str):
        self.is_playing = True
        self.can_interrupt = True
        generation_id = str(uuid.uuid4())
        self.current_generation_id = generation_id

        # 启动TTS播放
        tts_task = asyncio.create_task(
            self.tts_client.stream_audio(text, generation_id)
        )

        # 启动打断监听
        interrupt_task = asyncio.create_task(
            self.monitor_interruption(generation_id)
        )

        # 等待任一任务完成
        done, pending = await asyncio.wait(
            [tts_task, interrupt_task],
            return_when=asyncio.FIRST_COMPLETED
        )

        # 清理未完成的任务
        for task in pending:
            task.cancel()

    async def handle_interruption(self, generation_id: str):
        if generation_id != self.current_generation_id:
            return  # 过期的打断信号

        # 立即停止TTS
        await self.tts_client.stop_current_generation(generation_id)

        # 保存被打断的内容
        self.interrupted_content = await self.tts_client.get_remaining_content()

        # 更新状态
        self.is_playing = False
        self.can_interrupt = False

        # 通知前端
        await self.notify_frontend({
            'type': 'interruption',
            'action': 'tts_stopped',
            'generation_id': generation_id
        })
```

### 10.3 上下文恢复机制

#### 10.3.1 状态保存
* **对话历史**: 保存完整的对话上下文
* **未完成内容**: 保存被打断的TTS内容
* **搜索状态**: 保存当前搜索进度和结果
* **UI状态**: 保存当前页面和选择状态

#### 10.3.2 智能恢复
```python
class ContextRecoveryManager:
    def __init__(self):
        self.saved_contexts = {}

    def save_context(self, session_id: str, context: dict):
        self.saved_contexts[session_id] = {
            'timestamp': datetime.now(),
            'conversation_history': context.get('history', []),
            'current_search_results': context.get('results', []),
            'ui_state': context.get('ui_state', {}),
            'interrupted_content': context.get('interrupted_content', ''),
            'user_intent': context.get('intent', {})
        }

    async def recover_context(self, session_id: str, new_input: str):
        if session_id not in self.saved_contexts:
            return None

        saved = self.saved_contexts[session_id]

        # 分析新输入与被打断内容的关系
        relationship = await self.analyze_input_relationship(
            new_input,
            saved['interrupted_content'],
            saved['user_intent']
        )

        if relationship == 'continuation':
            # 用户想继续之前的话题
            return self.merge_contexts(saved, new_input)
        elif relationship == 'new_topic':
            # 用户开始新话题
            return self.start_new_context(new_input)
        else:
            # 用户想修正或补充
            return self.refine_context(saved, new_input)
```

---

## 11. 搜索任务控制详细设计（新增）

### 11.1 任务生命周期管理

#### 11.1.1 任务状态定义
```python
from enum import Enum

class TaskStatus(Enum):
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    FAILED = "failed"

class SearchTask:
    def __init__(self, task_id: str, query: str, priority: int = 1):
        self.task_id = task_id
        self.query = query
        self.priority = priority
        self.status = TaskStatus.CREATED
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.results = []
        self.error_message = None
        self.progress = 0.0
```

#### 11.1.2 任务调度器
```python
class SearchTaskScheduler:
    def __init__(self):
        self.active_tasks = {}
        self.task_queue = asyncio.PriorityQueue()
        self.max_concurrent_tasks = 3
        self.running_tasks = set()

    async def submit_task(self, task: SearchTask) -> str:
        await self.task_queue.put((task.priority, task))
        self.active_tasks[task.task_id] = task

        # 如果有空闲槽位，立即开始执行
        if len(self.running_tasks) < self.max_concurrent_tasks:
            asyncio.create_task(self._execute_next_task())

        return task.task_id

    async def _execute_next_task(self):
        try:
            priority, task = await self.task_queue.get()
            if task.task_id in self.running_tasks:
                return  # 任务已在运行

            self.running_tasks.add(task.task_id)
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()

            # 执行搜索任务
            await self._run_search_task(task)

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
        finally:
            self.running_tasks.discard(task.task_id)
            # 继续执行队列中的下一个任务
            if not self.task_queue.empty():
                asyncio.create_task(self._execute_next_task())
```

### 11.2 任务优先级和抢占

#### 11.2.1 优先级定义
* **紧急 (Priority 0)**: 用户打断后的新搜索
* **高 (Priority 1)**: 用户主动发起的搜索
* **中 (Priority 2)**: "换一批"等UI控制触发的搜索
* **低 (Priority 3)**: 预加载和缓存更新

#### 11.2.2 抢占机制
```python
async def handle_high_priority_task(self, new_task: SearchTask):
    if new_task.priority == 0:  # 紧急任务
        # 暂停所有低优先级任务
        for task_id in list(self.running_tasks):
            task = self.active_tasks[task_id]
            if task.priority > new_task.priority:
                await self.pause_task(task_id)

        # 立即执行新任务
        await self.submit_task(new_task)
```

### 11.3 结果缓存和"换一批"实现

#### 11.3.1 缓存策略
```python
class SearchResultCache:
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5分钟过期

    def cache_results(self, query: str, results: list, page: int = 0):
        cache_key = self._generate_cache_key(query, page)
        self.cache[cache_key] = {
            'results': results,
            'timestamp': datetime.now(),
            'query': query,
            'page': page
        }

    def get_next_batch(self, query: str, current_page: int = 0):
        # 尝试从缓存获取下一页
        next_page = current_page + 1
        cache_key = self._generate_cache_key(query, next_page)

        if cache_key in self.cache:
            cached = self.cache[cache_key]
            if self._is_cache_valid(cached['timestamp']):
                return cached['results']

        # 缓存未命中，需要新的搜索
        return None
```

#### 11.3.2 "换一批"实现逻辑
```python
async def handle_next_batch_request(self, session_id: str):
    session = self.get_session(session_id)
    current_query = session.get('last_query')
    current_page = session.get('current_page', 0)

    # 尝试从缓存获取
    cached_results = self.cache.get_next_batch(current_query, current_page)

    if cached_results:
        # 直接返回缓存结果
        session['current_page'] = current_page + 1
        await self.send_results_to_frontend(session_id, cached_results)
    else:
        # 创建新的搜索任务
        task = SearchTask(
            task_id=str(uuid.uuid4()),
            query=current_query,
            priority=2  # 中等优先级
        )
        task.params = {
            'page': current_page + 1,
            'exclude_items': session.get('shown_items', [])
        }

        await self.task_scheduler.submit_task(task)
```

---

## 12. 实施建议和最佳实践（新增）

### 12.1 开发优先级建议

#### 12.1.1 第一阶段 (MVP)
1. **基础语音UI控制**: 实现"换一批"和基本滚动功能
2. **简单打断机制**: 实现基本的TTS停止功能
3. **核心搜索功能**: 确保基本搜索流程稳定

#### 12.1.2 第二阶段 (增强)
1. **完整语音指令集**: 实现所有定义的语音指令
2. **智能打断**: 增加VAD和上下文恢复
3. **任务调度**: 实现完整的任务管理系统

#### 12.1.3 第三阶段 (优化)
1. **性能优化**: 减少延迟，提高响应速度
2. **用户体验**: 增加动画效果和反馈机制
3. **错误处理**: 完善异常情况的处理

### 12.2 技术选型建议

#### 12.2.1 前端技术栈
* **基础**: HTML5, CSS3, ES6+
* **音频处理**: Web Audio API, WebRTC
* **动画**: CSS Transitions + JavaScript
* **状态管理**: 原生JavaScript或轻量级状态库

#### 12.2.2 后端技术栈
* **框架**: FastAPI (已选定)
* **异步处理**: asyncio, aiohttp
* **任务队列**: asyncio.Queue 或 Redis Queue
* **缓存**: Redis 或内存缓存

### 12.3 性能优化建议

#### 12.3.1 延迟优化
* **音频处理**: 使用音频缓冲减少延迟
* **网络优化**: 使用WebSocket保持连接
* **预加载**: 预加载下一批商品数据
* **并发处理**: 并行执行ASR和TTS处理

#### 12.3.2 用户体验优化
* **即时反馈**: 语音指令执行后立即提供视觉反馈
* **平滑动画**: 使用CSS3动画实现平滑过渡
* **错误提示**: 清晰的错误信息和恢复建议
* **加载状态**: 明确的加载指示器

### 12.4 测试策略建议

#### 12.4.1 自动化测试
* **单元测试**: 覆盖所有核心功能模块
* **集成测试**: 测试模块间的协作
* **端到端测试**: 模拟真实用户场景

#### 12.4.2 用户测试
* **可用性测试**: 测试语音指令的自然度
* **压力测试**: 测试系统在高负载下的表现
* **兼容性测试**: 测试不同浏览器和设备的兼容性

---

## 13. 总结

本优化版技术方案在原有模块化架构基础上，成功集成了语音UI控制功能和流式语音打断机制。主要改进包括：

### 13.1 架构决策
* **语音UI控制集成到Web UI模块**: 避免了架构复杂化，保持了系统的简洁性
* **增强Main Agent的协调能力**: 新增了流式打断和任务控制功能
* **保持模块独立性**: Search Agent和RAG Agent保持原有设计，确保系统稳定性

### 13.2 核心创新
* **实时语音UI控制**: 用户可通过自然语音指令操控界面
* **流式语音打断**: 支持实时打断和智能上下文恢复
* **智能任务调度**: 支持任务优先级和并发控制
* **缓存优化**: 实现"换一批"功能的高效缓存机制

### 13.3 技术优势
* **低延迟响应**: 打断检测和UI控制响应时间 < 200ms
* **高可用性**: 完善的错误处理和恢复机制
* **良好扩展性**: 模块化设计支持功能的持续扩展
* **用户友好**: 自然的语音交互和流畅的界面操作

这个优化方案为实时语音商品搜索系统提供了完整的技术蓝图，既保持了原有架构的优势，又增加了先进的语音交互功能，将为用户提供更加自然和高效的购物体验。
