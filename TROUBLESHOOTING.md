# 故障排除指南

本文档提供常见问题的解决方案。

## 安装问题

### 1. uv安装失败

**问题**: `uv` 命令未找到或安装失败

**解决方案**:
```bash
# 方法1: 使用pip安装
pip install uv

# 方法2: 手动下载安装
# 访问 https://docs.astral.sh/uv/getting-started/installation/

# 方法3: 使用conda安装
conda install -c conda-forge uv
```

### 2. 依赖安装失败

**问题**: `uv sync` 失败或依赖冲突

**解决方案**:
```bash
# 清理缓存重新安装
uv cache clean
uv sync --reinstall

# 如果仍然失败，尝试逐步安装
uv sync --no-dev
uv add --editable ./thirdparty/realtime_chat/openai-python
```

### 3. OpenAI包安装问题

**问题**: 修改过的OpenAI包安装失败

**解决方案**:
```bash
# 先移除现有的openai包
uv remove openai

# 重新安装修改过的版本
uv add --editable ./thirdparty/realtime_chat/openai-python

# 验证安装
uv run python -c "import openai; print(openai.__version__)"
```

## 运行时问题

### 1. 音频设备问题

**问题**: 无法检测到音频设备或音频功能异常

**解决方案**:
```bash
# 检查音频设备
uv run python -c "import sounddevice; print(sounddevice.query_devices())"

# macOS: 检查权限设置
# 系统偏好设置 > 安全性与隐私 > 隐私 > 麦克风

# Linux: 安装音频库
sudo apt-get install portaudio19-dev python3-pyaudio
# 或
sudo yum install portaudio-devel

# Windows: 确保音频驱动正常
```

### 2. SSL证书问题

**问题**: HTTPS访问失败或证书错误

**解决方案**:
```bash
# 方法1: 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 方法2: 使用HTTP模式（开发环境）
# 修改app.py中的启动参数，移除ssl_certfile和ssl_keyfile

# 方法3: 使用现有证书
# 将证书文件复制为 cert.pem 和 key.pem
```

### 3. API配置问题

**问题**: API调用失败或认证错误

**解决方案**:
```bash
# 检查.env文件配置
cat .env

# 确保包含以下配置:
AZURE_OPENAI_API_KEY=your_actual_api_key
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_MODEL_NAME=gpt-4

# 测试API连接
uv run python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('API Key:', os.getenv('AZURE_OPENAI_API_KEY')[:10] + '...')
print('Endpoint:', os.getenv('AZURE_OPENAI_ENDPOINT'))
"
```

### 4. 端口占用问题

**问题**: 端口7878已被占用

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :7878  # macOS/Linux
netstat -ano | findstr :7878  # Windows

# 终止占用进程
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows

# 或修改app.py中的端口号
```

## 性能问题

### 1. 语音识别延迟

**问题**: 语音识别响应慢或不准确

**解决方案**:
- 检查网络连接质量
- 确保麦克风质量良好
- 调整音频采样率设置
- 检查CPU使用率

### 2. TTS合成问题

**问题**: 语音合成失败或音质差

**解决方案**:
- 检查TTS服务配置
- 验证音频输出设备
- 调整音频格式设置
- 检查网络连接

### 3. 内存使用过高

**问题**: 应用占用内存过多

**解决方案**:
- 重启应用释放内存
- 检查是否有内存泄漏
- 调整批处理大小
- 清理图片缓存目录

## 开发问题

### 1. 代码修改不生效

**问题**: 修改代码后重启应用仍然使用旧代码

**解决方案**:
```bash
# 清理Python缓存
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +

# 重新安装包
uv sync --reinstall
```

### 2. 导入模块失败

**问题**: 无法导入thirdparty目录下的模块

**解决方案**:
```python
# 确保路径正确添加
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent / "thirdparty" / "realtime_chat"))
sys.path.append(str(Path(__file__).parent / "thirdparty" / "tb_speech_search"))
```

### 3. 调试信息不足

**问题**: 错误信息不够详细

**解决方案**:
```bash
# 启用详细日志
export DEBUG=true
export LOG_LEVEL=DEBUG

# 或在.env文件中添加
echo "DEBUG=true" >> .env
echo "LOG_LEVEL=DEBUG" >> .env
```

## 获取帮助

### 1. 运行验证脚本

```bash
uv run python verify_installation.py
```

### 2. 查看日志

应用运行时会输出详细日志，包括：
- 语音识别结果
- API调用状态
- 错误堆栈信息

### 3. 检查系统要求

- Python 3.10+
- 足够的内存 (建议4GB+)
- 稳定的网络连接
- 音频输入/输出设备

### 4. 联系支持

如果问题仍然存在：
1. 收集错误日志
2. 记录重现步骤
3. 提供系统信息
4. 提交GitHub Issue

## 常用命令

```bash
# 完整重新安装
uv cache clean
uv sync --reinstall
uv add --editable ./thirdparty/realtime_chat/openai-python

# 验证安装
uv run python verify_installation.py

# 启动应用
uv run python app.py

# 查看依赖
uv tree

# 更新依赖
uv sync --upgrade
```
