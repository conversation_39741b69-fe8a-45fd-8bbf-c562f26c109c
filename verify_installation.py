#!/usr/bin/env python3
"""
实时语音商品搜索代理 - 安装验证脚本
检查所有必要的依赖和配置是否正确安装
"""

import sys
import os
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.10或更高版本")
        return False

def check_import(module_name, package_name=None):
    """检查模块导入"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {package_name or module_name}")
        return True
    except ImportError as e:
        print(f"❌ {package_name or module_name}: {e}")
        return False

def check_core_dependencies():
    """检查核心依赖"""
    print("\n📦 检查核心依赖...")
    
    dependencies = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("gradio", "Gradio"),
        ("numpy", "NumPy"),
        ("pandas", "Pandas"),
        ("pydantic", "Pydantic"),
        ("requests", "Requests"),
        ("aiohttp", "AioHTTP"),
        ("websockets", "WebSockets"),
    ]
    
    success_count = 0
    for module, name in dependencies:
        if check_import(module, name):
            success_count += 1
    
    return success_count == len(dependencies)

def check_audio_dependencies():
    """检查音频相关依赖"""
    print("\n🎵 检查音频依赖...")
    
    dependencies = [
        ("sounddevice", "SoundDevice"),
        ("soundfile", "SoundFile"),
        ("librosa", "Librosa"),
        ("pydub", "PyDub"),
        ("audioread", "AudioRead"),
    ]
    
    success_count = 0
    for module, name in dependencies:
        if check_import(module, name):
            success_count += 1
    
    return success_count == len(dependencies)

def check_ai_dependencies():
    """检查AI相关依赖"""
    print("\n🤖 检查AI依赖...")
    
    dependencies = [
        ("openai", "OpenAI"),
        ("huggingface_hub", "Hugging Face Hub"),
    ]
    
    success_count = 0
    for module, name in dependencies:
        if check_import(module, name):
            success_count += 1
    
    return success_count == len(dependencies)

def check_realtime_dependencies():
    """检查实时通信依赖"""
    print("\n📡 检查实时通信依赖...")
    
    dependencies = [
        ("aiortc", "AioRTC"),
        ("fastrtc", "FastRTC"),
        ("aioice", "AioICE"),
        ("pylibsrtp", "PyLibSRTP"),
    ]
    
    success_count = 0
    for module, name in dependencies:
        if check_import(module, name):
            success_count += 1
    
    return success_count == len(dependencies)

def check_thirdparty_modules():
    """检查第三方模块"""
    print("\n🔧 检查第三方模块...")
    
    # 添加thirdparty路径
    thirdparty_path = Path(__file__).parent / "thirdparty"
    if thirdparty_path.exists():
        sys.path.insert(0, str(thirdparty_path / "realtime_chat"))
        sys.path.insert(0, str(thirdparty_path / "tb_speech_search"))
    
    modules = [
        ("chat", "聊天模块"),
        ("tts8", "TTS模块"),
        ("custom_tools", "自定义工具"),
        ("search_agent", "搜索代理"),
    ]
    
    success_count = 0
    for module, name in modules:
        if check_import(module, name):
            success_count += 1
    
    return success_count == len(modules)

def check_audio_devices():
    """检查音频设备"""
    print("\n🎤 检查音频设备...")
    try:
        import sounddevice as sd
        devices = sd.query_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
        output_devices = [d for d in devices if d['max_output_channels'] > 0]
        
        print(f"✅ 找到 {len(input_devices)} 个输入设备")
        print(f"✅ 找到 {len(output_devices)} 个输出设备")
        
        if len(input_devices) == 0:
            print("⚠️  警告: 未找到音频输入设备")
            return False
        if len(output_devices) == 0:
            print("⚠️  警告: 未找到音频输出设备")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 音频设备检查失败: {e}")
        return False

def check_configuration():
    """检查配置文件"""
    print("\n⚙️  检查配置文件...")
    
    config_files = [".env", "key.env"]
    config_found = False
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ 找到配置文件: {config_file}")
            config_found = True
            
            # 检查必要的配置项
            with open(config_file, 'r') as f:
                content = f.read()
                if "AZURE_OPENAI_API_KEY" in content:
                    print("✅ 找到API密钥配置")
                else:
                    print("⚠️  警告: 未找到API密钥配置")
                    
                if "AZURE_OPENAI_ENDPOINT" in content:
                    print("✅ 找到API端点配置")
                else:
                    print("⚠️  警告: 未找到API端点配置")
            break
    
    if not config_found:
        print("⚠️  警告: 未找到配置文件 (.env 或 key.env)")
        print("   请创建配置文件并设置API密钥")
        return False
    
    return True

def check_ssl_certificates():
    """检查SSL证书"""
    print("\n🔐 检查SSL证书...")
    
    cert_files = ["cert.pem", "key.pem"]
    all_found = True
    
    for cert_file in cert_files:
        if os.path.exists(cert_file):
            print(f"✅ 找到证书文件: {cert_file}")
        else:
            print(f"⚠️  未找到证书文件: {cert_file}")
            all_found = False
    
    if not all_found:
        print("   注意: HTTPS模式需要SSL证书文件")
        print("   可以使用HTTP模式运行（不推荐生产环境）")
    
    return all_found

def check_directories():
    """检查必要的目录"""
    print("\n📁 检查目录结构...")
    
    directories = [
        "thirdparty/realtime_chat",
        "thirdparty/tb_speech_search",
        "thirdparty/realtime_chat/openai-python",
        "image_cache",
        "prompts",
    ]
    
    success_count = 0
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}")
            success_count += 1
        else:
            print(f"❌ {directory}")
    
    return success_count == len(directories)

def main():
    """主验证函数"""
    print("🔍 实时语音商品搜索代理 - 安装验证")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("核心依赖", check_core_dependencies),
        ("音频依赖", check_audio_dependencies),
        ("AI依赖", check_ai_dependencies),
        ("实时通信依赖", check_realtime_dependencies),
        ("第三方模块", check_thirdparty_modules),
        ("音频设备", check_audio_devices),
        ("配置文件", check_configuration),
        ("SSL证书", check_ssl_certificates),
        ("目录结构", check_directories),
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查出错: {e}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 验证结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！环境安装正确。")
        print("💡 可以运行: uv run python app.py")
        return True
    else:
        print("⚠️  部分检查未通过，请根据上述提示进行修复。")
        print("📚 详细说明请查看 README.md")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
