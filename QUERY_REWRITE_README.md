# Query改写和Memory管理功能

## 功能概述

为app.py语音通信系统新增了智能query改写和对话记忆管理功能，以提高商品搜索的准确率和用户体验。

## 核心特性

### 1. 对话记忆管理 (ConversationMemory)
- **多轮对话记录**: 自动记录用户的对话历史，包括意图、置信度、时间戳等
- **搜索上下文提取**: 智能提取地点、价格、品类等搜索相关信息
- **上下文累积**: 支持多轮对话信息的累积和合并

### 2. 智能Query改写
- **仅对搜索意图改写**: 只对明确的商品搜索意图进行query改写，交互意图保持不变
- **多轮信息合并**: 将多轮对话中的补充信息合并为完整的搜索查询
- **TTS播报**: 改写后的query通过语音播报，让用户确认理解正确

### 3. 上下文信息提取
- **价格信息**: 自动识别"20元以下"、"100-200元"等价格限制
- **地点信息**: 提取"北京"、"上海"等地理位置信息
- **品类信息**: 识别商品类别如"糕点"、"手机"、"运动鞋"等

## 使用示例

### 场景1: 地点+商品+价格限制
```
用户: "北京有什么好吃的糕点"
系统: 正在为您搜索「北京有什么好吃的糕点」相关商品...

用户: "我想要20元以下的"
系统: 我理解您要搜索「北京20元以下糕点」，正在为您查找相关商品...
```

### 场景2: 品牌+商品+颜色
```
用户: "苹果手机"
系统: 正在为您搜索「苹果手机」相关商品...

用户: "红色的"
系统: 我理解您要搜索「红色苹果手机」，正在为您查找相关商品...
```

### 场景3: 复杂多轮搜索
```
用户: "运动鞋"
系统: 正在为您搜索「运动鞋」相关商品...

用户: "耐克的"
系统: 我理解您要搜索「耐克运动鞋」，正在为您查找相关商品...

用户: "500到800元"
系统: 我理解您要搜索「耐克500-800元运动鞋」，正在为您查找相关商品...
```

## 技术实现

### 核心类结构

#### ConversationTurn
```python
class ConversationTurn:
    def __init__(self, turn_id: str, user_input: str, intent: str, confidence: float, 
                 timestamp: str = None, agent_response: str = "", search_query: str = "", 
                 rewritten_query: str = ""):
```

#### ConversationMemory
```python
class ConversationMemory:
    def __init__(self, max_turns: int = 10):
        self.conversation_history: List[ConversationTurn] = []
        self.current_search_context = {}
        self.user_preferences = {}
```

### 关键方法

#### 1. rewrite_search_query()
- 使用LLM进行智能query改写
- 结合对话历史和搜索上下文
- 生成更精确的搜索查询

#### 2. _update_search_context()
- 自动提取价格、地点、品类信息
- 使用正则表达式和关键词匹配
- 累积多轮对话的搜索上下文

#### 3. get_search_context_for_rewrite()
- 获取用于query改写的完整上下文
- 合并历史查询和当前搜索上下文
- 为LLM提供充分的改写依据

## 集成方式

### 在handle_search_intent中的集成流程:
1. **Query改写**: 调用`rewrite_search_query()`进行智能改写
2. **记录对话**: 创建`ConversationTurn`并添加到memory
3. **TTS播报**: 如果有改写，播报改写后的query
4. **执行搜索**: 使用改写后的query进行商品搜索
5. **更新记录**: 将搜索结果反馈更新到对话记录

### 保持原有功能不变
- ✅ ASR语音识别功能完全保留
- ✅ TTS语音合成功能完全保留  
- ✅ 语音打断功能完全保留
- ✅ 商品卡片展示功能完全保留
- ✅ 语音控制功能完全保留

## 测试验证

### 运行测试脚本
```bash
# 基础功能测试
python test_query_rewrite.py

# 完整演示
python demo_query_rewrite.py
```

### 测试覆盖
- ✅ 对话记忆管理
- ✅ 上下文信息提取
- ✅ Query改写逻辑
- ✅ 多轮对话场景
- ✅ 意图区分处理

## 配置参数

### ConversationMemory配置
- `max_turns`: 最大保留对话轮次数 (默认: 15)
- `session_id`: 会话唯一标识符
- `current_search_context`: 当前搜索上下文字典

### Query改写配置
- 支持的价格模式: "X元以下", "X-Y元", "不超过X元"等
- 支持的地点: 主要城市和省份名称
- 支持的品类: 食品、数码、服装、家居、美妆、运动等

## 注意事项

1. **只对搜索意图改写**: 交互意图(闲聊、问候等)不进行query改写
2. **保持原查询**: 如果改写失败或结果异常，自动回退到原查询
3. **TTS播报控制**: 只在有实际改写时才播报，避免重复播报
4. **Memory限制**: 自动清理超出限制的历史记录，保持性能
5. **错误处理**: 完善的异常处理机制，确保系统稳定性

## 未来扩展

- [ ] 支持更复杂的商品属性提取(尺寸、材质等)
- [ ] 增加用户偏好学习功能
- [ ] 支持多语言query改写
- [ ] 添加改写质量评估机制
- [ ] 集成更多电商平台搜索API
