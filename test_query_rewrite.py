#!/usr/bin/env python3
"""
测试query改写功能的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import SearchIntegratedAudioHandler

async def test_query_rewrite():
    """测试query改写功能"""
    print("🧪 开始测试query改写功能")
    
    # 创建SearchIntegratedAudioHandler实例
    agent = SearchIntegratedAudioHandler()
    
    # 测试用例
    test_cases = [
        "我想买水果",
        "我要买手机", 
        "帮我找运动鞋",
        "搜索笔记本电脑",
        "看看苹果手机",
        "需要一个背包",
        "买个充电器",
        "买点零食",
        "我想买苹果",  # 歧义词汇测试
        "水果",  # 直接商品名
        "太贵了",  # 价格反馈
        # 新增隐含购买需求测试用例
        "我的雨伞丢了",  # 重点测试用例
        "手机坏了",
        "充电器没有了",
        "洗发水用完了",
        "缺少一个背包",
        "没有合适的鞋子",
        "耳机不够用",
        "钥匙不见了",
    ]
    
    print(f"📝 测试用例数量: {len(test_cases)}")
    print("=" * 60)
    
    for i, query in enumerate(test_cases, 1):
        print(f"\n🔍 测试用例 {i}: '{query}'")
        try:
            # 调用改写函数
            rewritten = await agent.rewrite_search_query(query, f"test_conv_{i}")
            
            # 输出结果
            if rewritten != query:
                print(f"✅ 改写成功: '{query}' -> '{rewritten}'")
            else:
                print(f"⚪ 无需改写: '{query}'")
                
        except Exception as e:
            print(f"❌ 改写失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🧪 测试完成")

if __name__ == "__main__":
    asyncio.run(test_query_rewrite())
