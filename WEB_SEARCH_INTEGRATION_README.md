# Web搜索集成功能说明

## 概述

已成功在app.py中集成web搜索功能，使语音助手能够回答天气、百科、新闻等需要实时信息的查询。该功能基于夸克浏览器API，使用`fetch_rag`接口获取网络信息。

## 功能特性

### 1. 智能意图识别
- **自动识别**需要web搜索的查询类型
- **支持类型**：天气、百科知识、新闻时事、旅游攻略、比较对比等
- **避免误触发**：普通问候、感谢、商品搜索等不会触发web搜索

### 2. 时间上下文增强
- **自动添加时间**：为时效性查询添加当前日期
- **智能识别**：检测"今天"、"现在"、"最新"等时间相关词汇
- **提高准确性**：确保搜索结果的时效性

### 3. 搜索结果优化
- **使用fetch_rag**：快速获取搜索结果，避免额外的LLM总结步骤
- **简洁回复**：格式化为适合语音交互的简洁回复
- **错误处理**：搜索失败时提供友好的回退处理

### 4. 语音交互优化
- **保持现有功能**：不影响ASR、TTS、语音打断等核心功能
- **流式响应**：支持流式文本输出
- **状态提示**：适当的搜索状态提示

## 代码结构

### 新增方法

1. **`should_use_web_search(transcript: str) -> bool`**
   - 判断是否需要使用web搜索
   - 基于关键词模式和问句形式识别

2. **`handle_web_search_interaction(transcript: str, conv_id: str)`**
   - 处理需要web搜索的交互意图
   - 调用搜索API并格式化结果

3. **`handle_simple_interaction(transcript: str, conv_id: str)`**
   - 处理普通交互意图（不需要web搜索）
   - 保持原有的友好对话功能

4. **`enhance_query_with_time_context(query: str) -> str`**
   - 为时效性查询添加当前时间上下文
   - 智能识别需要时间信息的查询

5. **`format_web_search_response(original_query: str, search_results: list) -> str`**
   - 格式化web搜索结果为语音回复
   - 提取关键信息，生成简洁回复

### 修改的方法

- **`handle_interaction_intent`**：重构为分发器，根据需求调用不同的处理方法

## 使用示例

### 支持的查询类型

#### 天气查询
```
用户："今天天气怎么样"
助手："根据最新信息，今天杭州多云，气温15-25度，适合出行。"
```

#### 百科知识
```
用户："什么是人工智能"
助手："人工智能是模拟人类智能的技术，主要包括机器学习、深度学习等..."
```

#### 新闻时事
```
用户："最新新闻"
助手："关于最新新闻，今天的主要新闻包括..."
```

#### 旅游攻略
```
用户："杭州有什么好玩的景点"
助手："关于杭州景点，西湖风景名胜区、西溪湿地公园等都值得一游..."
```

### 不触发web搜索的查询

- 问候语："你好"、"谢谢"、"再见"
- 商品搜索："我想买手机"（这些会走商品搜索流程）
- 纯闲聊："你真棒"、"心情不好"

## 技术实现

### 依赖模块
- `search_agent.py`：提供`fetch_rag`函数
- 夸克浏览器API：实际的搜索服务

### 关键配置
- **搜索结果数量**：使用前2-3个结果生成回复
- **回复长度限制**：控制在80字以内，适合语音交互
- **时间格式**：使用"YYYY年MM月DD日"格式

### 错误处理
- **搜索失败**：回退到友好错误提示
- **无结果**：提供"没有找到相关信息"的回复
- **格式化错误**：使用默认回复模板

## 测试验证

### 测试脚本
- `test_web_search_integration.py`：基础功能测试
- `demo_web_search_integration.py`：完整功能演示

### 测试结果
✅ 所有测试通过：
- 导入功能正常
- 搜索API调用成功
- 意图识别准确
- 时间上下文增强有效
- 结果格式化正确

## 部署说明

### 环境要求
- Python 3.8+
- 网络连接（访问夸克浏览器API）
- 现有的app.py运行环境

### 启动方式
```bash
# 直接运行app.py，web搜索功能已集成
python3 app.py
```

### 调试模式
```bash
# 运行演示脚本查看功能
python3 demo_web_search_integration.py

# 运行测试脚本验证功能
python3 test_web_search_integration.py
```

## 注意事项

1. **保持现有功能**：所有原有的语音通信、商品搜索功能保持不变
2. **网络依赖**：web搜索功能需要网络连接
3. **API限制**：注意夸克浏览器API的调用频率限制
4. **语音优化**：回复内容已优化为适合语音交互的简洁格式
5. **错误恢复**：搜索失败时会自动回退到普通交互处理

## 未来扩展

- 支持更多搜索类型（图片、视频等）
- 增加搜索结果缓存机制
- 优化搜索关键词提取
- 支持多轮对话上下文搜索

---

**集成完成时间**：2025年6月11日  
**版本**：v1.0  
**状态**：✅ 已完成并测试通过
